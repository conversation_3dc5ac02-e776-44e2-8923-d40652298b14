
/*!
 * artplayer.js v5.1.7
 * Github: https://github.com/zhw2590582/ArtPlayer
 * (c) 2017-2024 <PERSON>
 * Released under the MIT License.
 */
!function(e,t,r,n,o){var a="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{},s="function"==typeof a[n]&&a[n],i=s.cache||{},l="undefined"!=typeof module&&"function"==typeof module.require&&module.require.bind(module);function c(t,r){if(!i[t]){if(!e[t]){var o="function"==typeof a[n]&&a[n];if(!r&&o)return o(t,!0);if(s)return s(t,!0);if(l&&"string"==typeof t)return l(t);var u=Error("Cannot find module '"+t+"'");throw u.code="MODULE_NOT_FOUND",u}f.resolve=function(r){var n=e[t][1][r];return null!=n?n:r},f.cache={};var p=i[t]=new c.Module(t);e[t][0].call(p.exports,f,p,p.exports,this)}return i[t].exports;function f(e){var t=f.resolve(e);return!1===t?{}:c(t)}}c.isParcelRequire=!0,c.Module=function(e){this.id=e,this.bundle=c,this.exports={}},c.modules=e,c.cache=i,c.parent=s,c.register=function(t,r){e[t]=[function(e,t){t.exports=r},{}]},Object.defineProperty(c,"root",{get:function(){return a[n]}}),a[n]=c;for(var u=0;u<t.length;u++)c(t[u]);if(r){var p=c(r);"object"==typeof exports&&"undefined"!=typeof module?module.exports=p:"function"==typeof define&&define.amd&&define(function(){return p})}}({"7Csdv":[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return er});var o=e("@swc/helpers/_/_assert_this_initialized"),a=e("@swc/helpers/_/_class_call_check"),s=e("@swc/helpers/_/_create_class"),i=e("@swc/helpers/_/_inherits"),l=e("@swc/helpers/_/_create_super"),c=e("bundle-text:./style/index.less"),u=n.interopDefault(c),p=e("option-validator"),f=n.interopDefault(p),d=e("./utils/emitter"),h=n.interopDefault(d),m=e("./utils"),v=e("./scheme"),g=n.interopDefault(v),_=e("./config"),y=n.interopDefault(_),b=e("./template"),w=n.interopDefault(b),x=e("./i18n"),j=n.interopDefault(x),k=e("./player"),T=n.interopDefault(k),S=e("./control"),M=n.interopDefault(S),I=e("./contextmenu"),E=n.interopDefault(I),F=e("./info"),C=n.interopDefault(F),R=e("./subtitle"),O=n.interopDefault(R),P=e("./events"),D=n.interopDefault(P),A=e("./hotkey"),z=n.interopDefault(A),V=e("./layer"),$=n.interopDefault(V),L=e("./loading"),q=n.interopDefault(L),H=e("./notice"),N=n.interopDefault(H),W=e("./mask"),B=n.interopDefault(W),U=e("./icons"),Y=n.interopDefault(U),K=e("./setting"),X=n.interopDefault(K),G=e("./storage"),J=n.interopDefault(G),Z=e("./plugins"),Q=n.interopDefault(Z),ee=0,et=[],er=function(e){(0,i._)(r,e);var t=(0,l._)(r);function r(e,n){(0,a._)(this,r),(s=t.call(this)).id=++ee;var s,i=m.mergeDeep(r.option,e);if(i.container=e.container,s.option=(0,f.default)(i,g.default),s.isLock=!1,s.isReady=!1,s.isFocus=!1,s.isInput=!1,s.isRotate=!1,s.isDestroy=!1,s.template=new w.default((0,o._)(s)),s.events=new D.default((0,o._)(s)),s.storage=new J.default((0,o._)(s)),s.icons=new Y.default((0,o._)(s)),s.i18n=new j.default((0,o._)(s)),s.notice=new N.default((0,o._)(s)),s.player=new T.default((0,o._)(s)),s.layers=new $.default((0,o._)(s)),s.controls=new M.default((0,o._)(s)),s.contextmenu=new E.default((0,o._)(s)),s.subtitle=new O.default((0,o._)(s)),s.info=new C.default((0,o._)(s)),s.loading=new q.default((0,o._)(s)),s.hotkey=new z.default((0,o._)(s)),s.mask=new B.default((0,o._)(s)),s.setting=new X.default((0,o._)(s)),s.plugins=new Q.default((0,o._)(s)),"function"==typeof n&&s.on("ready",function(){return n.call((0,o._)(s),(0,o._)(s))}),r.DEBUG){var l=function(e){return console.log("[ART.".concat(s.id,"] ->").concat(e))};l("Version@"+r.version),l("Env@"+r.env),l("Build@"+r.build);for(var c=0;c<y.default.events.length;c++)s.on("video:"+y.default.events[c],function(e){return l("Event@"+e.type)})}return et.push((0,o._)(s)),s}return(0,s._)(r,[{key:"proxy",get:function(){return this.events.proxy}},{key:"query",get:function(){return this.template.query}},{key:"video",get:function(){return this.template.$video}},{key:"destroy",value:function(){var e=!(arguments.length>0)||void 0===arguments[0]||arguments[0];this.events.destroy(),this.template.destroy(e),et.splice(et.indexOf(this),1),this.isDestroy=!0,this.emit("destroy")}}],[{key:"instances",get:function(){return et}},{key:"version",get:function(){return"5.1.7"}},{key:"env",get:function(){return"production"}},{key:"build",get:function(){return"2024-08-15 23:27:09"}},{key:"config",get:function(){return y.default}},{key:"utils",get:function(){return m}},{key:"scheme",get:function(){return g.default}},{key:"Emitter",get:function(){return h.default}},{key:"validator",get:function(){return f.default}},{key:"kindOf",get:function(){return f.default.kindOf}},{key:"html",get:function(){return w.default.html}},{key:"option",get:function(){return{id:"",container:"#artplayer",url:"",poster:"",type:"",theme:"#f00",volume:.7,isLive:!1,muted:!1,autoplay:!1,autoSize:!1,autoMini:!1,loop:!1,flip:!1,playbackRate:!1,aspectRatio:!1,screenshot:!1,setting:!1,hotkey:!0,pip:!1,mutex:!0,backdrop:!0,fullscreen:!1,fullscreenWeb:!1,subtitleOffset:!1,miniProgressBar:!1,useSSR:!1,playsInline:!0,lock:!1,fastForward:!1,autoPlayback:!1,autoOrientation:!1,airplay:!1,layers:[],contextmenu:[],controls:[],settings:[],quality:[],highlight:[],plugins:[],thumbnails:{url:"",number:60,column:10,width:0,height:0},subtitle:{url:"",type:"",style:{},name:"",escape:!0,encoding:"utf-8",onVttLoad:function(e){return e}},moreVideoAttr:{controls:!1,preload:m.isSafari?"auto":"metadata"},i18n:{},icons:{},cssVar:{},customType:{},lang:navigator.language.toLowerCase()}}}]),r}(h.default);er.STYLE=u.default,er.DEBUG=!1,er.CONTEXTMENU=!0,er.NOTICE_TIME=2e3,er.SETTING_WIDTH=250,er.SETTING_ITEM_WIDTH=200,er.SETTING_ITEM_HEIGHT=35,er.RESIZE_TIME=200,er.SCROLL_TIME=200,er.SCROLL_GAP=50,er.AUTO_PLAYBACK_MAX=10,er.AUTO_PLAYBACK_MIN=5,er.AUTO_PLAYBACK_TIMEOUT=3e3,er.RECONNECT_TIME_MAX=5,er.RECONNECT_SLEEP_TIME=1e3,er.CONTROL_HIDE_TIME=3e3,er.DBCLICK_TIME=300,er.DBCLICK_FULLSCREEN=!0,er.MOBILE_DBCLICK_PLAY=!0,er.MOBILE_CLICK_PLAY=!1,er.AUTO_ORIENTATION_TIME=200,er.INFO_LOOP_TIME=1e3,er.FAST_FORWARD_VALUE=3,er.FAST_FORWARD_TIME=1e3,er.TOUCH_MOVE_RATIO=.5,er.VOLUME_STEP=.1,er.SEEK_STEP=5,er.PLAYBACK_RATE=[.5,.75,1,1.25,1.5,2],er.ASPECT_RATIO=["default","4:3","16:9"],er.FLIP=["normal","horizontal","vertical"],er.FULLSCREEN_WEB_IN_BODY=!1,er.LOG_VERSION=!0,er.USE_RAF=!1,m.isBrowser&&(window.Artplayer=er,m.setStyleText("artplayer-style",u.default),setTimeout(function(){er.LOG_VERSION&&console.log("%c ArtPlayer %c ".concat(er.version," %c https://artplayer.org"),"color: #fff; background: #5f5f5f","color: #fff; background: #4bc729","")},100))},{"@swc/helpers/_/_assert_this_initialized":"jTlDX","@swc/helpers/_/_class_call_check":"dRqgV","@swc/helpers/_/_create_class":"8RAzW","@swc/helpers/_/_inherits":"aRPJQ","@swc/helpers/_/_create_super":"kqTtK","bundle-text:./style/index.less":"7Efch","option-validator":"gEtej","./utils/emitter":"23yIZ","./utils":"7esST","./scheme":"hYXnH","./config":"1lpcp","./template":"aRbVP","./i18n":"d1hXD","./player":"aUXOX","./control":"7Cphn","./contextmenu":"aH04y","./info":"kEevD","./subtitle":"14H9c","./events":"9biZS","./hotkey":"bCoXC","./layer":"eQf6l","./loading":"gzHl5","./notice":"gdkSy","./mask":"kH8gp","./icons":"1Mh7c","./setting":"2lyD4","./storage":"38YEE","./plugins":"b3Yxu","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],jTlDX:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");function o(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}n.defineInteropFlag(r),n.export(r,"_assert_this_initialized",function(){return o}),n.export(r,"_",function(){return o})},{"@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],hTt7M:[function(e,t,r){r.interopDefault=function(e){return e&&e.__esModule?e:{default:e}},r.defineInteropFlag=function(e){Object.defineProperty(e,"__esModule",{value:!0})},r.exportAll=function(e,t){return Object.keys(e).forEach(function(r){"default"===r||"__esModule"===r||Object.prototype.hasOwnProperty.call(t,r)||Object.defineProperty(t,r,{enumerable:!0,get:function(){return e[r]}})}),t},r.export=function(e,t,r){Object.defineProperty(e,t,{enumerable:!0,get:r})}},{}],dRqgV:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");function o(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}n.defineInteropFlag(r),n.export(r,"_class_call_check",function(){return o}),n.export(r,"_",function(){return o})},{"@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],"8RAzW":[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");function o(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function a(e,t,r){return t&&o(e.prototype,t),r&&o(e,r),e}n.defineInteropFlag(r),n.export(r,"_create_class",function(){return a}),n.export(r,"_",function(){return a})},{"@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],aRPJQ:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"_inherits",function(){return a}),n.export(r,"_",function(){return a});var o=e("./_set_prototype_of.js");function a(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&(0,o._set_prototype_of)(e,t)}},{"./_set_prototype_of.js":"eAgjA","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],eAgjA:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");function o(e,t){return(o=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}n.defineInteropFlag(r),n.export(r,"_set_prototype_of",function(){return o}),n.export(r,"_",function(){return o})},{"@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],kqTtK:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"_create_super",function(){return i}),n.export(r,"_",function(){return i});var o=e("./_get_prototype_of.js"),a=e("./_is_native_reflect_construct.js"),s=e("./_possible_constructor_return.js");function i(e){var t=(0,a._is_native_reflect_construct)();return function(){var r,n=(0,o._get_prototype_of)(e);return r=t?Reflect.construct(n,arguments,(0,o._get_prototype_of)(this).constructor):n.apply(this,arguments),(0,s._possible_constructor_return)(this,r)}}},{"./_get_prototype_of.js":"2mh6E","./_is_native_reflect_construct.js":"M5rXK","./_possible_constructor_return.js":"dp6P3","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],"2mh6E":[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");function o(e){return(o=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}n.defineInteropFlag(r),n.export(r,"_get_prototype_of",function(){return o}),n.export(r,"_",function(){return o})},{"@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],M5rXK:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");function o(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}n.defineInteropFlag(r),n.export(r,"_is_native_reflect_construct",function(){return o}),n.export(r,"_",function(){return o})},{"@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],dp6P3:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"_possible_constructor_return",function(){return s}),n.export(r,"_",function(){return s});var o=e("./_assert_this_initialized.js"),a=e("./_type_of.js");function s(e,t){return t&&("object"===(0,a._type_of)(t)||"function"==typeof t)?t:(0,o._assert_this_initialized)(e)}},{"./_assert_this_initialized.js":"jTlDX","./_type_of.js":"l4kpM","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],l4kpM:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");function o(e){return e&&"undefined"!=typeof Symbol&&e.constructor===Symbol?"symbol":typeof e}n.defineInteropFlag(r),n.export(r,"_type_of",function(){return o}),n.export(r,"_",function(){return o})},{"@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],"7Efch":[function(e,t,r){t.exports='.art-video-player{--art-theme:red;--art-font-color:#fff;--art-background-color:#000;--art-text-shadow-color:rgba(0,0,0,.5);--art-transition-duration:.2s;--art-padding:10px;--art-border-radius:3px;--art-progress-height:6px;--art-progress-color:rgba(255,255,255,.25);--art-hover-color:rgba(255,255,255,.25);--art-loaded-color:rgba(255,255,255,.25);--art-state-size:80px;--art-state-opacity:.8;--art-bottom-height:100px;--art-bottom-offset:20px;--art-bottom-gap:5px;--art-highlight-width:8px;--art-highlight-color:rgba(255,255,255,.5);--art-control-height:46px;--art-control-opacity:.75;--art-control-icon-size:36px;--art-control-icon-scale:1.1;--art-volume-height:120px;--art-volume-handle-size:14px;--art-lock-size:36px;--art-indicator-scale:0;--art-indicator-size:16px;--art-fullscreen-web-index:9999;--art-settings-icon-size:24px;--art-settings-max-height:300px;--art-selector-max-height:300px;--art-contextmenus-min-width:250px;--art-subtitle-font-size:20px;--art-subtitle-gap:5px;--art-subtitle-bottom:15px;--art-subtitle-border:#000;--art-widget-background:rgba(0,0,0,.85);--art-tip-background:rgba(0,0,0,.7);--art-scrollbar-size:4px;--art-scrollbar-background:rgba(255,255,255,.25);--art-scrollbar-background-hover:rgba(255,255,255,.5);--art-mini-progress-height:2px}.art-bg-cover{background-position:50%;background-repeat:no-repeat;background-size:cover}.art-bottom-gradient{background-image:linear-gradient(transparent,rgba(0,0,0,.4),#000);background-position:bottom;background-repeat:repeat-x}.art-backdrop-filter{backdrop-filter:saturate(180%)blur(20px);background-color:rgba(0,0,0,.75)!important}.art-truncate{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.art-video-player{zoom:1;text-align:left;-ms-user-select:none;user-select:none;box-sizing:border-box;color:var(--art-font-color);background-color:var(--art-background-color);text-shadow:0 0 2px var(--art-text-shadow-color);-webkit-tap-highlight-color:transparent;-ms-touch-action:manipulation;touch-action:manipulation;-ms-high-contrast-adjust:none;direction:ltr;outline:0;width:100%;height:100%;margin:0 auto;padding:0;font-family:PingFang SC,Helvetica Neue,Microsoft YaHei,Roboto,Arial,sans-serif;font-size:14px;line-height:1.3;position:relative}.art-video-player *,.art-video-player :before,.art-video-player :after{box-sizing:border-box}.art-video-player ::-webkit-scrollbar{width:var(--art-scrollbar-size);height:var(--art-scrollbar-size)}.art-video-player ::-webkit-scrollbar-thumb{background-color:var(--art-scrollbar-background)}.art-video-player ::-webkit-scrollbar-thumb:hover{background-color:var(--art-scrollbar-background-hover)}.art-video-player img{vertical-align:top;max-width:100%}.art-video-player svg{fill:var(--art-font-color)}.art-video-player a{color:var(--art-font-color);text-decoration:none}.art-icon{justify-content:center;align-items:center;line-height:1;display:flex}.art-video-player.art-backdrop .art-contextmenus,.art-video-player.art-backdrop .art-info,.art-video-player.art-backdrop .art-settings,.art-video-player.art-backdrop .art-layer-auto-playback,.art-video-player.art-backdrop .art-selector-list,.art-video-player.art-backdrop .art-volume-inner{backdrop-filter:saturate(180%)blur(20px);background-color:rgba(0,0,0,.75)!important}.art-video{z-index:10;cursor:pointer;width:100%;height:100%;position:absolute;top:0;bottom:0;left:0;right:0}.art-poster{z-index:11;pointer-events:none;background-position:50%;background-repeat:no-repeat;background-size:cover;width:100%;height:100%;position:absolute;top:0;bottom:0;left:0;right:0}.art-video-player .art-subtitle{z-index:20;text-align:center;pointer-events:none;justify-content:center;align-items:center;gap:var(--art-subtitle-gap);bottom:var(--art-subtitle-bottom);font-size:var(--art-subtitle-font-size);transition:bottom var(--art-transition-duration)ease;text-shadow:var(--art-subtitle-border)1px 0 1px,var(--art-subtitle-border)0 1px 1px,var(--art-subtitle-border)-1px 0 1px,var(--art-subtitle-border)0 -1px 1px,var(--art-subtitle-border)1px 1px 1px,var(--art-subtitle-border)-1px -1px 1px,var(--art-subtitle-border)1px -1px 1px,var(--art-subtitle-border)-1px 1px 1px;flex-direction:column;width:100%;padding:0 5%;display:none;position:absolute}.art-video-player.art-subtitle-show .art-subtitle{display:flex}.art-video-player.art-control-show .art-subtitle{bottom:calc(var(--art-control-height) + var(--art-subtitle-bottom))}.art-danmuku{z-index:30;pointer-events:none;width:100%;height:100%;position:absolute;top:0;bottom:0;left:0;right:0;overflow:hidden}.art-video-player .art-layers{z-index:40;pointer-events:none;width:100%;height:100%;display:none;position:absolute;top:0;bottom:0;left:0;right:0}.art-video-player .art-layers .art-layer{pointer-events:auto}.art-video-player.art-layer-show .art-layers{display:flex}.art-video-player .art-mask{z-index:50;pointer-events:none;justify-content:center;align-items:center;width:100%;height:100%;display:flex;position:absolute;top:0;bottom:0;left:0;right:0}.art-video-player .art-mask .art-state{opacity:0;width:var(--art-state-size);height:var(--art-state-size);transition:all var(--art-transition-duration)ease;justify-content:center;align-items:center;display:flex;transform:scale(2)}.art-video-player.art-mask-show .art-state{cursor:pointer;pointer-events:auto;opacity:var(--art-state-opacity);transform:scale(1)}.art-video-player.art-loading-show .art-state{display:none}.art-video-player .art-loading{z-index:70;pointer-events:none;justify-content:center;align-items:center;width:100%;height:100%;display:none;position:absolute;top:0;bottom:0;left:0;right:0}.art-video-player.art-loading-show .art-loading{display:flex}.art-video-player .art-bottom{z-index:60;opacity:0;pointer-events:none;padding:0 var(--art-padding);transition:all var(--art-transition-duration)ease;background-size:100% var(--art-bottom-height);background-image:linear-gradient(transparent,rgba(0,0,0,.4),#000);background-position:bottom;background-repeat:repeat-x;flex-direction:column;justify-content:flex-end;width:100%;height:100%;display:flex;position:absolute;top:0;bottom:0;left:0;right:0;overflow:hidden}.art-video-player .art-bottom .art-controls,.art-video-player .art-bottom .art-progress{transform:translateY(var(--art-bottom-offset));transition:transform var(--art-transition-duration)ease}.art-video-player.art-control-show .art-bottom,.art-video-player.art-hover .art-bottom{opacity:1}.art-video-player.art-control-show .art-bottom .art-controls,.art-video-player.art-hover .art-bottom .art-controls,.art-video-player.art-control-show .art-bottom .art-progress,.art-video-player.art-hover .art-bottom .art-progress{transform:translateY(0)}.art-bottom .art-progress{z-index:0;pointer-events:auto;padding-bottom:var(--art-bottom-gap);position:relative}.art-bottom .art-progress .art-control-progress{cursor:pointer;height:var(--art-progress-height);justify-content:center;align-items:center;display:flex;position:relative}.art-bottom .art-progress .art-control-progress .art-control-progress-inner{transition:height var(--art-transition-duration)ease;background-color:var(--art-progress-color);align-items:center;width:100%;height:50%;display:flex;position:relative}.art-bottom .art-progress .art-control-progress .art-control-progress-inner .art-progress-hover{z-index:0;background-color:var(--art-hover-color);width:0%;height:100%;position:absolute;top:0;bottom:0;left:0;right:0}.art-bottom .art-progress .art-control-progress .art-control-progress-inner .art-progress-loaded{z-index:10;background-color:var(--art-loaded-color);width:0%;height:100%;position:absolute;top:0;bottom:0;left:0;right:0}.art-bottom .art-progress .art-control-progress .art-control-progress-inner .art-progress-played{z-index:20;background-color:var(--art-theme);width:0%;height:100%;position:absolute;top:0;bottom:0;left:0;right:0}.art-bottom .art-progress .art-control-progress .art-control-progress-inner .art-progress-highlight{z-index:30;pointer-events:none;width:100%;height:100%;position:absolute;top:0;bottom:0;left:0;right:0}.art-bottom .art-progress .art-control-progress .art-control-progress-inner .art-progress-highlight span{z-index:0;pointer-events:auto;transform:translateX(calc(var(--art-highlight-width)/-2));background-color:var(--art-highlight-color);width:100%;height:100%;position:absolute;top:0;bottom:0;left:0;right:auto;width:var(--art-highlight-width)!important}.art-bottom .art-progress .art-control-progress .art-control-progress-inner .art-progress-indicator{z-index:40;width:var(--art-indicator-size);height:var(--art-indicator-size);transform:scale(var(--art-indicator-scale));margin-left:calc(var(--art-indicator-size)/-2);transition:transform var(--art-transition-duration)ease;border-radius:50%;justify-content:center;align-items:center;display:flex;position:absolute;left:0}.art-bottom .art-progress .art-control-progress .art-control-progress-inner .art-progress-indicator .art-icon{pointer-events:none;width:100%;height:100%}.art-bottom .art-progress .art-control-progress .art-control-progress-inner .art-progress-indicator:hover{transform:scale(1.2)!important}.art-bottom .art-progress .art-control-progress .art-control-progress-inner .art-progress-indicator:active{transform:scale(1)!important}.art-bottom .art-progress .art-control-progress .art-control-progress-inner .art-progress-tip{z-index:50;border-radius:var(--art-border-radius);white-space:nowrap;background-color:var(--art-tip-background);padding:3px 5px;font-size:12px;line-height:1;display:none;position:absolute;top:-25px;left:0}.art-bottom .art-progress .art-control-progress:hover .art-control-progress-inner{height:100%}.art-bottom .art-progress .art-control-thumbnails{bottom:calc(var(--art-bottom-gap) + 10px);border-radius:var(--art-border-radius);pointer-events:none;background-color:var(--art-widget-background);display:none;position:absolute;left:0;box-shadow:0 1px 3px rgba(0,0,0,.2),0 1px 2px -1px rgba(0,0,0,.2)}.art-bottom:hover .art-progress .art-control-progress .art-control-progress-inner .art-progress-indicator{transform:scale(1)}.art-controls{z-index:10;pointer-events:auto;height:var(--art-control-height);justify-content:space-between;align-items:center;display:flex;position:relative}.art-controls .art-controls-left,.art-controls .art-controls-right{height:100%;display:flex}.art-controls .art-controls-center{flex:1;justify-content:center;align-items:center;height:100%;padding:0 10px;display:none}.art-controls .art-controls-right{justify-content:flex-end}.art-controls .art-control{cursor:pointer;white-space:nowrap;opacity:var(--art-control-opacity);min-height:var(--art-control-height);min-width:var(--art-control-height);transition:opacity var(--art-transition-duration)ease;flex-shrink:0;justify-content:center;align-items:center;display:flex}.art-controls .art-control .art-icon{height:var(--art-control-icon-size);width:var(--art-control-icon-size);transform:scale(var(--art-control-icon-scale));transition:transform var(--art-transition-duration)ease}.art-controls .art-control .art-icon:active{transform:scale(calc(var(--art-control-icon-scale)*.8))}.art-controls .art-control:hover{opacity:1}.art-control-volume{position:relative}.art-control-volume .art-volume-panel{text-align:center;cursor:default;opacity:0;pointer-events:none;left:0;right:0;bottom:var(--art-control-height);width:var(--art-control-height);height:var(--art-volume-height);transition:all var(--art-transition-duration)ease;justify-content:center;align-items:center;padding:0 5px;font-size:12px;display:flex;position:absolute;transform:translateY(10px)}.art-control-volume .art-volume-panel .art-volume-inner{border-radius:var(--art-border-radius);background-color:var(--art-widget-background);flex-direction:column;align-items:center;gap:10px;width:100%;height:100%;padding:10px 0 12px;display:flex}.art-control-volume .art-volume-panel .art-volume-inner .art-volume-slider{cursor:pointer;flex:1;justify-content:center;width:100%;display:flex;position:relative}.art-control-volume .art-volume-panel .art-volume-inner .art-volume-slider .art-volume-handle{border-radius:var(--art-border-radius);background-color:rgba(255,255,255,.25);justify-content:center;width:2px;display:flex;position:relative;overflow:hidden}.art-control-volume .art-volume-panel .art-volume-inner .art-volume-slider .art-volume-handle .art-volume-loaded{z-index:0;background-color:var(--art-theme);width:100%;height:100%;position:absolute;top:0;bottom:0;left:0;right:0}.art-control-volume .art-volume-panel .art-volume-inner .art-volume-slider .art-volume-indicator{width:var(--art-volume-handle-size);height:var(--art-volume-handle-size);margin-top:calc(var(--art-volume-handle-size)/-2);background-color:var(--art-theme);transition:transform var(--art-transition-duration)ease;border-radius:100%;flex-shrink:0;position:absolute;transform:scale(1)}.art-control-volume .art-volume-panel .art-volume-inner .art-volume-slider:active .art-volume-indicator{transform:scale(.9)}.art-control-volume:hover .art-volume-panel{opacity:1;pointer-events:auto;transform:translateY(0)}.art-video-player .art-notice{z-index:80;padding:var(--art-padding);pointer-events:none;width:100%;height:auto;display:none;position:absolute;top:0;bottom:auto;left:0;right:0}.art-video-player .art-notice .art-notice-inner{border-radius:var(--art-border-radius);background-color:var(--art-tip-background);padding:5px;line-height:1;display:inline-flex}.art-video-player.art-notice-show .art-notice{display:flex}.art-video-player .art-contextmenus{z-index:120;border-radius:var(--art-border-radius);background-color:var(--art-widget-background);min-width:var(--art-contextmenus-min-width);flex-direction:column;padding:5px 0;font-size:12px;display:none;position:absolute}.art-video-player .art-contextmenus .art-contextmenu{cursor:pointer;border-bottom:1px solid rgba(255,255,255,.1);padding:10px 15px;display:flex}.art-video-player .art-contextmenus .art-contextmenu span{padding:0 8px}.art-video-player .art-contextmenus .art-contextmenu span:hover,.art-video-player .art-contextmenus .art-contextmenu span.art-current{color:var(--art-theme)}.art-video-player .art-contextmenus .art-contextmenu:hover{background-color:rgba(255,255,255,.1)}.art-video-player .art-contextmenus .art-contextmenu:last-child{border-bottom:none}.art-video-player.art-contextmenu-show .art-contextmenus{display:flex}.art-video-player .art-settings{z-index:90;border-radius:var(--art-border-radius);transform-origin:100% 100%;max-height:var(--art-settings-max-height);left:auto;right:var(--art-padding);bottom:var(--art-control-height);transform:scale(var(--art-settings-scale));transition:all var(--art-transition-duration)ease;background-color:var(--art-widget-background);flex-direction:column;display:none;position:absolute;overflow-x:hidden;overflow-y:auto}.art-video-player .art-settings .art-setting-panel{flex-direction:column;display:none}.art-video-player .art-settings .art-setting-panel.art-current{display:flex}.art-video-player .art-settings .art-setting-panel .art-setting-item{cursor:pointer;transition:background-color var(--art-transition-duration)ease;justify-content:space-between;align-items:center;padding:0 5px;display:flex;overflow:hidden}.art-video-player .art-settings .art-setting-panel .art-setting-item:hover{background-color:rgba(255,255,255,.1)}.art-video-player .art-settings .art-setting-panel .art-setting-item.art-current{color:var(--art-theme)}.art-video-player .art-settings .art-setting-panel .art-setting-item .art-icon-check{visibility:hidden;height:15px}.art-video-player .art-settings .art-setting-panel .art-setting-item.art-current .art-icon-check{visibility:visible}.art-video-player .art-settings .art-setting-panel .art-setting-item .art-setting-item-left{justify-content:center;align-items:center;gap:5px;display:flex}.art-video-player .art-settings .art-setting-panel .art-setting-item .art-setting-item-left .art-setting-item-left-icon{height:var(--art-settings-icon-size);width:var(--art-settings-icon-size);justify-content:center;align-items:center;display:flex}.art-video-player .art-settings .art-setting-panel .art-setting-item .art-setting-item-right{justify-content:center;align-items:center;gap:5px;font-size:12px;display:flex}.art-video-player .art-settings .art-setting-panel .art-setting-item .art-setting-item-right .art-setting-item-right-tooltip{white-space:nowrap;color:rgba(255,255,255,.5)}.art-video-player .art-settings .art-setting-panel .art-setting-item .art-setting-item-right .art-setting-item-right-icon{justify-content:center;align-items:center;min-width:32px;height:24px;display:flex}.art-video-player .art-settings .art-setting-panel .art-setting-item .art-setting-item-right .art-setting-range{-ms-appearance:none;appearance:none;background-color:rgba(255,255,255,.2);outline:none;width:80px;height:3px}.art-video-player .art-settings .art-setting-panel .art-setting-item-back{border-bottom:1px solid rgba(255,255,255,.1)}.art-video-player.art-setting-show .art-settings{display:flex}.art-video-player .art-info{left:var(--art-padding);top:var(--art-padding);z-index:100;border-radius:var(--art-border-radius);background-color:var(--art-widget-background);padding:10px;font-size:12px;display:none;position:absolute}.art-video-player .art-info .art-info-panel{flex-direction:column;gap:5px;display:flex}.art-video-player .art-info .art-info-panel .art-info-item{align-items:center;gap:5px;display:flex}.art-video-player .art-info .art-info-panel .art-info-item .art-info-title{text-align:right;width:100px}.art-video-player .art-info .art-info-panel .art-info-item .art-info-content{text-overflow:ellipsis;white-space:nowrap;-ms-user-select:all;user-select:all;width:250px;overflow:hidden}.art-video-player .art-info .art-info-close{cursor:pointer;position:absolute;top:5px;right:5px}.art-video-player.art-info-show .art-info{display:flex}.art-hide-cursor *{cursor:none!important}.art-video-player[data-aspect-ratio]{overflow:hidden}.art-video-player[data-aspect-ratio] .art-video{object-fit:fill;box-sizing:content-box}.art-fullscreen{--art-progress-height:8px;--art-indicator-size:20px;--art-control-height:60px;--art-control-icon-scale:1.3}.art-fullscreen-web{--art-progress-height:8px;--art-indicator-size:20px;--art-control-height:60px;--art-control-icon-scale:1.3;z-index:var(--art-fullscreen-web-index);width:100%;height:100%;position:fixed;top:0;bottom:0;left:0;right:0}.art-mini-popup{z-index:9999;border-radius:var(--art-border-radius);cursor:move;-ms-user-select:none;user-select:none;background:#000;width:320px;height:180px;transition:opacity .2s;position:fixed;overflow:hidden;box-shadow:0 0 5px rgba(0,0,0,.5)}.art-mini-popup svg{fill:#fff}.art-mini-popup .art-video{pointer-events:none}.art-mini-popup .art-mini-close{z-index:20;cursor:pointer;opacity:0;transition:opacity .2s;position:absolute;top:10px;right:10px}.art-mini-popup .art-mini-state{z-index:30;pointer-events:none;opacity:0;background-color:rgba(0,0,0,.25);justify-content:center;align-items:center;width:100%;height:100%;transition:opacity .2s;display:flex;position:absolute;top:0;bottom:0;left:0;right:0}.art-mini-popup .art-mini-state .art-icon{opacity:.75;cursor:pointer;pointer-events:auto;transition:transform .2s;transform:scale(3)}.art-mini-popup .art-mini-state .art-icon:active{transform:scale(2.5)}.art-mini-popup.art-mini-droging{opacity:.9}.art-mini-popup:hover .art-mini-close,.art-mini-popup:hover .art-mini-state{opacity:1}.art-video-player[data-flip=horizontal] .art-video{transform:scaleX(-1)}.art-video-player[data-flip=vertical] .art-video{transform:scaleY(-1)}.art-video-player .art-layer-lock{height:var(--art-lock-size);width:var(--art-lock-size);top:50%;left:var(--art-padding);background-color:var(--art-tip-background);border-radius:50%;justify-content:center;align-items:center;display:none;position:absolute;transform:translateY(-50%)}.art-video-player .art-layer-auto-playback{border-radius:var(--art-border-radius);left:var(--art-padding);bottom:calc(var(--art-control-height) + var(--art-bottom-gap) + 10px);background-color:var(--art-widget-background);align-items:center;gap:10px;padding:10px;line-height:1;display:none;position:absolute}.art-video-player .art-layer-auto-playback .art-auto-playback-close{cursor:pointer;justify-content:center;align-items:center;display:flex}.art-video-player .art-layer-auto-playback .art-auto-playback-close svg{fill:var(--art-theme);width:15px;height:15px}.art-video-player .art-layer-auto-playback .art-auto-playback-jump{color:var(--art-theme);cursor:pointer}.art-video-player.art-lock .art-subtitle{bottom:var(--art-subtitle-bottom)!important}.art-video-player.art-mini-progress-bar .art-bottom,.art-video-player.art-lock .art-bottom{opacity:1;background-image:none;padding:0}.art-video-player.art-mini-progress-bar .art-bottom .art-controls,.art-video-player.art-lock .art-bottom .art-controls,.art-video-player.art-mini-progress-bar .art-bottom .art-progress,.art-video-player.art-lock .art-bottom .art-progress{transform:translateY(calc(var(--art-control-height) + var(--art-bottom-gap) + var(--art-progress-height)/4))}.art-video-player.art-mini-progress-bar .art-bottom .art-progress-indicator,.art-video-player.art-lock .art-bottom .art-progress-indicator{display:none!important}.art-video-player.art-control-show .art-layer-lock{display:flex}.art-control-selector{position:relative}.art-control-selector .art-selector-list{text-align:center;border-radius:var(--art-border-radius);opacity:0;pointer-events:none;bottom:var(--art-control-height);max-height:var(--art-selector-max-height);background-color:var(--art-widget-background);transition:all var(--art-transition-duration)ease;flex-direction:column;align-items:center;display:flex;position:absolute;overflow-x:hidden;overflow-y:auto;transform:translateY(10px)}.art-control-selector .art-selector-list .art-selector-item{flex-shrink:0;justify-content:center;align-items:center;width:100%;padding:10px 15px;line-height:1;display:flex}.art-control-selector .art-selector-list .art-selector-item:hover{background-color:rgba(255,255,255,.1)}.art-control-selector .art-selector-list .art-selector-item:hover,.art-control-selector .art-selector-list .art-selector-item.art-current{color:var(--art-theme)}.art-control-selector:hover .art-selector-list{opacity:1;pointer-events:auto;transform:translateY(0)}[class*=hint--]{font-style:normal;display:inline-block;position:relative}[class*=hint--]:before,[class*=hint--]:after{visibility:hidden;opacity:0;z-index:1000000;pointer-events:none;transition:all .3s;position:absolute;transform:translate(0,0)}[class*=hint--]:hover:before,[class*=hint--]:hover:after{visibility:visible;opacity:1;transition-delay:.1s}[class*=hint--]:before{content:"";z-index:1000001;background:0 0;border:6px solid transparent;position:absolute}[class*=hint--]:after{color:#fff;white-space:nowrap;background:#000;padding:8px 10px;font-family:Helvetica Neue,Helvetica,Arial,sans-serif;font-size:12px;line-height:12px}[class*=hint--][aria-label]:after{content:attr(aria-label)}[class*=hint--][data-hint]:after{content:attr(data-hint)}[aria-label=""]:before,[aria-label=""]:after,[data-hint=""]:before,[data-hint=""]:after{display:none!important}.hint--top-left:before,.hint--top-right:before,.hint--top:before{border-top-color:#000}.hint--bottom-left:before,.hint--bottom-right:before,.hint--bottom:before{border-bottom-color:#000}.hint--left:before{border-left-color:#000}.hint--right:before{border-right-color:#000}.hint--top:before{margin-bottom:-11px}.hint--top:before,.hint--top:after{bottom:100%;left:50%}.hint--top:before{left:calc(50% - 6px)}.hint--top:after{transform:translate(-50%)}.hint--top:hover:before{transform:translateY(-8px)}.hint--top:hover:after{transform:translate(-50%)translateY(-8px)}.hint--bottom:before{margin-top:-11px}.hint--bottom:before,.hint--bottom:after{top:100%;left:50%}.hint--bottom:before{left:calc(50% - 6px)}.hint--bottom:after{transform:translate(-50%)}.hint--bottom:hover:before{transform:translateY(8px)}.hint--bottom:hover:after{transform:translate(-50%)translateY(8px)}.hint--right:before{margin-bottom:-6px;margin-left:-11px}.hint--right:after{margin-bottom:-14px}.hint--right:before,.hint--right:after{bottom:50%;left:100%}.hint--right:hover:before,.hint--right:hover:after{transform:translate(8px)}.hint--left:before{margin-bottom:-6px;margin-right:-11px}.hint--left:after{margin-bottom:-14px}.hint--left:before,.hint--left:after{bottom:50%;right:100%}.hint--left:hover:before,.hint--left:hover:after{transform:translate(-8px)}.hint--top-left:before{margin-bottom:-11px}.hint--top-left:before,.hint--top-left:after{bottom:100%;left:50%}.hint--top-left:before{left:calc(50% - 6px)}.hint--top-left:after{margin-left:12px;transform:translate(-100%)}.hint--top-left:hover:before{transform:translateY(-8px)}.hint--top-left:hover:after{transform:translate(-100%)translateY(-8px)}.hint--top-right:before{margin-bottom:-11px}.hint--top-right:before,.hint--top-right:after{bottom:100%;left:50%}.hint--top-right:before{left:calc(50% - 6px)}.hint--top-right:after{margin-left:-12px;transform:translate(0)}.hint--top-right:hover:before,.hint--top-right:hover:after{transform:translateY(-8px)}.hint--bottom-left:before{margin-top:-11px}.hint--bottom-left:before,.hint--bottom-left:after{top:100%;left:50%}.hint--bottom-left:before{left:calc(50% - 6px)}.hint--bottom-left:after{margin-left:12px;transform:translate(-100%)}.hint--bottom-left:hover:before{transform:translateY(8px)}.hint--bottom-left:hover:after{transform:translate(-100%)translateY(8px)}.hint--bottom-right:before{margin-top:-11px}.hint--bottom-right:before,.hint--bottom-right:after{top:100%;left:50%}.hint--bottom-right:before{left:calc(50% - 6px)}.hint--bottom-right:after{margin-left:-12px;transform:translate(0)}.hint--bottom-right:hover:before,.hint--bottom-right:hover:after{transform:translateY(8px)}.hint--small:after,.hint--medium:after,.hint--large:after{white-space:normal;word-wrap:break-word;line-height:1.4em}.hint--small:after{width:80px}.hint--medium:after{width:150px}.hint--large:after{width:300px}[class*=hint--]:after{text-shadow:0 -1px #000;box-shadow:4px 4px 8px rgba(0,0,0,.3)}.hint--error:after{text-shadow:0 -1px #592726;background-color:#b34e4d}.hint--error.hint--top-left:before,.hint--error.hint--top-right:before,.hint--error.hint--top:before{border-top-color:#b34e4d}.hint--error.hint--bottom-left:before,.hint--error.hint--bottom-right:before,.hint--error.hint--bottom:before{border-bottom-color:#b34e4d}.hint--error.hint--left:before{border-left-color:#b34e4d}.hint--error.hint--right:before{border-right-color:#b34e4d}.hint--warning:after{text-shadow:0 -1px #6c5328;background-color:#c09854}.hint--warning.hint--top-left:before,.hint--warning.hint--top-right:before,.hint--warning.hint--top:before{border-top-color:#c09854}.hint--warning.hint--bottom-left:before,.hint--warning.hint--bottom-right:before,.hint--warning.hint--bottom:before{border-bottom-color:#c09854}.hint--warning.hint--left:before{border-left-color:#c09854}.hint--warning.hint--right:before{border-right-color:#c09854}.hint--info:after{text-shadow:0 -1px #1a3c4d;background-color:#3986ac}.hint--info.hint--top-left:before,.hint--info.hint--top-right:before,.hint--info.hint--top:before{border-top-color:#3986ac}.hint--info.hint--bottom-left:before,.hint--info.hint--bottom-right:before,.hint--info.hint--bottom:before{border-bottom-color:#3986ac}.hint--info.hint--left:before{border-left-color:#3986ac}.hint--info.hint--right:before{border-right-color:#3986ac}.hint--success:after{text-shadow:0 -1px #1a321a;background-color:#458746}.hint--success.hint--top-left:before,.hint--success.hint--top-right:before,.hint--success.hint--top:before{border-top-color:#458746}.hint--success.hint--bottom-left:before,.hint--success.hint--bottom-right:before,.hint--success.hint--bottom:before{border-bottom-color:#458746}.hint--success.hint--left:before{border-left-color:#458746}.hint--success.hint--right:before{border-right-color:#458746}.hint--always:after,.hint--always:before{opacity:1;visibility:visible}.hint--always.hint--top:before{transform:translateY(-8px)}.hint--always.hint--top:after{transform:translate(-50%)translateY(-8px)}.hint--always.hint--top-left:before{transform:translateY(-8px)}.hint--always.hint--top-left:after{transform:translate(-100%)translateY(-8px)}.hint--always.hint--top-right:before,.hint--always.hint--top-right:after{transform:translateY(-8px)}.hint--always.hint--bottom:before{transform:translateY(8px)}.hint--always.hint--bottom:after{transform:translate(-50%)translateY(8px)}.hint--always.hint--bottom-left:before{transform:translateY(8px)}.hint--always.hint--bottom-left:after{transform:translate(-100%)translateY(8px)}.hint--always.hint--bottom-right:before,.hint--always.hint--bottom-right:after{transform:translateY(8px)}.hint--always.hint--left:before,.hint--always.hint--left:after{transform:translate(-8px)}.hint--always.hint--right:before,.hint--always.hint--right:after{transform:translate(8px)}.hint--rounded:after{border-radius:4px}.hint--no-animate:before,.hint--no-animate:after{transition-duration:0s}.hint--bounce:before,.hint--bounce:after{-webkit-transition:opacity .3s,visibility .3s,-webkit-transform .3s cubic-bezier(.71,1.7,.77,1.24);-moz-transition:opacity .3s,visibility .3s,-moz-transform .3s cubic-bezier(.71,1.7,.77,1.24);transition:opacity .3s,visibility .3s,transform .3s cubic-bezier(.71,1.7,.77,1.24)}.hint--no-shadow:before,.hint--no-shadow:after{text-shadow:initial;box-shadow:initial}.hint--no-arrow:before{display:none}.art-video-player.art-mobile{--art-bottom-gap:10px;--art-control-height:38px;--art-control-icon-scale:1;--art-state-size:60px;--art-settings-max-height:180px;--art-selector-max-height:180px;--art-indicator-scale:1;--art-control-opacity:1}.art-video-player.art-mobile .art-controls-left{margin-left:calc(var(--art-padding)/-1)}.art-video-player.art-mobile .art-controls-right{margin-right:calc(var(--art-padding)/-1)}'},{}],gEtej:[function(e,t,r){var n,o=e("@swc/helpers/_/_type_of");n=function(){function e(t){return(e="function"==typeof Symbol&&"symbol"==(0,o._)(Symbol.iterator)?function(e){return void 0===e?"undefined":(0,o._)(e)}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":void 0===e?"undefined":(0,o._)(e)})(t)}var t=Object.prototype.toString,r=function(r){if(void 0===r)return"undefined";if(null===r)return"null";var o=e(r);if("boolean"===o)return"boolean";if("string"===o)return"string";if("number"===o)return"number";if("symbol"===o)return"symbol";if("function"===o)return"GeneratorFunction"===n(r)?"generatorfunction":"function";if(Array.isArray?Array.isArray(r):r instanceof Array)return"array";if(r.constructor&&"function"==typeof r.constructor.isBuffer&&r.constructor.isBuffer(r))return"buffer";if(function(e){try{if("number"==typeof e.length&&"function"==typeof e.callee)return!0}catch(e){if(-1!==e.message.indexOf("callee"))return!0}return!1}(r))return"arguments";if(r instanceof Date||"function"==typeof r.toDateString&&"function"==typeof r.getDate&&"function"==typeof r.setDate)return"date";if(r instanceof Error||"string"==typeof r.message&&r.constructor&&"number"==typeof r.constructor.stackTraceLimit)return"error";if(r instanceof RegExp||"string"==typeof r.flags&&"boolean"==typeof r.ignoreCase&&"boolean"==typeof r.multiline&&"boolean"==typeof r.global)return"regexp";switch(n(r)){case"Symbol":return"symbol";case"Promise":return"promise";case"WeakMap":return"weakmap";case"WeakSet":return"weakset";case"Map":return"map";case"Set":return"set";case"Int8Array":return"int8array";case"Uint8Array":return"uint8array";case"Uint8ClampedArray":return"uint8clampedarray";case"Int16Array":return"int16array";case"Uint16Array":return"uint16array";case"Int32Array":return"int32array";case"Uint32Array":return"uint32array";case"Float32Array":return"float32array";case"Float64Array":return"float64array"}if("function"==typeof r.throw&&"function"==typeof r.return&&"function"==typeof r.next)return"generator";switch(o=t.call(r)){case"[object Object]":return"object";case"[object Map Iterator]":return"mapiterator";case"[object Set Iterator]":return"setiterator";case"[object String Iterator]":return"stringiterator";case"[object Array Iterator]":return"arrayiterator"}return o.slice(8,-1).toLowerCase().replace(/\s/g,"")};function n(e){return e.constructor?e.constructor.name:null}function a(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:["option"];return s(e,t,n),i(e,t,n),function(e,t,n){var o=r(t),l=r(e);if("object"===o){if("object"!==l)throw Error("[Type Error]: '".concat(n.join("."),"' require 'object' type, but got '").concat(l,"'"));Object.keys(t).forEach(function(r){var o=e[r],l=t[r],c=n.slice();c.push(r),s(o,l,c),i(o,l,c),a(o,l,c)})}if("array"===o){if("array"!==l)throw Error("[Type Error]: '".concat(n.join("."),"' require 'array' type, but got '").concat(l,"'"));e.forEach(function(r,o){var l=e[o],c=t[o]||t[0],u=n.slice();u.push(o),s(l,c,u),i(l,c,u),a(l,c,u)})}}(e,t,n),e}function s(e,t,n){if("string"===r(t)){var o=r(e);if("?"===t[0]&&(t=t.slice(1)+"|undefined"),!(-1<t.indexOf("|")?t.split("|").map(function(e){return e.toLowerCase().trim()}).filter(Boolean).some(function(e){return o===e}):t.toLowerCase().trim()===o))throw Error("[Type Error]: '".concat(n.join("."),"' require '").concat(t,"' type, but got '").concat(o,"'"))}}function i(e,t,n){if("function"===r(t)){var o=t(e,r(e),n);if(!0!==o){var a=r(o);throw"string"===a?Error(o):"error"===a?o:Error("[Validator Error]: The scheme for '".concat(n.join("."),"' validator require return true, but got '").concat(o,"'"))}}}return a.kindOf=r,a},t.exports=n()},{"@swc/helpers/_/_type_of":"l4kpM"}],"23yIZ":[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return s});var o=e("@swc/helpers/_/_class_call_check"),a=e("@swc/helpers/_/_create_class"),s=function(){function e(){(0,o._)(this,e)}return(0,a._)(e,[{key:"on",value:function(e,t,r){var n=this.e||(this.e={});return(n[e]||(n[e]=[])).push({fn:t,ctx:r}),this}},{key:"once",value:function(e,t,r){var n=this;function o(){for(var a=arguments.length,s=Array(a),i=0;i<a;i++)s[i]=arguments[i];n.off(e,o),t.apply(r,s)}return o._=t,this.on(e,o,r)}},{key:"emit",value:function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];for(var o=((this.e||(this.e={}))[e]||[]).slice(),a=0;a<o.length;a+=1)o[a].fn.apply(o[a].ctx,r);return this}},{key:"off",value:function(e,t){var r=this.e||(this.e={}),n=r[e],o=[];if(n&&t)for(var a=0,s=n.length;a<s;a+=1)n[a].fn!==t&&n[a].fn._!==t&&o.push(n[a]);return o.length?r[e]=o:delete r[e],this}}]),e}()},{"@swc/helpers/_/_class_call_check":"dRqgV","@swc/helpers/_/_create_class":"8RAzW","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],"7esST":[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r);var o=e("./dom");n.exportAll(o,r);var a=e("./error");n.exportAll(a,r);var s=e("./subtitle");n.exportAll(s,r);var i=e("./file");n.exportAll(i,r);var l=e("./property");n.exportAll(l,r);var c=e("./time");n.exportAll(c,r);var u=e("./format");n.exportAll(u,r);var p=e("./compatibility");n.exportAll(p,r)},{"./dom":"7UlmS","./error":"d6sk8","./subtitle":"gOgPD","./file":"8pVuv","./property":"iCvtI","./time":"18KGG","./format":"eSCih","./compatibility":"g6fxC","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],"7UlmS":[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"query",function(){return a}),n.export(r,"queryAll",function(){return s}),n.export(r,"addClass",function(){return i}),n.export(r,"removeClass",function(){return l}),n.export(r,"hasClass",function(){return c}),n.export(r,"append",function(){return u}),n.export(r,"remove",function(){return p}),n.export(r,"setStyle",function(){return f}),n.export(r,"setStyles",function(){return d}),n.export(r,"getStyle",function(){return h}),n.export(r,"sublings",function(){return m}),n.export(r,"inverseClass",function(){return v}),n.export(r,"tooltip",function(){return g}),n.export(r,"isInViewport",function(){return _}),n.export(r,"includeFromEvent",function(){return y}),n.export(r,"replaceElement",function(){return b}),n.export(r,"createElement",function(){return w}),n.export(r,"getIcon",function(){return x}),n.export(r,"setStyleText",function(){return j}),n.export(r,"supportsFlex",function(){return k}),n.export(r,"getRect",function(){return T});var o=e("./compatibility");function a(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:document;return t.querySelector(e)}function s(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:document;return Array.from(t.querySelectorAll(e))}function i(e,t){return e.classList.add(t)}function l(e,t){return e.classList.remove(t)}function c(e,t){return e.classList.contains(t)}function u(e,t){return t instanceof Element?e.appendChild(t):e.insertAdjacentHTML("beforeend",String(t)),e.lastElementChild||e.lastChild}function p(e){return e.parentNode.removeChild(e)}function f(e,t,r){return e.style[t]=r,e}function d(e,t){for(var r in t)f(e,r,t[r]);return e}function h(e,t){var r=!(arguments.length>2)||void 0===arguments[2]||arguments[2],n=window.getComputedStyle(e,null).getPropertyValue(t);return r?parseFloat(n):n}function m(e){return Array.from(e.parentElement.children).filter(function(t){return t!==e})}function v(e,t){m(e).forEach(function(e){return l(e,t)}),i(e,t)}function g(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"top";o.isMobile||(e.setAttribute("aria-label",t),i(e,"hint--rounded"),i(e,"hint--".concat(r)))}function _(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=e.getBoundingClientRect(),n=window.innerHeight||document.documentElement.clientHeight,o=window.innerWidth||document.documentElement.clientWidth,a=r.top-t<=n&&r.top+r.height+t>=0,s=r.left-t<=o+t&&r.left+r.width+t>=0;return a&&s}function y(e,t){return e.composedPath&&e.composedPath().indexOf(t)>-1}function b(e,t){return t.parentNode.replaceChild(e,t),e}function w(e){return document.createElement(e)}function x(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",r=w("i");return i(r,"art-icon"),i(r,"art-icon-".concat(e)),u(r,t),r}function j(e,t){var r=document.getElementById(e);if(r)r.textContent=t;else{var n=w("style");n.id=e,n.textContent=t,document.head.appendChild(n)}}function k(){var e=document.createElement("div");return e.style.display="flex","flex"===e.style.display}function T(e){return e.getBoundingClientRect()}},{"./compatibility":"g6fxC","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],g6fxC:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"userAgent",function(){return o}),n.export(r,"isSafari",function(){return a}),n.export(r,"isWechat",function(){return s}),n.export(r,"isIE",function(){return i}),n.export(r,"isAndroid",function(){return l}),n.export(r,"isIOS",function(){return c}),n.export(r,"isIOS13",function(){return u}),n.export(r,"isMobile",function(){return p}),n.export(r,"isBrowser",function(){return f});var o="undefined"!=typeof navigator?navigator.userAgent:"",a=/^((?!chrome|android).)*safari/i.test(o),s=/MicroMessenger/i.test(o),i=/MSIE|Trident/i.test(o),l=/android/i.test(o),c=/iPad|iPhone|iPod/i.test(o)&&!window.MSStream,u=c||o.includes("Macintosh")&&navigator.maxTouchPoints>=1,p=/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(o)||u,f="undefined"!=typeof window},{"@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],d6sk8:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"ArtPlayerError",function(){return c}),n.export(r,"errorHandle",function(){return u});var o=e("@swc/helpers/_/_assert_this_initialized"),a=e("@swc/helpers/_/_class_call_check"),s=e("@swc/helpers/_/_inherits"),i=e("@swc/helpers/_/_wrap_native_super"),l=e("@swc/helpers/_/_create_super"),c=function(e){(0,s._)(r,e);var t=(0,l._)(r);function r(e,n){var s;return(0,a._)(this,r),s=t.call(this,e),"function"==typeof Error.captureStackTrace&&Error.captureStackTrace((0,o._)(s),n||s.constructor),s.name="ArtPlayerError",s}return r}((0,i._)(Error));function u(e,t){if(!e)throw new c(t);return e}},{"@swc/helpers/_/_assert_this_initialized":"jTlDX","@swc/helpers/_/_class_call_check":"dRqgV","@swc/helpers/_/_inherits":"aRPJQ","@swc/helpers/_/_wrap_native_super":"1PJxD","@swc/helpers/_/_create_super":"kqTtK","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],"1PJxD":[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"_wrap_native_super",function(){return l}),n.export(r,"_",function(){return l});var o=e("./_construct.js"),a=e("./_get_prototype_of.js"),s=e("./_is_native_function.js"),i=e("./_set_prototype_of.js");function l(e){var t="function"==typeof Map?new Map:void 0;return(l=function(e){if(null===e||!(0,s._is_native_function)(e))return e;if("function"!=typeof e)throw TypeError("Super expression must either be null or a function");if(void 0!==t){if(t.has(e))return t.get(e);t.set(e,r)}function r(){return(0,o._construct)(e,arguments,(0,a._get_prototype_of)(this).constructor)}return r.prototype=Object.create(e.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),(0,i._set_prototype_of)(r,e)})(e)}},{"./_construct.js":"8tOYA","./_get_prototype_of.js":"2mh6E","./_is_native_function.js":"aYK5s","./_set_prototype_of.js":"eAgjA","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],"8tOYA":[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"_construct",function(){return s}),n.export(r,"_",function(){return s});var o=e("./_is_native_reflect_construct.js"),a=e("./_set_prototype_of.js");function s(e,t,r){return(s=(0,o._is_native_reflect_construct)()?Reflect.construct:function(e,t,r){var n=[null];n.push.apply(n,t);var o=new(Function.bind.apply(e,n));return r&&(0,a._set_prototype_of)(o,r.prototype),o}).apply(null,arguments)}},{"./_is_native_reflect_construct.js":"M5rXK","./_set_prototype_of.js":"eAgjA","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],aYK5s:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");function o(e){return -1!==Function.toString.call(e).indexOf("[native code]")}n.defineInteropFlag(r),n.export(r,"_is_native_function",function(){return o}),n.export(r,"_",function(){return o})},{"@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],gOgPD:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");function o(e){return"WEBVTT \r\n\r\n".concat(e.replace(/(\d\d:\d\d:\d\d)[,.](\d+)/g,function(e,t,r){var n=r.slice(0,3);return 1===r.length&&(n=r+"00"),2===r.length&&(n=r+"0"),"".concat(t,",").concat(n)}).replace(/\{\\([ibu])\}/g,"</$1>").replace(/\{\\([ibu])1\}/g,"<$1>").replace(/\{([ibu])\}/g,"<$1>").replace(/\{\/([ibu])\}/g,"</$1>").replace(/(\d\d:\d\d:\d\d),(\d\d\d)/g,"$1.$2").replace(/{[\s\S]*?}/g,"").concat("\r\n\r\n"))}function a(e){return URL.createObjectURL(new Blob([e],{type:"text/vtt"}))}function s(e){var t=RegExp("Dialogue:\\s\\d,(\\d+:\\d\\d:\\d\\d.\\d\\d),(\\d+:\\d\\d:\\d\\d.\\d\\d),([^,]*),([^,]*),(?:[^,]*,){4}([\\s\\S]*)$","i");function r(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return e.split(/[:.]/).map(function(e,t,r){if(t===r.length-1){if(1===e.length)return".".concat(e,"00");if(2===e.length)return".".concat(e,"0")}else if(1===e.length)return(0===t?"0":":0")+e;return 0===t?e:t===r.length-1?".".concat(e):":".concat(e)}).join("")}return"WEBVTT\n\n"+e.split(/\r?\n/).map(function(e){var n=e.match(t);return n?{start:r(n[1].trim()),end:r(n[2].trim()),text:n[5].replace(/{[\s\S]*?}/g,"").replace(/(\\N)/g,"\n").trim().split(/\r?\n/).map(function(e){return e.trim()}).join("\n")}:null}).filter(function(e){return e}).map(function(e,t){return e?t+1+"\n"+"".concat(e.start," --> ").concat(e.end)+"\n"+"".concat(e.text):""}).filter(function(e){return e.trim()}).join("\n\n")}n.defineInteropFlag(r),n.export(r,"srtToVtt",function(){return o}),n.export(r,"vttToBlob",function(){return a}),n.export(r,"assToVtt",function(){return s})},{"@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],"8pVuv":[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");function o(e,t){var r=document.createElement("a");r.style.display="none",r.href=e,r.download=t,document.body.appendChild(r),r.click(),document.body.removeChild(r)}n.defineInteropFlag(r),n.export(r,"getExt",function(){return function e(t){return t.includes("?")?e(t.split("?")[0]):t.includes("#")?e(t.split("#")[0]):t.trim().toLowerCase().split(".").pop()}}),n.export(r,"download",function(){return o})},{"@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],iCvtI:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"def",function(){return a}),n.export(r,"has",function(){return i}),n.export(r,"get",function(){return l}),n.export(r,"mergeDeep",function(){return function e(){for(var t=arguments.length,r=Array(t),n=0;n<t;n++)r[n]=arguments[n];var a=function(e){return e&&"object"==typeof e&&!Array.isArray(e)};return r.reduce(function(t,r){return Object.keys(r).forEach(function(n){var s=t[n],i=r[n];Array.isArray(s)&&Array.isArray(i)?t[n]=s.concat.apply(s,(0,o._)(i)):a(s)&&a(i)?t[n]=e(s,i):t[n]=i}),t},{})}});var o=e("@swc/helpers/_/_to_consumable_array"),a=Object.defineProperty,s=Object.prototype.hasOwnProperty;function i(e,t){return s.call(e,t)}function l(e,t){return Object.getOwnPropertyDescriptor(e,t)}},{"@swc/helpers/_/_to_consumable_array":"lHEqu","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],lHEqu:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"_to_consumable_array",function(){return l}),n.export(r,"_",function(){return l});var o=e("./_array_without_holes.js"),a=e("./_iterable_to_array.js"),s=e("./_non_iterable_spread.js"),i=e("./_unsupported_iterable_to_array.js");function l(e){return(0,o._array_without_holes)(e)||(0,a._iterable_to_array)(e)||(0,i._unsupported_iterable_to_array)(e)||(0,s._non_iterable_spread)()}},{"./_array_without_holes.js":"tYoZb","./_iterable_to_array.js":"4xElh","./_non_iterable_spread.js":"lCnHY","./_unsupported_iterable_to_array.js":"2OTAn","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],tYoZb:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"_array_without_holes",function(){return a}),n.export(r,"_",function(){return a});var o=e("./_array_like_to_array.js");function a(e){if(Array.isArray(e))return(0,o._array_like_to_array)(e)}},{"./_array_like_to_array.js":"204eC","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],"204eC":[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");function o(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}n.defineInteropFlag(r),n.export(r,"_array_like_to_array",function(){return o}),n.export(r,"_",function(){return o})},{"@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],"4xElh":[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");function o(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}n.defineInteropFlag(r),n.export(r,"_iterable_to_array",function(){return o}),n.export(r,"_",function(){return o})},{"@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],lCnHY:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");function o(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}n.defineInteropFlag(r),n.export(r,"_non_iterable_spread",function(){return o}),n.export(r,"_",function(){return o})},{"@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],"2OTAn":[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"_unsupported_iterable_to_array",function(){return a}),n.export(r,"_",function(){return a});var o=e("./_array_like_to_array.js");function a(e,t){if(e){if("string"==typeof e)return(0,o._array_like_to_array)(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(r);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return(0,o._array_like_to_array)(e,t)}}},{"./_array_like_to_array.js":"204eC","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],"18KGG":[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");function o(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return new Promise(function(t){return setTimeout(t,e)})}function a(e,t){var r;return function(){for(var n=this,o=arguments.length,a=Array(o),s=0;s<o;s++)a[s]=arguments[s];clearTimeout(r),r=setTimeout(function(){return r=null,e.apply(n,a)},t)}}function s(e,t){var r=!1;return function(){for(var n=arguments.length,o=Array(n),a=0;a<n;a++)o[a]=arguments[a];r||(e.apply(this,o),r=!0,setTimeout(function(){r=!1},t))}}n.defineInteropFlag(r),n.export(r,"sleep",function(){return o}),n.export(r,"debounce",function(){return a}),n.export(r,"throttle",function(){return s})},{"@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],eSCih:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"clamp",function(){return a}),n.export(r,"capitalize",function(){return s}),n.export(r,"isStringOrNumber",function(){return i}),n.export(r,"secondToTime",function(){return l}),n.export(r,"escape",function(){return c}),n.export(r,"unescape",function(){return u});var o=e("@swc/helpers/_/_type_of");function a(e,t,r){return Math.max(Math.min(e,Math.max(t,r)),Math.min(t,r))}function s(e){return e.charAt(0).toUpperCase()+e.slice(1)}function i(e){return["string","number"].includes(void 0===e?"undefined":(0,o._)(e))}function l(e){if(!e)return"00:00";var t=Math.floor(e/3600),r=Math.floor((e-3600*t)/60),n=Math.floor(e-3600*t-60*r);return(t>0?[t,r,n]:[r,n]).map(function(e){return e<10?"0".concat(e):String(e)}).join(":")}function c(e){return e.replace(/[&<>'"]/g,function(e){return({"&":"&amp;","<":"&lt;",">":"&gt;","'":"&#39;",'"':"&quot;"})[e]||e})}function u(e){var t={"&amp;":"&","&lt;":"<","&gt;":">","&#39;":"'","&quot;":'"'},r=RegExp("(".concat(Object.keys(t).join("|"),")"),"g");return e.replace(r,function(e){return t[e]||e})}},{"@swc/helpers/_/_type_of":"l4kpM","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],hYXnH:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"ComponentOption",function(){return h});var o=e("@swc/helpers/_/_object_spread"),a=e("@swc/helpers/_/_object_spread_props"),s=e("../utils"),i="array",l="boolean",c="string",u="number",p="object",f="function";function d(e,t,r){return(0,s.errorHandle)(t===c||t===u||e instanceof Element,"".concat(r.join(".")," require '").concat(c,"' or 'Element' type"))}var h={html:d,disable:"?".concat(l),name:"?".concat(c),index:"?".concat(u),style:"?".concat(p),click:"?".concat(f),mounted:"?".concat(f),tooltip:"?".concat(c,"|").concat(u),width:"?".concat(u),selector:"?".concat(i),onSelect:"?".concat(f),switch:"?".concat(l),onSwitch:"?".concat(f),range:"?".concat(i),onRange:"?".concat(f),onChange:"?".concat(f)};r.default={id:c,container:d,url:c,poster:c,type:c,theme:c,lang:c,volume:u,isLive:l,muted:l,autoplay:l,autoSize:l,autoMini:l,loop:l,flip:l,playbackRate:l,aspectRatio:l,screenshot:l,setting:l,hotkey:l,pip:l,mutex:l,backdrop:l,fullscreen:l,fullscreenWeb:l,subtitleOffset:l,miniProgressBar:l,useSSR:l,playsInline:l,lock:l,fastForward:l,autoPlayback:l,autoOrientation:l,airplay:l,plugins:[f],layers:[h],contextmenu:[h],settings:[h],controls:[(0,a._)((0,o._)({},h),{position:function(e,t,r){var n=["top","left","right"];return(0,s.errorHandle)(n.includes(e),"".concat(r.join(".")," only accept ").concat(n.toString()," as parameters"))}})],quality:[{default:"?".concat(l),html:c,url:c}],highlight:[{time:u,text:c}],thumbnails:{url:c,number:u,column:u,width:u,height:u},subtitle:{url:c,name:c,type:c,style:p,escape:l,encoding:c,onVttLoad:f},moreVideoAttr:p,i18n:p,icons:p,cssVar:p,customType:p}},{"@swc/helpers/_/_object_spread":"g42Vq","@swc/helpers/_/_object_spread_props":"cTIVI","../utils":"7esST","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],g42Vq:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"_object_spread",function(){return a}),n.export(r,"_",function(){return a});var o=e("./_define_property.js");function a(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable}))),n.forEach(function(t){(0,o._define_property)(e,t,r[t])})}return e}},{"./_define_property.js":"ceYdR","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],ceYdR:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");function o(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}n.defineInteropFlag(r),n.export(r,"_define_property",function(){return o}),n.export(r,"_",function(){return o})},{"@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],cTIVI:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");function o(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):(function(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r.push.apply(r,n)}return r})(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))}),e}n.defineInteropFlag(r),n.export(r,"_object_spread_props",function(){return o}),n.export(r,"_",function(){return o})},{"@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],"1lpcp":[function(e,t,r){e("@parcel/transformer-js/src/esmodule-helpers.js").defineInteropFlag(r),r.default={propertys:["audioTracks","autoplay","buffered","controller","controls","crossOrigin","currentSrc","currentTime","defaultMuted","defaultPlaybackRate","duration","ended","error","loop","mediaGroup","muted","networkState","paused","playbackRate","played","preload","readyState","seekable","seeking","src","startDate","textTracks","videoTracks","volume"],methods:["addTextTrack","canPlayType","load","play","pause"],events:["abort","canplay","canplaythrough","durationchange","emptied","ended","error","loadeddata","loadedmetadata","loadstart","pause","play","playing","progress","ratechange","seeked","seeking","stalled","suspend","timeupdate","volumechange","waiting"],prototypes:["width","height","videoWidth","videoHeight","poster","webkitDecodedFrameCount","webkitDroppedFrameCount","playsInline","webkitSupportsFullscreen","webkitDisplayingFullscreen","onenterpictureinpicture","onleavepictureinpicture","disablePictureInPicture","cancelVideoFrameCallback","requestVideoFrameCallback","getVideoPlaybackQuality","requestPictureInPicture","webkitEnterFullScreen","webkitEnterFullscreen","webkitExitFullScreen","webkitExitFullscreen"]}},{"@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],aRbVP:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return i});var o=e("@swc/helpers/_/_class_call_check"),a=e("@swc/helpers/_/_create_class"),s=e("./utils"),i=function(){function e(t){var r=this;(0,o._)(this,e),this.art=t;var n=t.option,a=t.constructor;n.container instanceof Element?this.$container=n.container:(this.$container=(0,s.query)(n.container),(0,s.errorHandle)(this.$container,"No container element found by ".concat(n.container))),(0,s.errorHandle)((0,s.supportsFlex)(),"The current browser does not support flex layout");var i=this.$container.tagName.toLowerCase();(0,s.errorHandle)("div"===i,"Unsupported container element type, only support 'div' but got '".concat(i,"'")),(0,s.errorHandle)(a.instances.every(function(e){return e.template.$container!==r.$container}),"Cannot mount multiple instances on the same dom element"),this.query=this.query.bind(this),this.$container.dataset.artId=t.id,this.init()}return(0,a._)(e,[{key:"query",value:function(e){return(0,s.query)(e,this.$container)}},{key:"init",value:function(){var t=this.art.option;t.useSSR||(this.$container.innerHTML=e.html),this.$player=this.query(".art-video-player"),this.$video=this.query(".art-video"),this.$track=this.query("track"),this.$poster=this.query(".art-poster"),this.$subtitle=this.query(".art-subtitle"),this.$danmuku=this.query(".art-danmuku"),this.$bottom=this.query(".art-bottom"),this.$progress=this.query(".art-progress"),this.$controls=this.query(".art-controls"),this.$controlsLeft=this.query(".art-controls-left"),this.$controlsCenter=this.query(".art-controls-center"),this.$controlsRight=this.query(".art-controls-right"),this.$layer=this.query(".art-layers"),this.$loading=this.query(".art-loading"),this.$notice=this.query(".art-notice"),this.$noticeInner=this.query(".art-notice-inner"),this.$mask=this.query(".art-mask"),this.$state=this.query(".art-state"),this.$setting=this.query(".art-settings"),this.$info=this.query(".art-info"),this.$infoPanel=this.query(".art-info-panel"),this.$infoClose=this.query(".art-info-close"),this.$contextmenu=this.query(".art-contextmenus"),t.backdrop&&(0,s.addClass)(this.$player,"art-backdrop"),s.isMobile&&(0,s.addClass)(this.$player,"art-mobile")}},{key:"destroy",value:function(e){e?this.$container.innerHTML="":(0,s.addClass)(this.$player,"art-destroy")}}],[{key:"html",get:function(){return'\n <div class="art-video-player art-subtitle-show art-layer-show art-control-show art-mask-show">\n<video class="art-video">\n<track default kind="metadata" src=""></track>\n</video>\n<div class="art-poster"></div>\n<div class="art-subtitle"></div>\n<div class="art-danmuku"></div>\n<div class="art-layers"></div>\n<div class="art-mask">\n<div class="art-state"></div>\n</div>\n<div class="art-bottom">\n<div class="art-progress"></div>\n<div class="art-controls">\n<div class="art-controls-left"></div>\n<div class="art-controls-center"></div>\n<div class="art-controls-right"></div>\n</div>\n</div>\n<div class="art-loading"></div>\n<div class="art-notice">\n<div class="art-notice-inner"></div>\n</div>\n<div class="art-settings"></div>\n<div class="art-info">\n<div class="art-info-panel">\n<div class="art-info-item">\n<div class="art-info-title">Player version:</div>\n<div class="art-info-content">5.1.7</div>\n</div>\n<div class="art-info-item">\n<div class="art-info-title">Video url:</div>\n<div class="art-info-content" data-video="src"></div>\n</div>\n<div class="art-info-item">\n<div class="art-info-title">Video volume:</div>\n<div class="art-info-content" data-video="volume"></div>\n</div>\n<div class="art-info-item">\n<div class="art-info-title">Video time:</div>\n<div class="art-info-content" data-video="currentTime"></div>\n</div>\n<div class="art-info-item">\n<div class="art-info-title">Video duration:</div>\n<div class="art-info-content" data-video="duration"></div>\n</div>\n<div class="art-info-item">\n<div class="art-info-title">Video resolution:</div>\n<div class="art-info-content">\n<span data-video="videoWidth"></span>x<span data-video="videoHeight"></span>\n</div>\n</div>\n</div>\n<div class="art-info-close">[x]</div>\n</div>\n<div class="art-contextmenus"></div>\n</div>\n '}}]),e}()},{"@swc/helpers/_/_class_call_check":"dRqgV","@swc/helpers/_/_create_class":"8RAzW","./utils":"7esST","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],d1hXD:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return c});var o=e("@swc/helpers/_/_class_call_check"),a=e("@swc/helpers/_/_create_class"),s=e("../utils"),i=e("./zh-cn"),l=n.interopDefault(i),c=function(){function e(t){(0,o._)(this,e),this.art=t,this.languages={"zh-cn":l.default},this.language={},this.update(t.option.i18n)}return(0,a._)(e,[{key:"init",value:function(){var e=this.art.option.lang.toLowerCase();this.language=this.languages[e]||{}}},{key:"get",value:function(e){return this.language[e]||e}},{key:"update",value:function(e){this.languages=(0,s.mergeDeep)(this.languages,e),this.init()}}]),e}()},{"@swc/helpers/_/_class_call_check":"dRqgV","@swc/helpers/_/_create_class":"8RAzW","../utils":"7esST","./zh-cn":"32rfy","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],"32rfy":[function(e,t,r){e("@parcel/transformer-js/src/esmodule-helpers.js").defineInteropFlag(r);var n={"Video Info":"统计信息",Close:"关闭","Video Load Failed":"加载失败",Volume:"音量",Play:"播放",Pause:"暂停",Rate:"速度",Mute:"静音","Video Flip":"画面翻转",Horizontal:"水平",Vertical:"垂直",Reconnect:"重新连接","Show Setting":"显示设置","Hide Setting":"隐藏设置",Screenshot:"截图","Play Speed":"播放速度","Aspect Ratio":"画面比例",Default:"默认",Normal:"正常",Open:"打开","Switch Video":"切换","Switch Subtitle":"切换字幕",Fullscreen:"全屏","Exit Fullscreen":"退出全屏","Web Fullscreen":"网页全屏","Exit Web Fullscreen":"退出网页全屏","Mini Player":"迷你播放器","PIP Mode":"开启画中画","Exit PIP Mode":"退出画中画","PIP Not Supported":"不支持画中画","Fullscreen Not Supported":"不支持全屏","Subtitle Offset":"字幕偏移","Last Seen":"上次看到","Jump Play":"跳转播放",AirPlay:"隔空播放","AirPlay Not Available":"隔空播放不可用"};r.default=n,"undefined"!=typeof window&&(window["artplayer-i18n-zh-cn"]=n)},{"@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],aUXOX:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return ew});var o=e("@swc/helpers/_/_class_call_check"),a=e("./urlMix"),s=n.interopDefault(a),i=e("./attrMix"),l=n.interopDefault(i),c=e("./playMix"),u=n.interopDefault(c),p=e("./pauseMix"),f=n.interopDefault(p),d=e("./toggleMix"),h=n.interopDefault(d),m=e("./seekMix"),v=n.interopDefault(m),g=e("./volumeMix"),_=n.interopDefault(g),y=e("./currentTimeMix"),b=n.interopDefault(y),w=e("./durationMix"),x=n.interopDefault(w),j=e("./switchMix"),k=n.interopDefault(j),T=e("./playbackRateMix"),S=n.interopDefault(T),M=e("./aspectRatioMix"),I=n.interopDefault(M),E=e("./screenshotMix"),F=n.interopDefault(E),C=e("./fullscreenMix"),R=n.interopDefault(C),O=e("./fullscreenWebMix"),P=n.interopDefault(O),D=e("./pipMix"),A=n.interopDefault(D),z=e("./loadedMix"),V=n.interopDefault(z),$=e("./playedMix"),L=n.interopDefault($),q=e("./playingMix"),H=n.interopDefault(q),N=e("./autoSizeMix"),W=n.interopDefault(N),B=e("./rectMix"),U=n.interopDefault(B),Y=e("./flipMix"),K=n.interopDefault(Y),X=e("./miniMix"),G=n.interopDefault(X),J=e("./posterMix"),Z=n.interopDefault(J),Q=e("./autoHeightMix"),ee=n.interopDefault(Q),et=e("./cssVarMix"),er=n.interopDefault(et),en=e("./themeMix"),eo=n.interopDefault(en),ea=e("./typeMix"),es=n.interopDefault(ea),ei=e("./stateMix"),el=n.interopDefault(ei),ec=e("./subtitleOffsetMix"),eu=n.interopDefault(ec),ep=e("./airplayMix"),ef=n.interopDefault(ep),ed=e("./qualityMix"),eh=n.interopDefault(ed),em=e("./thumbnailsMix"),ev=n.interopDefault(em),eg=e("./optionInit"),e_=n.interopDefault(eg),ey=e("./eventInit"),eb=n.interopDefault(ey),ew=function e(t){(0,o._)(this,e),(0,s.default)(t),(0,l.default)(t),(0,u.default)(t),(0,f.default)(t),(0,h.default)(t),(0,v.default)(t),(0,_.default)(t),(0,b.default)(t),(0,x.default)(t),(0,k.default)(t),(0,S.default)(t),(0,I.default)(t),(0,F.default)(t),(0,R.default)(t),(0,P.default)(t),(0,A.default)(t),(0,V.default)(t),(0,L.default)(t),(0,H.default)(t),(0,W.default)(t),(0,U.default)(t),(0,K.default)(t),(0,G.default)(t),(0,Z.default)(t),(0,ee.default)(t),(0,er.default)(t),(0,eo.default)(t),(0,es.default)(t),(0,el.default)(t),(0,eu.default)(t),(0,ef.default)(t),(0,eh.default)(t),(0,ev.default)(t),(0,eb.default)(t),(0,e_.default)(t)}},{"@swc/helpers/_/_class_call_check":"dRqgV","./urlMix":"94qAG","./attrMix":"gbKEc","./playMix":"4YToo","./pauseMix":"jJnYm","./toggleMix":"atYsy","./seekMix":"lsslL","./volumeMix":"cSjxV","./currentTimeMix":"7NYVK","./durationMix":"fW8lt","./switchMix":"j5YX9","./playbackRateMix":"Pz0cc","./aspectRatioMix":"ef8Wu","./screenshotMix":"ca8va","./fullscreenMix":"alRzX","./fullscreenWebMix":"4er6h","./pipMix":"jBv10","./loadedMix":"hQUyr","./playedMix":"8OMWK","./playingMix":"btSPR","./autoSizeMix":"dQH4Z","./rectMix":"hPGBy","./flipMix":"aVvjy","./miniMix":"juvfl","./posterMix":"2UAdD","./autoHeightMix":"377XR","./cssVarMix":"afCNS","./themeMix":"7l7GX","./typeMix":"3Vlhc","./stateMix":"ey2W0","./subtitleOffsetMix":"kDZ8v","./airplayMix":"bXTl1","./qualityMix":"hzrnC","./thumbnailsMix":"5oRuL","./optionInit":"g9kWX","./eventInit":"lgPrh","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],"94qAG":[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return i});var o=e("@swc/helpers/_/_async_to_generator"),a=e("@swc/helpers/_/_ts_generator"),s=e("../utils");function i(e){var t=e.option,r=e.template.$video;(0,s.def)(e,"url",{get:function(){return r.src},set:function(n){return(0,o._)(function(){var o,i,l;return(0,a._)(this,function(a){switch(a.label){case 0:if(!n)return[3,4];if(o=e.url,i=t.type||(0,s.getExt)(n),l=t.customType[i],!(i&&l))return[3,2];return[4,(0,s.sleep)()];case 1:return a.sent(),e.loading.show=!0,l.call(e,r,n,e),[3,3];case 2:URL.revokeObjectURL(o),r.src=n,a.label=3;case 3:return o!==e.url&&(e.option.url=n,e.isReady&&o&&e.once("video:canplay",function(){e.emit("restart",n)})),[3,6];case 4:return[4,(0,s.sleep)()];case 5:a.sent(),e.loading.show=!0,a.label=6;case 6:return[2]}})})()}})}},{"@swc/helpers/_/_async_to_generator":"eFkS8","@swc/helpers/_/_ts_generator":"7FJ4U","../utils":"7esST","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],eFkS8:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");function o(e,t,r,n,o,a,s){try{var i=e[a](s),l=i.value}catch(e){r(e);return}i.done?t(l):Promise.resolve(l).then(n,o)}function a(e){return function(){var t=this,r=arguments;return new Promise(function(n,a){var s=e.apply(t,r);function i(e){o(s,n,a,i,l,"next",e)}function l(e){o(s,n,a,i,l,"throw",e)}i(void 0)})}}n.defineInteropFlag(r),n.export(r,"_async_to_generator",function(){return a}),n.export(r,"_",function(){return a})},{"@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],"7FJ4U":[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"_",function(){return o.__generator}),n.export(r,"_ts_generator",function(){return o.__generator});var o=e("tslib")},{tslib:"jXHB6","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],jXHB6:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"__extends",function(){return s}),n.export(r,"__assign",function(){return i}),n.export(r,"__rest",function(){return l}),n.export(r,"__decorate",function(){return c}),n.export(r,"__param",function(){return u}),n.export(r,"__esDecorate",function(){return p}),n.export(r,"__runInitializers",function(){return f}),n.export(r,"__propKey",function(){return d}),n.export(r,"__setFunctionName",function(){return h}),n.export(r,"__metadata",function(){return m}),n.export(r,"__awaiter",function(){return v}),n.export(r,"__generator",function(){return g}),n.export(r,"__createBinding",function(){return _}),n.export(r,"__exportStar",function(){return y}),n.export(r,"__values",function(){return b}),n.export(r,"__read",function(){return w}),n.export(r,"__spread",function(){return x}),n.export(r,"__spreadArrays",function(){return j}),n.export(r,"__spreadArray",function(){return k}),n.export(r,"__await",function(){return T}),n.export(r,"__asyncGenerator",function(){return S}),n.export(r,"__asyncDelegator",function(){return M}),n.export(r,"__asyncValues",function(){return I}),n.export(r,"__makeTemplateObject",function(){return E}),n.export(r,"__importStar",function(){return C}),n.export(r,"__importDefault",function(){return R}),n.export(r,"__classPrivateFieldGet",function(){return O}),n.export(r,"__classPrivateFieldSet",function(){return P}),n.export(r,"__classPrivateFieldIn",function(){return D}),n.export(r,"__addDisposableResource",function(){return A}),n.export(r,"__disposeResources",function(){return V});var o=e("@swc/helpers/_/_type_of"),a=function(e,t){return(a=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)};function s(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}a(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}var i=function(){return(i=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function l(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r}function c(e,t,r,n){var o,a=arguments.length,s=a<3?t:null===n?n=Object.getOwnPropertyDescriptor(t,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(e,t,r,n);else for(var i=e.length-1;i>=0;i--)(o=e[i])&&(s=(a<3?o(s):a>3?o(t,r,s):o(t,r))||s);return a>3&&s&&Object.defineProperty(t,r,s),s}function u(e,t){return function(r,n){t(r,n,e)}}function p(e,t,r,n,o,a){function s(e){if(void 0!==e&&"function"!=typeof e)throw TypeError("Function expected");return e}for(var i,l=n.kind,c="getter"===l?"get":"setter"===l?"set":"value",u=!t&&e?n.static?e:e.prototype:null,p=t||(u?Object.getOwnPropertyDescriptor(u,n.name):{}),f=!1,d=r.length-1;d>=0;d--){var h={};for(var m in n)h[m]="access"===m?{}:n[m];for(var m in n.access)h.access[m]=n.access[m];h.addInitializer=function(e){if(f)throw TypeError("Cannot add initializers after decoration has completed");a.push(s(e||null))};var v=(0,r[d])("accessor"===l?{get:p.get,set:p.set}:p[c],h);if("accessor"===l){if(void 0===v)continue;if(null===v||"object"!=typeof v)throw TypeError("Object expected");(i=s(v.get))&&(p.get=i),(i=s(v.set))&&(p.set=i),(i=s(v.init))&&o.unshift(i)}else(i=s(v))&&("field"===l?o.unshift(i):p[c]=i)}u&&Object.defineProperty(u,n.name,p),f=!0}function f(e,t,r){for(var n=arguments.length>2,o=0;o<t.length;o++)r=n?t[o].call(e,r):t[o].call(e);return n?r:void 0}function d(e){return(void 0===e?"undefined":(0,o._)(e))==="symbol"?e:"".concat(e)}function h(e,t,r){return(void 0===t?"undefined":(0,o._)(t))==="symbol"&&(t=t.description?"[".concat(t.description,"]"):""),Object.defineProperty(e,"name",{configurable:!0,value:r?"".concat(r," ",t):t})}function m(e,t){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(e,t)}function v(e,t,r,n){return new(r||(r=Promise))(function(o,a){function s(e){try{l(n.next(e))}catch(e){a(e)}}function i(e){try{l(n.throw(e))}catch(e){a(e)}}function l(e){var t;e.done?o(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(s,i)}l((n=n.apply(e,t||[])).next())})}function g(e,t){var r,n,o,a,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return a={next:i(0),throw:i(1),return:i(2)},"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function i(i){return function(l){return function(i){if(r)throw TypeError("Generator is already executing.");for(;a&&(a=0,i[0]&&(s=0)),s;)try{if(r=1,n&&(o=2&i[0]?n.return:i[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,i[1])).done)return o;switch(n=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return s.label++,{value:i[1],done:!1};case 5:s.label++,n=i[1],i=[0];continue;case 7:i=s.ops.pop(),s.trys.pop();continue;default:if(!(o=(o=s.trys).length>0&&o[o.length-1])&&(6===i[0]||2===i[0])){s=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){s.label=i[1];break}if(6===i[0]&&s.label<o[1]){s.label=o[1],o=i;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(i);break}o[2]&&s.ops.pop(),s.trys.pop();continue}i=t.call(e,s)}catch(e){i=[6,e],n=0}finally{r=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,l])}}}var _=Object.create?function(e,t,r,n){void 0===n&&(n=r);var o=Object.getOwnPropertyDescriptor(t,r);(!o||("get"in o?!t.__esModule:o.writable||o.configurable))&&(o={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,o)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]};function y(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||_(t,e,r)}function b(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function w(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,a=r.call(e),s=[];try{for(;(void 0===t||t-- >0)&&!(n=a.next()).done;)s.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=a.return)&&r.call(a)}finally{if(o)throw o.error}}return s}function x(){for(var e=[],t=0;t<arguments.length;t++)e=e.concat(w(arguments[t]));return e}function j(){for(var e=0,t=0,r=arguments.length;t<r;t++)e+=arguments[t].length;for(var n=Array(e),o=0,t=0;t<r;t++)for(var a=arguments[t],s=0,i=a.length;s<i;s++,o++)n[o]=a[s];return n}function k(e,t,r){if(r||2==arguments.length)for(var n,o=0,a=t.length;o<a;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))}function T(e){return this instanceof T?(this.v=e,this):new T(e)}function S(e,t,r){if(!Symbol.asyncIterator)throw TypeError("Symbol.asyncIterator is not defined.");var n,o=r.apply(e,t||[]),a=[];return n={},s("next"),s("throw"),s("return",function(e){return function(t){return Promise.resolve(t).then(e,c)}}),n[Symbol.asyncIterator]=function(){return this},n;function s(e,t){o[e]&&(n[e]=function(t){return new Promise(function(r,n){a.push([e,t,r,n])>1||i(e,t)})},t&&(n[e]=t(n[e])))}function i(e,t){try{var r;(r=o[e](t)).value instanceof T?Promise.resolve(r.value.v).then(l,c):u(a[0][2],r)}catch(e){u(a[0][3],e)}}function l(e){i("next",e)}function c(e){i("throw",e)}function u(e,t){e(t),a.shift(),a.length&&i(a[0][0],a[0][1])}}function M(e){var t,r;return t={},n("next"),n("throw",function(e){throw e}),n("return"),t[Symbol.iterator]=function(){return this},t;function n(n,o){t[n]=e[n]?function(t){return(r=!r)?{value:T(e[n](t)),done:!1}:o?o(t):t}:o}}function I(e){if(!Symbol.asyncIterator)throw TypeError("Symbol.asyncIterator is not defined.");var t,r=e[Symbol.asyncIterator];return r?r.call(e):(e=b(e),t={},n("next"),n("throw"),n("return"),t[Symbol.asyncIterator]=function(){return this},t);function n(r){t[r]=e[r]&&function(t){return new Promise(function(n,o){!function(e,t,r,n){Promise.resolve(n).then(function(t){e({value:t,done:r})},t)}(n,o,(t=e[r](t)).done,t.value)})}}}function E(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e}var F=Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t};function C(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)"default"!==r&&Object.prototype.hasOwnProperty.call(e,r)&&_(t,e,r);return F(t,e),t}function R(e){return e&&e.__esModule?e:{default:e}}function O(e,t,r,n){if("a"===r&&!n)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?n:"a"===r?n.call(e):n?n.value:t.get(e)}function P(e,t,r,n,o){if("m"===n)throw TypeError("Private method is not writable");if("a"===n&&!o)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!o:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?o.call(e,r):o?o.value=r:t.set(e,r),r}function D(e,t){if(null===t||"object"!=typeof t&&"function"!=typeof t)throw TypeError("Cannot use 'in' operator on non-object");return"function"==typeof e?t===e:e.has(t)}function A(e,t,r){if(null!=t){var n,o;if("object"!=typeof t&&"function"!=typeof t)throw TypeError("Object expected.");if(r){if(!Symbol.asyncDispose)throw TypeError("Symbol.asyncDispose is not defined.");n=t[Symbol.asyncDispose]}if(void 0===n){if(!Symbol.dispose)throw TypeError("Symbol.dispose is not defined.");n=t[Symbol.dispose],r&&(o=n)}if("function"!=typeof n)throw TypeError("Object not disposable.");o&&(n=function(){try{o.call(this)}catch(e){return Promise.reject(e)}}),e.stack.push({value:t,dispose:n,async:r})}else r&&e.stack.push({async:!0});return t}var z="function"==typeof SuppressedError?SuppressedError:function(e,t,r){var n=Error(r);return n.name="SuppressedError",n.error=e,n.suppressed=t,n};function V(e){function t(t){e.error=e.hasError?new z(t,e.error,"An error was suppressed during disposal."):t,e.hasError=!0}return function r(){for(;e.stack.length;){var n=e.stack.pop();try{var o=n.dispose&&n.dispose.call(n.value);if(n.async)return Promise.resolve(o).then(r,function(e){return t(e),r()})}catch(e){t(e)}}if(e.hasError)throw e.error}()}r.default={__extends:s,__assign:i,__rest:l,__decorate:c,__param:u,__metadata:m,__awaiter:v,__generator:g,__createBinding:_,__exportStar:y,__values:b,__read:w,__spread:x,__spreadArrays:j,__spreadArray:k,__await:T,__asyncGenerator:S,__asyncDelegator:M,__asyncValues:I,__makeTemplateObject:E,__importStar:C,__importDefault:R,__classPrivateFieldGet:O,__classPrivateFieldSet:P,__classPrivateFieldIn:D,__addDisposableResource:A,__disposeResources:V}},{"@swc/helpers/_/_type_of":"l4kpM","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],gbKEc:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return a});var o=e("../utils");function a(e){var t=e.template.$video;(0,o.def)(e,"attr",{value:function(e,r){if(void 0===r)return t[e];t[e]=r}})}},{"../utils":"7esST","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],"4YToo":[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return i});var o=e("@swc/helpers/_/_async_to_generator"),a=e("@swc/helpers/_/_ts_generator"),s=e("../utils");function i(e){var t=e.i18n,r=e.notice,n=e.option,i=e.constructor.instances,l=e.template.$video;(0,s.def)(e,"play",{value:(0,o._)(function(){var o,s,c;return(0,a._)(this,function(a){switch(a.label){case 0:return[4,l.play()];case 1:if(o=a.sent(),r.show=t.get("Play"),e.emit("play"),n.mutex)for(s=0;s<i.length;s++)(c=i[s])!==e&&c.pause();return[2,o]}})})})}},{"@swc/helpers/_/_async_to_generator":"eFkS8","@swc/helpers/_/_ts_generator":"7FJ4U","../utils":"7esST","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],jJnYm:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return a});var o=e("../utils");function a(e){var t=e.template.$video,r=e.i18n,n=e.notice;(0,o.def)(e,"pause",{value:function(){var o=t.pause();return n.show=r.get("Pause"),e.emit("pause"),o}})}},{"../utils":"7esST","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],atYsy:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return a});var o=e("../utils");function a(e){(0,o.def)(e,"toggle",{value:function(){return e.playing?e.pause():e.play()}})}},{"../utils":"7esST","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],lsslL:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return a});var o=e("../utils");function a(e){var t=e.notice;(0,o.def)(e,"seek",{set:function(r){e.currentTime=r,e.emit("seek",e.currentTime),e.duration&&(t.show="".concat((0,o.secondToTime)(e.currentTime)," / ").concat((0,o.secondToTime)(e.duration)))}}),(0,o.def)(e,"forward",{set:function(t){e.seek=e.currentTime+t}}),(0,o.def)(e,"backward",{set:function(t){e.seek=e.currentTime-t}})}},{"../utils":"7esST","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],cSjxV:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return a});var o=e("../utils");function a(e){var t=e.template.$video,r=e.i18n,n=e.notice,a=e.storage;(0,o.def)(e,"volume",{get:function(){return t.volume||0},set:function(e){t.volume=(0,o.clamp)(e,0,1),n.show="".concat(r.get("Volume"),": ").concat(parseInt(100*t.volume,10)),0!==t.volume&&a.set("volume",t.volume)}}),(0,o.def)(e,"muted",{get:function(){return t.muted},set:function(r){t.muted=r,e.emit("muted",r)}})}},{"../utils":"7esST","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],"7NYVK":[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return a});var o=e("../utils");function a(e){var t=e.template.$video;(0,o.def)(e,"currentTime",{get:function(){return t.currentTime||0},set:function(r){Number.isNaN(r=parseFloat(r))||(t.currentTime=(0,o.clamp)(r,0,e.duration))}})}},{"../utils":"7esST","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],fW8lt:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return a});var o=e("../utils");function a(e){(0,o.def)(e,"duration",{get:function(){var t=e.template.$video.duration;return t===1/0?0:t||0}})}},{"../utils":"7esST","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],j5YX9:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return i});var o=e("@swc/helpers/_/_async_to_generator"),a=e("@swc/helpers/_/_ts_generator"),s=e("../utils");function i(e){function t(t,r){return new Promise(function(n,s){if(t!==e.url){var i=e.playing,l=e.aspectRatio,c=e.playbackRate;e.pause(),e.url=t,e.notice.show="",e.once("video:error",s),e.once("video:loadedmetadata",function(){e.currentTime=r}),e.once("video:canplay",(0,o._)(function(){return(0,a._)(this,function(t){switch(t.label){case 0:if(e.playbackRate=c,e.aspectRatio=l,!i)return[3,2];return[4,e.play()];case 1:t.sent(),t.label=2;case 2:return e.notice.show="",n(),[2]}})}))}})}(0,s.def)(e,"switchQuality",{value:function(r){return t(r,e.currentTime)}}),(0,s.def)(e,"switchUrl",{value:function(e){return t(e,0)}}),(0,s.def)(e,"switch",{set:e.switchUrl})}},{"@swc/helpers/_/_async_to_generator":"eFkS8","@swc/helpers/_/_ts_generator":"7FJ4U","../utils":"7esST","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],Pz0cc:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return a});var o=e("../utils");function a(e){var t=e.template.$video,r=e.i18n,n=e.notice;(0,o.def)(e,"playbackRate",{get:function(){return t.playbackRate},set:function(o){o?o!==t.playbackRate&&(t.playbackRate=o,n.show="".concat(r.get("Rate"),": ").concat(1===o?r.get("Normal"):"".concat(o,"x"))):e.playbackRate=1}})}},{"../utils":"7esST","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],ef8Wu:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return a});var o=e("../utils");function a(e){var t=e.i18n,r=e.notice,n=e.template,a=n.$video,s=n.$player;(0,o.def)(e,"aspectRatio",{get:function(){return s.dataset.aspectRatio||"default"},set:function(n){if(n||(n="default"),"default"===n)(0,o.setStyle)(a,"width",null),(0,o.setStyle)(a,"height",null),(0,o.setStyle)(a,"margin",null),delete s.dataset.aspectRatio;else{var i=n.split(":").map(Number),l=s.clientWidth,c=s.clientHeight,u=i[0]/i[1];l/c>u?((0,o.setStyle)(a,"width","".concat(u*c,"px")),(0,o.setStyle)(a,"height","100%"),(0,o.setStyle)(a,"margin","0 auto")):((0,o.setStyle)(a,"width","100%"),(0,o.setStyle)(a,"height","".concat(l/u,"px")),(0,o.setStyle)(a,"margin","auto 0")),s.dataset.aspectRatio=n}r.show="".concat(t.get("Aspect Ratio"),": ").concat("default"===n?t.get("Default"):n),e.emit("aspectRatio",n)}})}},{"../utils":"7esST","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],ca8va:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return i});var o=e("@swc/helpers/_/_async_to_generator"),a=e("@swc/helpers/_/_ts_generator"),s=e("../utils");function i(e){var t,r=e.notice,n=e.template.$video,i=(0,s.createElement)("canvas");(0,s.def)(e,"getDataURL",{value:function(){return new Promise(function(e,t){try{i.width=n.videoWidth,i.height=n.videoHeight,i.getContext("2d").drawImage(n,0,0),e(i.toDataURL("image/png"))}catch(e){r.show=e,t(e)}})}}),(0,s.def)(e,"getBlobUrl",{value:function(){return new Promise(function(e,t){try{i.width=n.videoWidth,i.height=n.videoHeight,i.getContext("2d").drawImage(n,0,0),i.toBlob(function(t){e(URL.createObjectURL(t))})}catch(e){r.show=e,t(e)}})}}),(0,s.def)(e,"screenshot",{value:(t=(0,o._)(function(t){var r,o;return(0,a._)(this,function(a){switch(a.label){case 0:return[4,e.getDataURL()];case 1:return r=a.sent(),o=t||"artplayer_".concat((0,s.secondToTime)(n.currentTime)),(0,s.download)(r,"".concat(o,".png")),e.emit("screenshot",r),[2,r]}})}),function(e){return t.apply(this,arguments)})})}},{"@swc/helpers/_/_async_to_generator":"eFkS8","@swc/helpers/_/_ts_generator":"7FJ4U","../utils":"7esST","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],alRzX:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return c});var o=e("@swc/helpers/_/_async_to_generator"),a=e("@swc/helpers/_/_ts_generator"),s=e("../libs/screenfull"),i=n.interopDefault(s),l=e("../utils");function c(e){var t=e.i18n,r=e.notice,n=e.template,s=n.$video,c=n.$player,u=function(e){(0,i.default).on("change",function(){e.emit("fullscreen",i.default.isFullscreen)}),(0,i.default).on("error",function(t){e.emit("fullscreenError",t)}),(0,l.def)(e,"fullscreen",{get:function(){return i.default.isFullscreen},set:function(t){return(0,o._)(function(){return(0,a._)(this,function(r){switch(r.label){case 0:if(!t)return[3,2];return e.state="fullscreen",[4,(0,i.default).request(c)];case 1:return r.sent(),(0,l.addClass)(c,"art-fullscreen"),[3,4];case 2:return[4,(0,i.default).exit()];case 3:r.sent(),(0,l.removeClass)(c,"art-fullscreen"),r.label=4;case 4:return e.emit("resize"),[2]}})})()}})},p=function(e){e.proxy(document,"webkitfullscreenchange",function(){e.emit("fullscreen",e.fullscreen),e.emit("resize")}),(0,l.def)(e,"fullscreen",{get:function(){return document.fullscreenElement===s},set:function(t){t?(e.state="fullscreen",s.webkitEnterFullscreen()):s.webkitExitFullscreen()}})};e.once("video:loadedmetadata",function(){i.default.isEnabled?u(e):s.webkitSupportsFullscreen?p(e):(0,l.def)(e,"fullscreen",{get:function(){return!1},set:function(){r.show=t.get("Fullscreen Not Supported")}}),(0,l.def)(e,"fullscreen",(0,l.get)(e,"fullscreen"))})}},{"@swc/helpers/_/_async_to_generator":"eFkS8","@swc/helpers/_/_ts_generator":"7FJ4U","../libs/screenfull":"4g0Hw","../utils":"7esST","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],"4g0Hw":[function(e,t,r){e("@parcel/transformer-js/src/esmodule-helpers.js").defineInteropFlag(r);var n=e("@swc/helpers/_/_sliced_to_array"),o=[["requestFullscreen","exitFullscreen","fullscreenElement","fullscreenEnabled","fullscreenchange","fullscreenerror"],["webkitRequestFullscreen","webkitExitFullscreen","webkitFullscreenElement","webkitFullscreenEnabled","webkitfullscreenchange","webkitfullscreenerror"],["webkitRequestFullScreen","webkitCancelFullScreen","webkitCurrentFullScreenElement","webkitCancelFullScreen","webkitfullscreenchange","webkitfullscreenerror"],["mozRequestFullScreen","mozCancelFullScreen","mozFullScreenElement","mozFullScreenEnabled","mozfullscreenchange","mozfullscreenerror"],["msRequestFullscreen","msExitFullscreen","msFullscreenElement","msFullscreenEnabled","MSFullscreenChange","MSFullscreenError"]],a=function(){if("undefined"==typeof document)return!1;var e=o[0],t={},r=!0,a=!1,s=void 0;try{for(var i,l=o[Symbol.iterator]();!(r=(i=l.next()).done);r=!0){var c=i.value;if(c[1]in document){var u=!0,p=!1,f=void 0;try{for(var d,h=c.entries()[Symbol.iterator]();!(u=(d=h.next()).done);u=!0){var m=(0,n._)(d.value,2),v=m[0],g=m[1];t[e[v]]=g}}catch(e){p=!0,f=e}finally{try{u||null==h.return||h.return()}finally{if(p)throw f}}return t}}}catch(e){a=!0,s=e}finally{try{r||null==l.return||l.return()}finally{if(a)throw s}}return!1}(),s={change:a.fullscreenchange,error:a.fullscreenerror},i={request:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document.documentElement,t=arguments.length>1?arguments[1]:void 0;return new Promise(function(r,n){var o=function(){i.off("change",o),r()};i.on("change",o);var s=e[a.requestFullscreen](t);s instanceof Promise&&s.then(o).catch(n)})},exit:function(){return new Promise(function(e,t){if(!i.isFullscreen){e();return}var r=function(){i.off("change",r),e()};i.on("change",r);var n=document[a.exitFullscreen]();n instanceof Promise&&n.then(r).catch(t)})},toggle:function(e,t){return i.isFullscreen?i.exit():i.request(e,t)},onchange:function(e){i.on("change",e)},onerror:function(e){i.on("error",e)},on:function(e,t){var r=s[e];r&&document.addEventListener(r,t,!1)},off:function(e,t){var r=s[e];r&&document.removeEventListener(r,t,!1)},raw:a};Object.defineProperties(i,{isFullscreen:{get:function(){return!!document[a.fullscreenElement]}},element:{enumerable:!0,get:function(){return document[a.fullscreenElement]}},isEnabled:{enumerable:!0,get:function(){return!!document[a.fullscreenEnabled]}}}),a||(i={isEnabled:!1}),r.default=i},{"@swc/helpers/_/_sliced_to_array":"8wTBg","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],"8wTBg":[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"_sliced_to_array",function(){return l}),n.export(r,"_",function(){return l});var o=e("./_array_with_holes.js"),a=e("./_iterable_to_array_limit.js"),s=e("./_non_iterable_rest.js"),i=e("./_unsupported_iterable_to_array.js");function l(e,t){return(0,o._array_with_holes)(e)||(0,a._iterable_to_array_limit)(e,t)||(0,i._unsupported_iterable_to_array)(e,t)||(0,s._non_iterable_rest)()}},{"./_array_with_holes.js":"4ct5h","./_iterable_to_array_limit.js":"eV4uK","./_non_iterable_rest.js":"bXqZd","./_unsupported_iterable_to_array.js":"2OTAn","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],"4ct5h":[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");function o(e){if(Array.isArray(e))return e}n.defineInteropFlag(r),n.export(r,"_array_with_holes",function(){return o}),n.export(r,"_",function(){return o})},{"@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],eV4uK:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");function o(e,t){var r,n,o=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=o){var a=[],s=!0,i=!1;try{for(o=o.call(e);!(s=(r=o.next()).done)&&(a.push(r.value),!t||a.length!==t);s=!0);}catch(e){i=!0,n=e}finally{try{s||null==o.return||o.return()}finally{if(i)throw n}}return a}}n.defineInteropFlag(r),n.export(r,"_iterable_to_array_limit",function(){return o}),n.export(r,"_",function(){return o})},{"@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],bXqZd:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");function o(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}n.defineInteropFlag(r),n.export(r,"_non_iterable_rest",function(){return o}),n.export(r,"_",function(){return o})},{"@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],"4er6h":[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return a});var o=e("../utils");function a(e){var t=e.constructor,r=e.template,n=r.$container,a=r.$player,s="";(0,o.def)(e,"fullscreenWeb",{get:function(){return(0,o.hasClass)(a,"art-fullscreen-web")},set:function(r){r?(s=a.style.cssText,t.FULLSCREEN_WEB_IN_BODY&&(0,o.append)(document.body,a),e.state="fullscreenWeb",(0,o.setStyle)(a,"width","100%"),(0,o.setStyle)(a,"height","100%"),(0,o.addClass)(a,"art-fullscreen-web"),e.emit("fullscreenWeb",!0)):(t.FULLSCREEN_WEB_IN_BODY&&(0,o.append)(n,a),s&&(a.style.cssText=s,s=""),(0,o.removeClass)(a,"art-fullscreen-web"),e.emit("fullscreenWeb",!1)),e.emit("resize")}})}},{"../utils":"7esST","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],jBv10:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return a});var o=e("../utils");function a(e){var t,r,n,a,s=e.i18n,i=e.notice,l=e.template.$video;document.pictureInPictureEnabled?(t=e.template.$video,r=e.proxy,n=e.notice,t.disablePictureInPicture=!1,(0,o.def)(e,"pip",{get:function(){return document.pictureInPictureElement},set:function(r){r?(e.state="pip",t.requestPictureInPicture().catch(function(e){throw n.show=e,e})):document.exitPictureInPicture().catch(function(e){throw n.show=e,e})}}),r(t,"enterpictureinpicture",function(){e.emit("pip",!0)}),r(t,"leavepictureinpicture",function(){e.emit("pip",!1)})):l.webkitSupportsPresentationMode?((a=e.template.$video).webkitSetPresentationMode("inline"),(0,o.def)(e,"pip",{get:function(){return"picture-in-picture"===a.webkitPresentationMode},set:function(t){t?(e.state="pip",a.webkitSetPresentationMode("picture-in-picture"),e.emit("pip",!0)):(a.webkitSetPresentationMode("inline"),e.emit("pip",!1))}})):(0,o.def)(e,"pip",{get:function(){return!1},set:function(){i.show=s.get("PIP Not Supported")}})}},{"../utils":"7esST","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],hQUyr:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return a});var o=e("../utils");function a(e){var t=e.template.$video;(0,o.def)(e,"loaded",{get:function(){return e.loadedTime/t.duration}}),(0,o.def)(e,"loadedTime",{get:function(){return t.buffered.length?t.buffered.end(t.buffered.length-1):0}})}},{"../utils":"7esST","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],"8OMWK":[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return a});var o=e("../utils");function a(e){(0,o.def)(e,"played",{get:function(){return e.currentTime/e.duration}})}},{"../utils":"7esST","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],btSPR:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return a});var o=e("../utils");function a(e){var t=e.template.$video;(0,o.def)(e,"playing",{get:function(){return!!(t.currentTime>0&&!t.paused&&!t.ended&&t.readyState>2)}})}},{"../utils":"7esST","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],dQH4Z:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return a});var o=e("../utils");function a(e){var t=e.template,r=t.$container,n=t.$player,a=t.$video;(0,o.def)(e,"autoSize",{value:function(){var t=a.videoWidth,s=a.videoHeight,i=(0,o.getRect)(r),l=i.width,c=i.height,u=t/s;l/c>u?((0,o.setStyle)(n,"width","".concat(c*u/l*100,"%")),(0,o.setStyle)(n,"height","100%")):((0,o.setStyle)(n,"width","100%"),(0,o.setStyle)(n,"height","".concat(l/u/c*100,"%"))),e.emit("autoSize",{width:e.width,height:e.height})}})}},{"../utils":"7esST","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],hPGBy:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return a});var o=e("../utils");function a(e){(0,o.def)(e,"rect",{get:function(){return(0,o.getRect)(e.template.$player)}});for(var t=["bottom","height","left","right","top","width"],r=0;r<t.length;r++)!function(r){var n=t[r];(0,o.def)(e,n,{get:function(){return e.rect[n]}})}(r);(0,o.def)(e,"x",{get:function(){return e.left+window.pageXOffset}}),(0,o.def)(e,"y",{get:function(){return e.top+window.pageYOffset}})}},{"../utils":"7esST","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],aVvjy:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return a});var o=e("../utils");function a(e){var t=e.template.$player,r=e.i18n,n=e.notice;(0,o.def)(e,"flip",{get:function(){return t.dataset.flip||"normal"},set:function(a){a||(a="normal"),"normal"===a?delete t.dataset.flip:t.dataset.flip=a,n.show="".concat(r.get("Video Flip"),": ").concat(r.get((0,o.capitalize)(a))),e.emit("flip",a)}})}},{"../utils":"7esST","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],juvfl:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return a});var o=e("../utils");function a(e){var t=e.icons,r=e.proxy,n=e.storage,a=e.template,s=a.$player,i=a.$video,l=!1,c=0,u=0;function p(){var t=e.template.$mini;t&&((0,o.removeClass)(s,"art-mini"),(0,o.setStyle)(t,"display","none"),s.prepend(i),e.emit("mini",!1))}function f(t,r){e.playing?((0,o.setStyle)(t,"display","none"),(0,o.setStyle)(r,"display","flex")):((0,o.setStyle)(t,"display","flex"),(0,o.setStyle)(r,"display","none"))}function d(){var t=e.template.$mini,r=(0,o.getRect)(t),a=window.innerHeight-r.height-50,s=window.innerWidth-r.width-50;n.set("top",a),n.set("left",s),(0,o.setStyle)(t,"top","".concat(a,"px")),(0,o.setStyle)(t,"left","".concat(s,"px"))}(0,o.def)(e,"mini",{get:function(){return(0,o.hasClass)(s,"art-mini")},set:function(a){if(a){e.state="mini",(0,o.addClass)(s,"art-mini");var h=function(){var a=e.template.$mini;if(a)return(0,o.append)(a,i),(0,o.setStyle)(a,"display","flex");var s=(0,o.createElement)("div");(0,o.addClass)(s,"art-mini-popup"),(0,o.append)(document.body,s),e.template.$mini=s,(0,o.append)(s,i);var d=(0,o.append)(s,'<div class="art-mini-close"></div>');(0,o.append)(d,t.close),r(d,"click",p);var h=(0,o.append)(s,'<div class="art-mini-state"></div>'),m=(0,o.append)(h,t.play),v=(0,o.append)(h,t.pause);return r(m,"click",function(){return e.play()}),r(v,"click",function(){return e.pause()}),f(m,v),e.on("video:playing",function(){return f(m,v)}),e.on("video:pause",function(){return f(m,v)}),e.on("video:timeupdate",function(){return f(m,v)}),r(s,"mousedown",function(e){l=0===e.button,c=e.pageX,u=e.pageY}),e.on("document:mousemove",function(e){if(l){(0,o.addClass)(s,"art-mini-droging");var t=e.pageX-c,r=e.pageY-u;(0,o.setStyle)(s,"transform","translate(".concat(t,"px, ").concat(r,"px)"))}}),e.on("document:mouseup",function(){if(l){l=!1,(0,o.removeClass)(s,"art-mini-droging");var e=(0,o.getRect)(s);n.set("left",e.left),n.set("top",e.top),(0,o.setStyle)(s,"left","".concat(e.left,"px")),(0,o.setStyle)(s,"top","".concat(e.top,"px")),(0,o.setStyle)(s,"transform",null)}}),s}(),m=n.get("top"),v=n.get("left");m&&v?((0,o.setStyle)(h,"top","".concat(m,"px")),(0,o.setStyle)(h,"left","".concat(v,"px")),(0,o.isInViewport)(h)||d()):d(),e.emit("mini",!0)}else p()}})}},{"../utils":"7esST","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],"2UAdD":[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return a});var o=e("../utils");function a(e){var t=e.template.$poster;(0,o.def)(e,"poster",{get:function(){try{return t.style.backgroundImage.match(/"(.*)"/)[1]}catch(e){return""}},set:function(e){(0,o.setStyle)(t,"backgroundImage","url(".concat(e,")"))}})}},{"../utils":"7esST","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],"377XR":[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return a});var o=e("../utils");function a(e){var t=e.template,r=t.$container,n=t.$video;(0,o.def)(e,"autoHeight",{value:function(){var t=r.clientWidth,a=n.videoHeight,s=t/n.videoWidth*a;(0,o.setStyle)(r,"height",s+"px"),e.emit("autoHeight",s)}})}},{"../utils":"7esST","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],afCNS:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return a});var o=e("../utils");function a(e){var t=e.template.$player;(0,o.def)(e,"cssVar",{value:function(e,r){return r?t.style.setProperty(e,r):getComputedStyle(t).getPropertyValue(e)}})}},{"../utils":"7esST","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],"7l7GX":[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return a});var o=e("../utils");function a(e){(0,o.def)(e,"theme",{get:function(){return e.cssVar("--art-theme")},set:function(t){e.cssVar("--art-theme",t)}})}},{"../utils":"7esST","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],"3Vlhc":[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return a});var o=e("../utils");function a(e){(0,o.def)(e,"type",{get:function(){return e.option.type},set:function(t){e.option.type=t}})}},{"../utils":"7esST","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],ey2W0:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return a});var o=e("../utils");function a(e){var t=["mini","pip","fullscreen","fullscreenWeb"];(0,o.def)(e,"state",{get:function(){return t.find(function(t){return e[t]})||"standard"},set:function(r){for(var n=0;n<t.length;n++){var o=t[n];o!==r&&e[o]&&(e[o]=!1)}}})}},{"../utils":"7esST","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],kDZ8v:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return a});var o=e("../utils");function a(e){var t=e.constructor.utils.clamp,r=e.notice,n=e.template,a=e.i18n,s=0,i=[];e.on("subtitle:switch",function(){i=[]}),(0,o.def)(e,"subtitleOffset",{get:function(){return s},set:function(o){if(n.$track&&n.$track.track){var l=Array.from(n.$track.track.cues);s=t(o,-5,5);for(var c=0;c<l.length;c++){var u=l[c];i[c]||(i[c]={startTime:u.startTime,endTime:u.endTime}),u.startTime=t(i[c].startTime+s,0,e.duration),u.endTime=t(i[c].endTime+s,0,e.duration)}e.subtitle.update(),r.show="".concat(a.get("Subtitle Offset"),": ").concat(o,"s"),e.emit("subtitleOffset",o)}else e.emit("subtitleOffset",0)}})}},{"../utils":"7esST","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],bXTl1:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return a});var o=e("../utils");function a(e){var t=e.i18n,r=e.notice,n=e.proxy,a=e.template.$video,s=!0;window.WebKitPlaybackTargetAvailabilityEvent&&a.webkitShowPlaybackTargetPicker?n(a,"webkitplaybacktargetavailabilitychanged",function(e){switch(e.availability){case"available":s=!0;break;case"not-available":s=!1}}):s=!1,(0,o.def)(e,"airplay",{value:function(){s?(a.webkitShowPlaybackTargetPicker(),e.emit("airplay")):r.show=t.get("AirPlay Not Available")}})}},{"../utils":"7esST","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],hzrnC:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return i});var o=e("@swc/helpers/_/_async_to_generator"),a=e("@swc/helpers/_/_ts_generator"),s=e("../utils");function i(e){(0,s.def)(e,"quality",{set:function(t){var r=e.controls,n=e.notice,s=e.i18n,i=t.find(function(e){return e.default})||t[0];r.update({name:"quality",position:"right",index:10,style:{marginRight:"10px"},html:i?i.html:"",selector:t,onSelect:function(t){return(0,o._)(function(){return(0,a._)(this,function(r){switch(r.label){case 0:return[4,e.switchQuality(t.url)];case 1:return r.sent(),n.show="".concat(s.get("Switch Video"),": ").concat(t.html),[2]}})})()}})}})}},{"@swc/helpers/_/_async_to_generator":"eFkS8","@swc/helpers/_/_ts_generator":"7FJ4U","../utils":"7esST","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],"5oRuL":[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return i});var o=e("@swc/helpers/_/_async_to_generator"),a=e("@swc/helpers/_/_ts_generator"),s=e("../utils");function i(e){var t,r=e.option,n=e.events.loadImg,i=e.template,l=i.$progress,c=i.$video,u=null,p=null,f=!1,d=!1;e.on("setBar",(t=(0,o._)(function(t,o,i){var h,m,v,g,_;return(0,a._)(this,function(a){switch(a.label){case 0:if(m=null===(h=e.controls)||void 0===h?void 0:h.thumbnails,v=r.thumbnails.url,!m||!v)return[2];if(g="played"===t&&i&&s.isMobile,!("hover"===t||g))return[3,3];if(f)return[3,2];return f=!0,[4,n(v)];case 1:p=a.sent(),d=!0,a.label=2;case 2:if(!d)return[2];_=l.clientWidth*o,(0,s.setStyle)(m,"display","flex"),_>0&&_<l.clientWidth?function(t){var n,o=null===(n=e.controls)||void 0===n?void 0:n.thumbnails;if(o){var a=r.thumbnails,i=a.url,u=a.number,f=a.column,d=a.width,h=a.height,m=d||p.naturalWidth/f,v=h||m/(c.videoWidth/c.videoHeight),g=Math.floor(t/(l.clientWidth/u)),_=Math.ceil(g/f)-1;(0,s.setStyle)(o,"backgroundImage","url(".concat(i,")")),(0,s.setStyle)(o,"height","".concat(v,"px")),(0,s.setStyle)(o,"width","".concat(m,"px")),(0,s.setStyle)(o,"backgroundPosition","-".concat((g%f||f-1)*m,"px -").concat(_*v,"px")),t<=m/2?(0,s.setStyle)(o,"left",0):t>l.clientWidth-m/2?(0,s.setStyle)(o,"left","".concat(l.clientWidth-m,"px")):(0,s.setStyle)(o,"left","".concat(t-m/2,"px"))}}(_):s.isMobile||(0,s.setStyle)(m,"display","none"),g&&(clearTimeout(u),u=setTimeout(function(){(0,s.setStyle)(m,"display","none")},500)),a.label=3;case 3:return[2]}})}),function(e,r,n){return t.apply(this,arguments)})),(0,s.def)(e,"thumbnails",{get:function(){return e.option.thumbnails},set:function(t){t.url&&!e.option.isLive&&(e.option.thumbnails=t,clearTimeout(u),u=null,p=null,f=!1,d=!1)}})}},{"@swc/helpers/_/_async_to_generator":"eFkS8","@swc/helpers/_/_ts_generator":"7FJ4U","../utils":"7esST","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],g9kWX:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return a});var o=e("../utils");function a(e){var t=e.option,r=e.storage,n=e.template,a=n.$video,s=n.$poster;for(var i in t.moreVideoAttr)e.attr(i,t.moreVideoAttr[i]);t.muted&&(e.muted=t.muted),t.volume&&(a.volume=(0,o.clamp)(t.volume,0,1));var l=r.get("volume");for(var c in"number"==typeof l&&(a.volume=(0,o.clamp)(l,0,1)),t.poster&&(0,o.setStyle)(s,"backgroundImage","url(".concat(t.poster,")")),t.autoplay&&(a.autoplay=t.autoplay),t.playsInline&&(a.playsInline=!0,a["webkit-playsinline"]=!0),t.theme&&(t.cssVar["--art-theme"]=t.theme),t.cssVar)e.cssVar(c,t.cssVar[c]);e.url=t.url}},{"../utils":"7esST","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],lgPrh:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return c});var o=e("@swc/helpers/_/_async_to_generator"),a=e("@swc/helpers/_/_ts_generator"),s=e("../config"),i=n.interopDefault(s),l=e("../utils");function c(e){for(var t,r=e.i18n,n=e.notice,s=e.option,c=e.constructor,u=e.proxy,p=e.template,f=p.$player,d=p.$video,h=p.$poster,m=0,v=0;v<i.default.events.length;v++)u(d,i.default.events[v],function(t){e.emit("video:".concat(t.type),t)});e.on("video:canplay",function(){m=0,e.loading.show=!1}),e.once("video:canplay",function(){e.loading.show=!1,e.controls.show=!0,e.mask.show=!0,e.isReady=!0,e.emit("ready")}),e.on("video:ended",function(){s.loop?(e.seek=0,e.play(),e.controls.show=!1,e.mask.show=!1):(e.controls.show=!0,e.mask.show=!0)}),e.on("video:error",(t=(0,o._)(function(t){return(0,a._)(this,function(o){switch(o.label){case 0:if(!(m<c.RECONNECT_TIME_MAX))return[3,2];return[4,(0,l.sleep)(c.RECONNECT_SLEEP_TIME)];case 1:return o.sent(),m+=1,e.url=s.url,n.show="".concat(r.get("Reconnect"),": ").concat(m),e.emit("error",t,m),[3,4];case 2:return e.mask.show=!0,e.loading.show=!1,e.controls.show=!0,(0,l.addClass)(f,"art-error"),[4,(0,l.sleep)(c.RECONNECT_SLEEP_TIME)];case 3:o.sent(),n.show=r.get("Video Load Failed"),o.label=4;case 4:return[2]}})}),function(e){return t.apply(this,arguments)})),e.on("video:loadedmetadata",function(){e.emit("resize"),l.isMobile&&(e.loading.show=!1,e.controls.show=!0,e.mask.show=!0)}),e.on("video:loadstart",function(){e.loading.show=!0,e.mask.show=!1,e.controls.show=!0}),e.on("video:pause",function(){e.controls.show=!0,e.mask.show=!0}),e.on("video:play",function(){e.mask.show=!1,(0,l.setStyle)(h,"display","none")}),e.on("video:playing",function(){e.mask.show=!1}),e.on("video:progress",function(){e.playing&&(e.loading.show=!1)}),e.on("video:seeked",function(){e.loading.show=!1,e.mask.show=!0}),e.on("video:seeking",function(){e.loading.show=!0,e.mask.show=!1}),e.on("video:timeupdate",function(){e.mask.show=!1}),e.on("video:waiting",function(){e.loading.show=!0,e.mask.show=!1})}},{"@swc/helpers/_/_async_to_generator":"eFkS8","@swc/helpers/_/_ts_generator":"7FJ4U","../config":"1lpcp","../utils":"7esST","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],"7Cphn":[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return O});var o=e("@swc/helpers/_/_class_call_check"),a=e("@swc/helpers/_/_create_class"),s=e("@swc/helpers/_/_get"),i=e("@swc/helpers/_/_get_prototype_of"),l=e("@swc/helpers/_/_inherits"),c=e("@swc/helpers/_/_create_super"),u=e("../utils"),p=e("../utils/component"),f=n.interopDefault(p),d=e("./fullscreen"),h=n.interopDefault(d),m=e("./fullscreenWeb"),v=n.interopDefault(m),g=e("./pip"),_=n.interopDefault(g),y=e("./playAndPause"),b=n.interopDefault(y),w=e("./progress"),x=n.interopDefault(w),j=e("./time"),k=n.interopDefault(j),T=e("./volume"),S=n.interopDefault(T),M=e("./setting"),I=n.interopDefault(M),E=e("./screenshot"),F=n.interopDefault(E),C=e("./airplay"),R=n.interopDefault(C),O=function(e){(0,l._)(r,e);var t=(0,c._)(r);function r(e){(0,o._)(this,r),(n=t.call(this,e)).isHover=!1,n.name="control",n.timer=Date.now();var n,a=e.constructor,s=n.art.template,i=s.$player,l=s.$bottom;return e.on("mousemove",function(){u.isMobile||(n.show=!0)}),e.on("click",function(){u.isMobile?n.toggle():n.show=!0}),e.on("document:mousemove",function(e){n.isHover=(0,u.includeFromEvent)(e,l)}),e.on("video:timeupdate",function(){!e.setting.show&&!n.isHover&&!e.isInput&&e.playing&&n.show&&Date.now()-n.timer>=a.CONTROL_HIDE_TIME&&(n.show=!1)}),e.on("control",function(e){e?((0,u.removeClass)(i,"art-hide-cursor"),(0,u.addClass)(i,"art-hover"),n.timer=Date.now()):((0,u.addClass)(i,"art-hide-cursor"),(0,u.removeClass)(i,"art-hover"))}),n.init(),n}return(0,a._)(r,[{key:"init",value:function(){var e=this,t=this.art.option;t.isLive||this.add((0,x.default)({name:"progress",position:"top",index:10})),this.add({name:"thumbnails",position:"top",index:20}),this.add((0,b.default)({name:"playAndPause",position:"left",index:10})),this.add((0,S.default)({name:"volume",position:"left",index:20})),t.isLive||this.add((0,k.default)({name:"time",position:"left",index:30})),t.quality.length&&(0,u.sleep)().then(function(){e.art.quality=t.quality}),t.screenshot&&!u.isMobile&&this.add((0,F.default)({name:"screenshot",position:"right",index:20})),t.setting&&this.add((0,I.default)({name:"setting",position:"right",index:30})),t.pip&&this.add((0,_.default)({name:"pip",position:"right",index:40})),t.airplay&&window.WebKitPlaybackTargetAvailabilityEvent&&this.add((0,R.default)({name:"airplay",position:"right",index:50})),t.fullscreenWeb&&this.add((0,v.default)({name:"fullscreenWeb",position:"right",index:60})),t.fullscreen&&this.add((0,h.default)({name:"fullscreen",position:"right",index:70}));for(var r=0;r<t.controls.length;r++)this.add(t.controls[r])}},{key:"add",value:function(e){var t="function"==typeof e?e(this.art):e,n=this.art.template,o=n.$progress,a=n.$controlsLeft,l=n.$controlsRight;switch(t.position){case"top":this.$parent=o;break;case"left":this.$parent=a;break;case"right":this.$parent=l;break;default:(0,u.errorHandle)(!1,"Control option.position must one of 'top', 'left', 'right'")}(0,s._)((0,i._)(r.prototype),"add",this).call(this,t)}}]),r}(f.default)},{"@swc/helpers/_/_class_call_check":"dRqgV","@swc/helpers/_/_create_class":"8RAzW","@swc/helpers/_/_get":"cUjhx","@swc/helpers/_/_get_prototype_of":"2mh6E","@swc/helpers/_/_inherits":"aRPJQ","@swc/helpers/_/_create_super":"kqTtK","../utils":"7esST","../utils/component":"cVtDw","./fullscreen":"fDyat","./fullscreenWeb":"KD2br","./pip":"hkN0J","./playAndPause":"jrPXg","./progress":"EmoUd","./time":"i5g4S","./volume":"aatHt","./setting":"k1U7y","./screenshot":"eocyV","./airplay":"Dlr2L","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],cUjhx:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"_get",function(){return a}),n.export(r,"_",function(){return a});var o=e("./_super_prop_base.js");function a(e,t,r){return(a="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(e,t,r){var n=(0,o._super_prop_base)(e,t);if(n){var a=Object.getOwnPropertyDescriptor(n,t);return a.get?a.get.call(r||e):a.value}})(e,t,r||e)}},{"./_super_prop_base.js":"3GVlP","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],"3GVlP":[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"_super_prop_base",function(){return a}),n.export(r,"_",function(){return a});var o=e("./_get_prototype_of.js");function a(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=(0,o._get_prototype_of)(e)););return e}},{"./_get_prototype_of.js":"2mh6E","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],cVtDw:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return h});var o=e("@swc/helpers/_/_async_to_generator"),a=e("@swc/helpers/_/_class_call_check"),s=e("@swc/helpers/_/_create_class"),i=e("@swc/helpers/_/_ts_generator"),l=e("./dom"),c=e("./format"),u=e("./error"),p=e("option-validator"),f=n.interopDefault(p),d=e("../scheme"),h=function(){function e(t){(0,a._)(this,e),this.id=0,this.art=t,this.cache=new Map,this.add=this.add.bind(this),this.remove=this.remove.bind(this),this.update=this.update.bind(this)}return(0,s._)(e,[{key:"show",get:function(){return(0,l.hasClass)(this.art.template.$player,"art-".concat(this.name,"-show"))},set:function(e){var t=this.art.template.$player,r="art-".concat(this.name,"-show");e?(0,l.addClass)(t,r):(0,l.removeClass)(t,r),this.art.emit(this.name,e)}},{key:"toggle",value:function(){this.show=!this.show}},{key:"add",value:function(e){var t=this,r="function"==typeof e?e(this.art):e;if(r.html=r.html||"",(0,f.default)(r,d.ComponentOption),this.$parent&&this.name&&!r.disable){var n=r.name||"".concat(this.name).concat(this.id),o=this.cache.get(n);(0,u.errorHandle)(!o,"Can't add an existing [".concat(n,"] to the [").concat(this.name,"]")),this.id+=1;var a=(0,l.createElement)("div");(0,l.addClass)(a,"art-".concat(this.name)),(0,l.addClass)(a,"art-".concat(this.name,"-").concat(n));var s=Array.from(this.$parent.children);a.dataset.index=r.index||this.id;var i=s.find(function(e){return Number(e.dataset.index)>=Number(a.dataset.index)});i?i.insertAdjacentElement("beforebegin",a):(0,l.append)(this.$parent,a),r.html&&(0,l.append)(a,r.html),r.style&&(0,l.setStyles)(a,r.style),r.tooltip&&(0,l.tooltip)(a,r.tooltip);var c=[];if(r.click){var p=this.art.events.proxy(a,"click",function(e){e.preventDefault(),r.click.call(t.art,t,e)});c.push(p)}return r.selector&&["left","right"].includes(r.position)&&this.addSelector(r,a,c),this[n]=a,this.cache.set(n,{$ref:a,events:c,option:r}),r.mounted&&r.mounted.call(this.art,a),a}}},{key:"addSelector",value:function(e,t,r){var n,a=this.art.events,s=a.hover,u=a.proxy;(0,l.addClass)(t,"art-control-selector");var p=(0,l.createElement)("div");(0,l.addClass)(p,"art-selector-value"),(0,l.append)(p,e.html),t.innerText="",(0,l.append)(t,p);var f=e.selector.map(function(e,t){return'<div class="art-selector-item '.concat(e.default?"art-current":"",'" data-index="').concat(t,'">').concat(e.html,"</div>")}).join(""),d=(0,l.createElement)("div");(0,l.addClass)(d,"art-selector-list"),(0,l.append)(d,f),(0,l.append)(t,d);var h=function(){var e=(0,l.getStyle)(t,"width"),r=(0,l.getStyle)(d,"width");d.style.left="".concat(e/2-r/2,"px")};s(t,h);var m=this,v=u(d,"click",(n=(0,o._)(function(t){var r,n,o,a;return(0,i._)(this,function(s){switch(s.label){case 0:if(!(r=(t.composedPath()||[]).find(function(e){return(0,l.hasClass)(e,"art-selector-item")})))return[2];if((0,l.inverseClass)(r,"art-current"),n=Number(r.dataset.index),o=e.selector[n]||{},p.innerText=r.innerText,!e.onSelect)return[3,2];return[4,e.onSelect.call(m.art,o,r,t)];case 1:a=s.sent(),(0,c.isStringOrNumber)(a)&&(p.innerHTML=a),s.label=2;case 2:return h(),[2]}})}),function(e){return n.apply(this,arguments)}));r.push(v)}},{key:"remove",value:function(e){var t=this.cache.get(e);(0,u.errorHandle)(t,"Can't find [".concat(e,"] from the [").concat(this.name,"]")),t.option.beforeUnmount&&t.option.beforeUnmount.call(this.art,t.$ref);for(var r=0;r<t.events.length;r++)this.art.events.remove(t.events[r]);this.cache.delete(e),delete this[e],(0,l.remove)(t.$ref)}},{key:"update",value:function(e){var t=this.cache.get(e.name);return t&&(e=Object.assign(t.option,e),this.remove(e.name)),this.add(e)}}]),e}()},{"@swc/helpers/_/_async_to_generator":"eFkS8","@swc/helpers/_/_class_call_check":"dRqgV","@swc/helpers/_/_create_class":"8RAzW","@swc/helpers/_/_ts_generator":"7FJ4U","./dom":"7UlmS","./format":"eSCih","./error":"d6sk8","option-validator":"gEtej","../scheme":"hYXnH","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],fDyat:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return i});var o=e("@swc/helpers/_/_object_spread"),a=e("@swc/helpers/_/_object_spread_props"),s=e("../utils");function i(e){return function(t){return(0,a._)((0,o._)({},e),{tooltip:t.i18n.get("Fullscreen"),mounted:function(e){var r=t.proxy,n=t.icons,o=t.i18n,a=(0,s.append)(e,n.fullscreenOn),i=(0,s.append)(e,n.fullscreenOff);(0,s.setStyle)(i,"display","none"),r(e,"click",function(){t.fullscreen=!t.fullscreen}),t.on("fullscreen",function(t){t?((0,s.tooltip)(e,o.get("Exit Fullscreen")),(0,s.setStyle)(a,"display","none"),(0,s.setStyle)(i,"display","inline-flex")):((0,s.tooltip)(e,o.get("Fullscreen")),(0,s.setStyle)(a,"display","inline-flex"),(0,s.setStyle)(i,"display","none"))})}})}}},{"@swc/helpers/_/_object_spread":"g42Vq","@swc/helpers/_/_object_spread_props":"cTIVI","../utils":"7esST","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],KD2br:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return i});var o=e("@swc/helpers/_/_object_spread"),a=e("@swc/helpers/_/_object_spread_props"),s=e("../utils");function i(e){return function(t){return(0,a._)((0,o._)({},e),{tooltip:t.i18n.get("Web Fullscreen"),mounted:function(e){var r=t.proxy,n=t.icons,o=t.i18n,a=(0,s.append)(e,n.fullscreenWebOn),i=(0,s.append)(e,n.fullscreenWebOff);(0,s.setStyle)(i,"display","none"),r(e,"click",function(){t.fullscreenWeb=!t.fullscreenWeb}),t.on("fullscreenWeb",function(t){t?((0,s.tooltip)(e,o.get("Exit Web Fullscreen")),(0,s.setStyle)(a,"display","none"),(0,s.setStyle)(i,"display","inline-flex")):((0,s.tooltip)(e,o.get("Web Fullscreen")),(0,s.setStyle)(a,"display","inline-flex"),(0,s.setStyle)(i,"display","none"))})}})}}},{"@swc/helpers/_/_object_spread":"g42Vq","@swc/helpers/_/_object_spread_props":"cTIVI","../utils":"7esST","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],hkN0J:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return i});var o=e("@swc/helpers/_/_object_spread"),a=e("@swc/helpers/_/_object_spread_props"),s=e("../utils");function i(e){return function(t){return(0,a._)((0,o._)({},e),{tooltip:t.i18n.get("PIP Mode"),mounted:function(e){var r=t.proxy,n=t.icons,o=t.i18n;(0,s.append)(e,n.pip),r(e,"click",function(){t.pip=!t.pip}),t.on("pip",function(t){(0,s.tooltip)(e,o.get(t?"Exit PIP Mode":"PIP Mode"))})}})}}},{"@swc/helpers/_/_object_spread":"g42Vq","@swc/helpers/_/_object_spread_props":"cTIVI","../utils":"7esST","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],jrPXg:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return i});var o=e("@swc/helpers/_/_object_spread"),a=e("@swc/helpers/_/_object_spread_props"),s=e("../utils");function i(e){return function(t){return(0,a._)((0,o._)({},e),{mounted:function(e){var r=t.proxy,n=t.icons,o=t.i18n,a=(0,s.append)(e,n.play),i=(0,s.append)(e,n.pause);function l(){(0,s.setStyle)(a,"display","flex"),(0,s.setStyle)(i,"display","none")}function c(){(0,s.setStyle)(a,"display","none"),(0,s.setStyle)(i,"display","flex")}(0,s.tooltip)(a,o.get("Play")),(0,s.tooltip)(i,o.get("Pause")),r(a,"click",function(){t.play()}),r(i,"click",function(){t.pause()}),t.playing?c():l(),t.on("video:playing",function(){c()}),t.on("video:pause",function(){l()})}})}}},{"@swc/helpers/_/_object_spread":"g42Vq","@swc/helpers/_/_object_spread_props":"cTIVI","../utils":"7esST","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],EmoUd:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"getPosFromEvent",function(){return i}),n.export(r,"setCurrentTime",function(){return l}),n.export(r,"default",function(){return c});var o=e("@swc/helpers/_/_object_spread"),a=e("@swc/helpers/_/_object_spread_props"),s=e("../utils");function i(e,t){var r=e.template.$progress,n=(0,s.getRect)(r).left,o=s.isMobile?t.touches[0].clientX:t.clientX,a=(0,s.clamp)(o-n,0,r.clientWidth),i=a/r.clientWidth*e.duration,l=(0,s.secondToTime)(i),c=(0,s.clamp)(a/r.clientWidth,0,1);return{second:i,time:l,width:a,percentage:c}}function l(e,t){if(e.isRotate){var r=t.touches[0].clientY/e.height,n=r*e.duration;e.emit("setBar","played",r,t),e.seek=n}else{var o=i(e,t),a=o.second,s=o.percentage;e.emit("setBar","played",s,t),e.seek=a}}function c(e){return function(t){var r=t.icons,n=t.option,c=t.proxy;return(0,a._)((0,o._)({},e),{html:'\n <div class="art-control-progress-inner">\n<div class="art-progress-hover"></div>\n<div class="art-progress-loaded"></div>\n<div class="art-progress-played"></div>\n<div class="art-progress-highlight"></div>\n<div class="art-progress-indicator"></div>\n<div class="art-progress-tip"></div>\n</div>\n ',mounted:function(e){var o=null,a=!1,u=(0,s.query)(".art-progress-hover",e),p=(0,s.query)(".art-progress-loaded",e),f=(0,s.query)(".art-progress-played",e),d=(0,s.query)(".art-progress-highlight",e),h=(0,s.query)(".art-progress-indicator",e),m=(0,s.query)(".art-progress-tip",e);function v(r,n){var o=n||i(t,r),a=o.width,l=o.time;m.innerText=l;var c=m.clientWidth;a<=c/2?(0,s.setStyle)(m,"left",0):a>e.clientWidth-c/2?(0,s.setStyle)(m,"left","".concat(e.clientWidth-c,"px")):(0,s.setStyle)(m,"left","".concat(a-c/2,"px"))}r.indicator?(0,s.append)(h,r.indicator):(0,s.setStyle)(h,"backgroundColor","var(--art-theme)"),t.on("setBar",function(r,n,a){var i="played"===r&&a&&s.isMobile;"loaded"===r&&(0,s.setStyle)(p,"width","".concat(100*n,"%")),"hover"===r&&(0,s.setStyle)(u,"width","".concat(100*n,"%")),"played"===r&&((0,s.setStyle)(f,"width","".concat(100*n,"%")),(0,s.setStyle)(h,"left","".concat(100*n,"%"))),i&&((0,s.setStyle)(m,"display","flex"),v(a,{width:e.clientWidth*n,time:(0,s.secondToTime)(n*t.duration)}),clearTimeout(o),o=setTimeout(function(){(0,s.setStyle)(m,"display","none")},500))}),t.on("video:loadedmetadata",function(){d.innerText="";for(var e=0;e<n.highlight.length;e++){var r=n.highlight[e],o=(0,s.clamp)(r.time,0,t.duration)/t.duration*100,a='<span data-text="'.concat(r.text,'" data-time="').concat(r.time,'" style="left: ').concat(o,'%"></span>');(0,s.append)(d,a)}}),t.on("video:progress",function(){t.emit("setBar","loaded",t.loaded)}),t.constructor.USE_RAF?t.on("raf",function(){t.emit("setBar","played",t.played)}):t.on("video:timeupdate",function(){t.emit("setBar","played",t.played)}),t.on("video:ended",function(){t.emit("setBar","played",1)}),t.emit("setBar","loaded",t.loaded||0),s.isMobile||(c(e,"click",function(e){e.target!==h&&l(t,e)}),c(e,"mousemove",function(r){var n,o,a,l=i(t,r).percentage;(t.emit("setBar","hover",l,r),(0,s.setStyle)(m,"display","flex"),(0,s.includeFromEvent)(r,d))?(n=i(t,r).width,o=r.target.dataset.text,m.innerText=o,n<=(a=m.clientWidth)/2?(0,s.setStyle)(m,"left",0):n>e.clientWidth-a/2?(0,s.setStyle)(m,"left","".concat(e.clientWidth-a,"px")):(0,s.setStyle)(m,"left","".concat(n-a/2,"px"))):v(r)}),c(e,"mouseleave",function(e){(0,s.setStyle)(m,"display","none"),t.emit("setBar","hover",0,e)}),c(e,"mousedown",function(e){a=0===e.button}),t.on("document:mousemove",function(e){if(a){var r=i(t,e),n=r.second,o=r.percentage;t.emit("setBar","played",o,e),t.seek=n}}),t.on("document:mouseup",function(){a&&(a=!1)}))}})}}},{"@swc/helpers/_/_object_spread":"g42Vq","@swc/helpers/_/_object_spread_props":"cTIVI","../utils":"7esST","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],i5g4S:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return i});var o=e("@swc/helpers/_/_object_spread"),a=e("@swc/helpers/_/_object_spread_props"),s=e("../utils");function i(e){return function(t){return(0,a._)((0,o._)({},e),{style:s.isMobile?{fontSize:"12px",padding:"0 5px"}:{cursor:"auto",padding:"0 10px"},mounted:function(e){function r(){var r="".concat((0,s.secondToTime)(t.currentTime)," / ").concat((0,s.secondToTime)(t.duration));r!==e.innerText&&(e.innerText=r)}r();for(var n=["video:loadedmetadata","video:timeupdate","video:progress"],o=0;o<n.length;o++)t.on(n[o],r)}})}}},{"@swc/helpers/_/_object_spread":"g42Vq","@swc/helpers/_/_object_spread_props":"cTIVI","../utils":"7esST","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],aatHt:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return i});var o=e("@swc/helpers/_/_object_spread"),a=e("@swc/helpers/_/_object_spread_props"),s=e("../utils");function i(e){return function(t){return(0,a._)((0,o._)({},e),{mounted:function(e){var r=t.proxy,n=t.icons,o=(0,s.append)(e,n.volume),a=(0,s.append)(e,n.volumeClose),i=(0,s.append)(e,'<div class="art-volume-panel"></div>'),l=(0,s.append)(i,'<div class="art-volume-inner"></div>'),c=(0,s.append)(l,'<div class="art-volume-val"></div>'),u=(0,s.append)(l,'<div class="art-volume-slider"></div>'),p=(0,s.append)(u,'<div class="art-volume-handle"></div>'),f=(0,s.append)(p,'<div class="art-volume-loaded"></div>'),d=(0,s.append)(u,'<div class="art-volume-indicator"></div>');function h(e){var t=(0,s.getRect)(u),r=t.top,n=t.height;return 1-(e.clientY-r)/n}function m(){if(t.muted||0===t.volume)(0,s.setStyle)(o,"display","none"),(0,s.setStyle)(a,"display","flex"),(0,s.setStyle)(d,"top","100%"),(0,s.setStyle)(f,"top","100%"),c.innerText=0;else{var e=100*t.volume;(0,s.setStyle)(o,"display","flex"),(0,s.setStyle)(a,"display","none"),(0,s.setStyle)(d,"top","".concat(100-e,"%")),(0,s.setStyle)(f,"top","".concat(100-e,"%")),c.innerText=Math.floor(e)}}if(m(),t.on("video:volumechange",m),r(o,"click",function(){t.muted=!0}),r(a,"click",function(){t.muted=!1}),s.isMobile)(0,s.setStyle)(i,"display","none");else{var v=!1;r(u,"mousedown",function(e){v=0===e.button,t.volume=h(e)}),t.on("document:mousemove",function(e){v&&(t.muted=!1,t.volume=h(e))}),t.on("document:mouseup",function(){v&&(v=!1)})}}})}}},{"@swc/helpers/_/_object_spread":"g42Vq","@swc/helpers/_/_object_spread_props":"cTIVI","../utils":"7esST","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],k1U7y:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return i});var o=e("@swc/helpers/_/_object_spread"),a=e("@swc/helpers/_/_object_spread_props"),s=e("../utils");function i(e){return function(t){return(0,a._)((0,o._)({},e),{tooltip:t.i18n.get("Show Setting"),mounted:function(e){var r=t.proxy,n=t.icons,o=t.i18n;(0,s.append)(e,n.setting),r(e,"click",function(){t.setting.toggle(),t.setting.updateStyle()}),t.on("setting",function(t){(0,s.tooltip)(e,o.get(t?"Hide Setting":"Show Setting"))})}})}}},{"@swc/helpers/_/_object_spread":"g42Vq","@swc/helpers/_/_object_spread_props":"cTIVI","../utils":"7esST","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],eocyV:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return i});var o=e("@swc/helpers/_/_object_spread"),a=e("@swc/helpers/_/_object_spread_props"),s=e("../utils");function i(e){return function(t){return(0,a._)((0,o._)({},e),{tooltip:t.i18n.get("Screenshot"),mounted:function(e){var r=t.proxy,n=t.icons;(0,s.append)(e,n.screenshot),r(e,"click",function(){t.screenshot()})}})}}},{"@swc/helpers/_/_object_spread":"g42Vq","@swc/helpers/_/_object_spread_props":"cTIVI","../utils":"7esST","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],Dlr2L:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return i});var o=e("@swc/helpers/_/_object_spread"),a=e("@swc/helpers/_/_object_spread_props"),s=e("../utils");function i(e){return function(t){return(0,a._)((0,o._)({},e),{tooltip:t.i18n.get("AirPlay"),mounted:function(e){var r=t.proxy,n=t.icons;(0,s.append)(e,n.airplay),r(e,"click",function(){return t.airplay()})}})}}},{"@swc/helpers/_/_object_spread":"g42Vq","@swc/helpers/_/_object_spread_props":"cTIVI","../utils":"7esST","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],aH04y:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return j});var o=e("@swc/helpers/_/_class_call_check"),a=e("@swc/helpers/_/_create_class"),s=e("@swc/helpers/_/_inherits"),i=e("@swc/helpers/_/_create_super"),l=e("../utils"),c=e("../utils/component"),u=n.interopDefault(c),p=e("./playbackRate"),f=n.interopDefault(p),d=e("./aspectRatio"),h=n.interopDefault(d),m=e("./flip"),v=n.interopDefault(m),g=e("./info"),_=n.interopDefault(g),y=e("./version"),b=n.interopDefault(y),w=e("./close"),x=n.interopDefault(w),j=function(e){(0,s._)(r,e);var t=(0,i._)(r);function r(e){var n;return(0,o._)(this,r),(n=t.call(this,e)).name="contextmenu",n.$parent=e.template.$contextmenu,l.isMobile||n.init(),n}return(0,a._)(r,[{key:"init",value:function(){var e=this,t=this.art,r=t.option,n=t.proxy,o=t.template,a=o.$player,s=o.$contextmenu;r.playbackRate&&this.add((0,f.default)({name:"playbackRate",index:10})),r.aspectRatio&&this.add((0,h.default)({name:"aspectRatio",index:20})),r.flip&&this.add((0,v.default)({name:"flip",index:30})),this.add((0,_.default)({name:"info",index:40})),this.add((0,b.default)({name:"version",index:50})),this.add((0,x.default)({name:"close",index:60}));for(var i=0;i<r.contextmenu.length;i++)this.add(r.contextmenu[i]);n(a,"contextmenu",function(t){if(e.art.constructor.CONTEXTMENU){t.preventDefault(),e.show=!0;var r=t.clientX,n=t.clientY,o=(0,l.getRect)(a),i=o.height,c=o.width,u=o.left,p=o.top,f=(0,l.getRect)(s),d=f.height,h=f.width,m=r-u,v=n-p;r+h>u+c&&(m=c-h),n+d>p+i&&(v=i-d),(0,l.setStyles)(s,{top:"".concat(v,"px"),left:"".concat(m,"px")})}}),n(a,"click",function(t){(0,l.includeFromEvent)(t,s)||(e.show=!1)}),this.art.on("blur",function(){e.show=!1})}}]),r}(u.default)},{"@swc/helpers/_/_class_call_check":"dRqgV","@swc/helpers/_/_create_class":"8RAzW","@swc/helpers/_/_inherits":"aRPJQ","@swc/helpers/_/_create_super":"kqTtK","../utils":"7esST","../utils/component":"cVtDw","./playbackRate":"7PKBT","./aspectRatio":"21e5i","./flip":"5paZL","./info":"9tDwX","./version":"7YY7g","./close":"5kAbg","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],"7PKBT":[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return i});var o=e("@swc/helpers/_/_object_spread"),a=e("@swc/helpers/_/_object_spread_props"),s=e("../utils");function i(e){return function(t){var r=t.i18n,n=t.constructor.PLAYBACK_RATE.map(function(e){return'<span data-value="'.concat(e,'">').concat(1===e?r.get("Normal"):e.toFixed(1),"</span>")}).join("");return(0,a._)((0,o._)({},e),{html:"".concat(r.get("Play Speed"),": ").concat(n),click:function(e,r){var n=r.target.dataset.value;n&&(t.playbackRate=Number(n),e.show=!1)},mounted:function(e){var r=(0,s.query)('[data-value="1"]',e);r&&(0,s.inverseClass)(r,"art-current"),t.on("video:ratechange",function(){var r=(0,s.queryAll)("span",e).find(function(e){return Number(e.dataset.value)===t.playbackRate});r&&(0,s.inverseClass)(r,"art-current")})}})}}},{"@swc/helpers/_/_object_spread":"g42Vq","@swc/helpers/_/_object_spread_props":"cTIVI","../utils":"7esST","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],"21e5i":[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return i});var o=e("@swc/helpers/_/_object_spread"),a=e("@swc/helpers/_/_object_spread_props"),s=e("../utils");function i(e){return function(t){var r=t.i18n,n=t.constructor.ASPECT_RATIO.map(function(e){return'<span data-value="'.concat(e,'">').concat("default"===e?r.get("Default"):e,"</span>")}).join("");return(0,a._)((0,o._)({},e),{html:"".concat(r.get("Aspect Ratio"),": ").concat(n),click:function(e,r){var n=r.target.dataset.value;n&&(t.aspectRatio=n,e.show=!1)},mounted:function(e){var r=(0,s.query)('[data-value="default"]',e);r&&(0,s.inverseClass)(r,"art-current"),t.on("aspectRatio",function(t){var r=(0,s.queryAll)("span",e).find(function(e){return e.dataset.value===t});r&&(0,s.inverseClass)(r,"art-current")})}})}}},{"@swc/helpers/_/_object_spread":"g42Vq","@swc/helpers/_/_object_spread_props":"cTIVI","../utils":"7esST","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],"5paZL":[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return i});var o=e("@swc/helpers/_/_object_spread"),a=e("@swc/helpers/_/_object_spread_props"),s=e("../utils");function i(e){return function(t){var r=t.i18n,n=t.constructor.FLIP.map(function(e){return'<span data-value="'.concat(e,'">').concat(r.get((0,s.capitalize)(e)),"</span>")}).join("");return(0,a._)((0,o._)({},e),{html:"".concat(r.get("Video Flip"),": ").concat(n),click:function(e,r){var n=r.target.dataset.value;n&&(t.flip=n.toLowerCase(),e.show=!1)},mounted:function(e){var r=(0,s.query)('[data-value="normal"]',e);r&&(0,s.inverseClass)(r,"art-current"),t.on("flip",function(t){var r=(0,s.queryAll)("span",e).find(function(e){return e.dataset.value===t});r&&(0,s.inverseClass)(r,"art-current")})}})}}},{"@swc/helpers/_/_object_spread":"g42Vq","@swc/helpers/_/_object_spread_props":"cTIVI","../utils":"7esST","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],"9tDwX":[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return s});var o=e("@swc/helpers/_/_object_spread"),a=e("@swc/helpers/_/_object_spread_props");function s(e){return function(t){return(0,a._)((0,o._)({},e),{html:t.i18n.get("Video Info"),click:function(e){t.info.show=!0,e.show=!1}})}}},{"@swc/helpers/_/_object_spread":"g42Vq","@swc/helpers/_/_object_spread_props":"cTIVI","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],"7YY7g":[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return s});var o=e("@swc/helpers/_/_object_spread"),a=e("@swc/helpers/_/_object_spread_props");function s(e){return(0,a._)((0,o._)({},e),{html:'<a href="https://artplayer.org" target="_blank">ArtPlayer 5.1.7</a>'})}},{"@swc/helpers/_/_object_spread":"g42Vq","@swc/helpers/_/_object_spread_props":"cTIVI","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],"5kAbg":[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return s});var o=e("@swc/helpers/_/_object_spread"),a=e("@swc/helpers/_/_object_spread_props");function s(e){return function(t){return(0,a._)((0,o._)({},e),{html:t.i18n.get("Close"),click:function(e){e.show=!1}})}}},{"@swc/helpers/_/_object_spread":"g42Vq","@swc/helpers/_/_object_spread_props":"cTIVI","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],kEevD:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return u});var o=e("@swc/helpers/_/_class_call_check"),a=e("@swc/helpers/_/_create_class"),s=e("@swc/helpers/_/_inherits"),i=e("@swc/helpers/_/_create_super"),l=e("./utils"),c=e("./utils/component"),u=function(e){(0,s._)(r,e);var t=(0,i._)(r);function r(e){var n;return(0,o._)(this,r),(n=t.call(this,e)).name="info",l.isMobile||n.init(),n}return(0,a._)(r,[{key:"init",value:function(){var e=this,t=this.art,r=t.proxy,n=t.constructor,o=t.template,a=o.$infoPanel,s=o.$infoClose,i=o.$video;r(s,"click",function(){e.show=!1});var c=null,u=(0,l.queryAll)("[data-video]",a)||[];this.art.on("destroy",function(){return clearTimeout(c)}),function e(){for(var t=0;t<u.length;t++){var r=u[t],o=i[r.dataset.video],a="number"==typeof o?o.toFixed(2):o;r.innerText!==a&&(r.innerText=a)}c=setTimeout(e,n.INFO_LOOP_TIME)}()}}]),r}(n.interopDefault(c).default)},{"@swc/helpers/_/_class_call_check":"dRqgV","@swc/helpers/_/_create_class":"8RAzW","@swc/helpers/_/_inherits":"aRPJQ","@swc/helpers/_/_create_super":"kqTtK","./utils":"7esST","./utils/component":"cVtDw","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],"14H9c":[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return y});var o=e("@swc/helpers/_/_async_to_generator"),a=e("@swc/helpers/_/_class_call_check"),s=e("@swc/helpers/_/_create_class"),i=e("@swc/helpers/_/_inherits"),l=e("@swc/helpers/_/_object_spread"),c=e("@swc/helpers/_/_object_spread_props"),u=e("@swc/helpers/_/_create_super"),p=e("@swc/helpers/_/_ts_generator"),f=e("./utils"),d=e("./utils/component"),h=n.interopDefault(d),m=e("option-validator"),v=n.interopDefault(m),g=e("./scheme"),_=n.interopDefault(g),y=function(e){(0,i._)(r,e);var t=(0,u._)(r);function r(e){(0,a._)(this,r),(n=t.call(this,e)).name="subtitle",n.eventDestroy=function(){return null},n.init(e.option.subtitle);var n,o=!1;return e.on("video:timeupdate",function(){if(n.url){var e=n.art.template.$video.webkitDisplayingFullscreen;"boolean"==typeof e&&e!==o&&(o=e,n.createTrack(e?"subtitles":"metadata",n.url))}}),n}return(0,s._)(r,[{key:"url",get:function(){return this.art.template.$track.src},set:function(e){this.switch(e)}},{key:"textTrack",get:function(){return this.art.template.$video.textTracks[0]}},{key:"activeCue",get:function(){return this.textTrack.activeCues[0]}},{key:"style",value:function(e,t){var r=this.art.template.$subtitle;return"object"==typeof e?(0,f.setStyles)(r,e):(0,f.setStyle)(r,e,t)}},{key:"update",value:function(){var e=this.art.template.$subtitle;e.innerHTML="",this.activeCue&&(this.art.option.subtitle.escape?e.innerHTML=this.activeCue.text.split(/\r?\n/).map(function(e){return'<div class="art-subtitle-line">'.concat((0,f.escape)(e),"</div>")}).join(""):e.innerHTML=this.activeCue.text,this.art.emit("subtitleUpdate",this.activeCue.text))}},{key:"switch",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=this;return(0,o._)(function(){var n,o,a,s,i,u;return(0,p._)(this,function(p){switch(p.label){case 0:return o=(n=r.art).i18n,a=n.notice,s=n.option,i=(0,c._)((0,l._)({},s.subtitle,t),{url:e}),[4,r.init(i)];case 1:return u=p.sent(),t.name&&(a.show="".concat(o.get("Switch Subtitle"),": ").concat(t.name)),[2,u]}})})()}},{key:"createTrack",value:function(e,t){var r=this,n=this.art,o=n.template,a=n.proxy,s=n.option,i=o.$video,l=o.$track,c=(0,f.createElement)("track");c.default=!0,c.kind=e,c.src=t,c.label=s.subtitle.name||"Artplayer",c.track.mode="hidden",this.eventDestroy(),(0,f.remove)(l),(0,f.append)(i,c),o.$track=c,this.eventDestroy=a(this.textTrack,"cuechange",function(){return r.update()})}},{key:"init",value:function(e){var t=this;return(0,o._)(function(){var r,n,o;return(0,p._)(this,function(a){return(n=(r=t.art).notice,o=r.template.$subtitle,(0,v.default)(e,_.default.subtitle),e.url)?(t.style(e.style),[2,fetch(e.url).then(function(e){return e.arrayBuffer()}).then(function(r){var n=new TextDecoder(e.encoding).decode(r);switch(t.art.emit("subtitleLoad",e.url),e.type||(0,f.getExt)(e.url)){case"srt":var o=(0,f.srtToVtt)(n),a=e.onVttLoad(o);return(0,f.vttToBlob)(a);case"ass":var s=(0,f.assToVtt)(n),i=e.onVttLoad(s);return(0,f.vttToBlob)(i);case"vtt":var l=e.onVttLoad(n);return(0,f.vttToBlob)(l);default:return e.url}}).then(function(e){return o.innerHTML="",t.url===e||(URL.revokeObjectURL(t.url),t.createTrack("metadata",e),t.art.emit("subtitleSwitch",e)),e}).catch(function(e){throw o.innerHTML="",n.show=e,e})]):[2]})})()}}]),r}(h.default)},{"@swc/helpers/_/_async_to_generator":"eFkS8","@swc/helpers/_/_class_call_check":"dRqgV","@swc/helpers/_/_create_class":"8RAzW","@swc/helpers/_/_inherits":"aRPJQ","@swc/helpers/_/_object_spread":"g42Vq","@swc/helpers/_/_object_spread_props":"cTIVI","@swc/helpers/_/_create_super":"kqTtK","@swc/helpers/_/_ts_generator":"7FJ4U","./utils":"7esST","./utils/component":"cVtDw","option-validator":"gEtej","./scheme":"hYXnH","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],"9biZS":[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return j});var o=e("@swc/helpers/_/_class_call_check"),a=e("@swc/helpers/_/_create_class"),s=e("../utils/error"),i=e("./clickInit"),l=n.interopDefault(i),c=e("./hoverInit"),u=n.interopDefault(c),p=e("./moveInit"),f=n.interopDefault(p),d=e("./resizeInit"),h=n.interopDefault(d),m=e("./gestureInit"),v=n.interopDefault(m),g=e("./viewInit"),_=n.interopDefault(g),y=e("./documentInit"),b=n.interopDefault(y),w=e("./updateInit"),x=n.interopDefault(w),j=function(){function e(t){(0,o._)(this,e),this.destroyEvents=[],this.proxy=this.proxy.bind(this),this.hover=this.hover.bind(this),this.loadImg=this.loadImg.bind(this),(0,l.default)(t,this),(0,u.default)(t,this),(0,f.default)(t,this),(0,h.default)(t,this),(0,v.default)(t,this),(0,_.default)(t,this),(0,b.default)(t,this),(0,x.default)(t,this)}return(0,a._)(e,[{key:"proxy",value:function(e,t,r){var n=this,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};if(Array.isArray(t))return t.map(function(t){return n.proxy(e,t,r,o)});e.addEventListener(t,r,o);var a=function(){return e.removeEventListener(t,r,o)};return this.destroyEvents.push(a),a}},{key:"hover",value:function(e,t,r){t&&this.proxy(e,"mouseenter",t),r&&this.proxy(e,"mouseleave",r)}},{key:"loadImg",value:function(e){var t=this;return new Promise(function(r,n){var o;if(e instanceof HTMLImageElement)o=e;else{if("string"!=typeof e)return n(new s.ArtPlayerError("Unable to get Image"));(o=new Image).src=e}if(o.complete)return r(o);t.proxy(o,"load",function(){return r(o)}),t.proxy(o,"error",function(){return n(new s.ArtPlayerError("Failed to load Image: ".concat(o.src)))})})}},{key:"remove",value:function(e){var t=this.destroyEvents.indexOf(e);t>-1&&(e(),this.destroyEvents.splice(t,1))}},{key:"destroy",value:function(){for(var e=0;e<this.destroyEvents.length;e++)this.destroyEvents[e]()}}]),e}()},{"@swc/helpers/_/_class_call_check":"dRqgV","@swc/helpers/_/_create_class":"8RAzW","../utils/error":"d6sk8","./clickInit":"kkCkX","./hoverInit":"ivZtu","./moveInit":"9atIc","./resizeInit":"6XMpF","./gestureInit":"96KZ8","./viewInit":"goqLs","./documentInit":"7eAOV","./updateInit":"ioax6","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],kkCkX:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return a});var o=e("../utils");function a(e,t){var r=e.constructor,n=e.template,a=n.$player,s=n.$video;t.proxy(document,["click","contextmenu"],function(t){(0,o.includeFromEvent)(t,a)?(e.isInput="INPUT"===t.target.tagName,e.isFocus=!0,e.emit("focus",t)):(e.isInput=!1,e.isFocus=!1,e.emit("blur",t))});var i=[];t.proxy(s,"click",function(t){var n=Date.now();i.push(n);var a=r.MOBILE_CLICK_PLAY,s=r.DBCLICK_TIME,l=r.MOBILE_DBCLICK_PLAY,c=r.DBCLICK_FULLSCREEN,u=i.filter(function(e){return n-e<=s});switch(u.length){case 1:e.emit("click",t),o.isMobile?!e.isLock&&a&&e.toggle():e.toggle(),i=u;break;case 2:e.emit("dblclick",t),o.isMobile?!e.isLock&&l&&e.toggle():c&&(e.fullscreen=!e.fullscreen),i=[];break;default:i=[]}})}},{"../utils":"7esST","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],ivZtu:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return a});var o=e("../utils");function a(e,t){var r=e.template.$player;t.hover(r,function(t){(0,o.addClass)(r,"art-hover"),e.emit("hover",!0,t)},function(t){(0,o.removeClass)(r,"art-hover"),e.emit("hover",!1,t)})}},{"../utils":"7esST","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],"9atIc":[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");function o(e,t){var r=e.template.$player;t.proxy(r,"mousemove",function(t){e.emit("mousemove",t)})}n.defineInteropFlag(r),n.export(r,"default",function(){return o})},{"@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],"6XMpF":[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return a});var o=e("../utils");function a(e,t){var r=e.option,n=e.constructor;e.on("resize",function(){var t=e.aspectRatio,n=e.notice;"standard"===e.state&&r.autoSize&&e.autoSize(),e.aspectRatio=t,n.show=""});var a=(0,o.debounce)(function(){return e.emit("resize")},n.RESIZE_TIME);t.proxy(window,["orientationchange","resize"],function(){return a()}),screen&&screen.orientation&&screen.orientation.onchange&&t.proxy(screen.orientation,"change",function(){return a()})}},{"../utils":"7esST","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],"96KZ8":[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return s});var o=e("../utils"),a=e("../control/progress");function s(e,t){if(o.isMobile&&!e.option.isLive){var r=e.template,n=r.$video,s=r.$progress,i=null,l=!1,c=0,u=0,p=0,f=function(t){if(1===t.touches.length&&!e.isLock){i===s&&(0,a.setCurrentTime)(e,t),l=!0;var r=t.touches[0],n=r.pageX,o=r.pageY;c=n,u=o,p=e.currentTime}},d=function(t){if(1===t.touches.length&&l&&e.duration){var r=t.touches[0],a=r.pageX,s=r.pageY,f=function(e,t,r,n){var o=t-n,a=r-e,s=0;if(2>Math.abs(a)&&2>Math.abs(o))return s;var i=180*Math.atan2(o,a)/Math.PI;return i>=-45&&i<45?s=4:i>=45&&i<135?s=1:i>=-135&&i<-45?s=2:(i>=135&&i<=180||i>=-180&&i<-135)&&(s=3),s}(c,u,a,s),d=[3,4].includes(f),h=[1,2].includes(f);if(d&&!e.isRotate||h&&e.isRotate){var m=(0,o.clamp)((a-c)/e.width,-1,1),v=(0,o.clamp)((s-u)/e.height,-1,1),g=e.isRotate?v:m,_=i===n?e.constructor.TOUCH_MOVE_RATIO:1,y=(0,o.clamp)(p+e.duration*g*_,0,e.duration);e.seek=y,e.emit("setBar","played",(0,o.clamp)(y/e.duration,0,1),t),e.notice.show="".concat((0,o.secondToTime)(y)," / ").concat((0,o.secondToTime)(e.duration))}}};t.proxy(s,"touchstart",function(e){i=s,f(e)}),t.proxy(n,"touchstart",function(e){i=n,f(e)}),t.proxy(n,"touchmove",d),t.proxy(s,"touchmove",d),t.proxy(document,"touchend",function(){l&&(c=0,u=0,p=0,l=!1,i=null)})}}},{"../utils":"7esST","../control/progress":"EmoUd","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],goqLs:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return a});var o=e("../utils");function a(e,t){var r=e.option,n=e.constructor,a=e.template.$container,s=(0,o.throttle)(function(){e.emit("view",(0,o.isInViewport)(a,n.SCROLL_GAP))},n.SCROLL_TIME);t.proxy(window,"scroll",function(){return s()}),e.on("view",function(t){r.autoMini&&(e.mini=!t)})}},{"../utils":"7esST","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],"7eAOV":[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");function o(e,t){t.proxy(document,"mousemove",function(t){e.emit("document:mousemove",t)}),t.proxy(document,"mouseup",function(t){e.emit("document:mouseup",t)})}n.defineInteropFlag(r),n.export(r,"default",function(){return o})},{"@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],ioax6:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");function o(e){if(e.constructor.USE_RAF){var t=null;!function r(){e.playing&&e.emit("raf"),e.isDestroy||(t=requestAnimationFrame(r))}(),e.on("destroy",function(){cancelAnimationFrame(t)})}}n.defineInteropFlag(r),n.export(r,"default",function(){return o})},{"@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],bCoXC:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return i});var o=e("@swc/helpers/_/_class_call_check"),a=e("@swc/helpers/_/_create_class"),s=e("./utils"),i=function(){function e(t){(0,o._)(this,e),this.art=t,this.keys={},t.option.hotkey&&!s.isMobile&&this.init()}return(0,a._)(e,[{key:"init",value:function(){var e=this,t=this.art,r=t.proxy,n=t.constructor;this.add(27,function(){e.art.fullscreenWeb&&(e.art.fullscreenWeb=!1)}),this.add(32,function(){e.art.toggle()}),this.add(37,function(){e.art.backward=n.SEEK_STEP}),this.add(38,function(){e.art.volume+=n.VOLUME_STEP}),this.add(39,function(){e.art.forward=n.SEEK_STEP}),this.add(40,function(){e.art.volume-=n.VOLUME_STEP}),r(window,"keydown",function(t){if(e.art.isFocus){var r=document.activeElement.tagName.toUpperCase(),n=document.activeElement.getAttribute("contenteditable");if("INPUT"!==r&&"TEXTAREA"!==r&&""!==n&&"true"!==n&&!t.altKey&&!t.ctrlKey&&!t.metaKey&&!t.shiftKey){var o=e.keys[t.keyCode];if(o){t.preventDefault();for(var a=0;a<o.length;a++)o[a].call(e.art,t);e.art.emit("hotkey",t)}}}})}},{key:"add",value:function(e,t){return this.keys[e]?this.keys[e].push(t):this.keys[e]=[t],this}},{key:"remove",value:function(e,t){if(this.keys[e]){var r=this.keys[e].indexOf(t);-1!==r&&this.keys[e].splice(r,1)}return this}}]),e}()},{"@swc/helpers/_/_class_call_check":"dRqgV","@swc/helpers/_/_create_class":"8RAzW","./utils":"7esST","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],eQf6l:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return l});var o=e("@swc/helpers/_/_class_call_check"),a=e("@swc/helpers/_/_inherits"),s=e("@swc/helpers/_/_create_super"),i=e("./utils/component"),l=function(e){(0,a._)(r,e);var t=(0,s._)(r);function r(e){(0,o._)(this,r),n=t.call(this,e);var n,a=e.option,s=e.template.$layer;n.name="layer",n.$parent=s;for(var i=0;i<a.layers.length;i++)n.add(a.layers[i]);return n}return r}(n.interopDefault(i).default)},{"@swc/helpers/_/_class_call_check":"dRqgV","@swc/helpers/_/_inherits":"aRPJQ","@swc/helpers/_/_create_super":"kqTtK","./utils/component":"cVtDw","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],gzHl5:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return c});var o=e("@swc/helpers/_/_class_call_check"),a=e("@swc/helpers/_/_inherits"),s=e("@swc/helpers/_/_create_super"),i=e("./utils"),l=e("./utils/component"),c=function(e){(0,a._)(r,e);var t=(0,s._)(r);function r(e){var n;return(0,o._)(this,r),(n=t.call(this,e)).name="loading",(0,i.append)(e.template.$loading,e.icons.loading),n}return r}(n.interopDefault(l).default)},{"@swc/helpers/_/_class_call_check":"dRqgV","@swc/helpers/_/_inherits":"aRPJQ","@swc/helpers/_/_create_super":"kqTtK","./utils":"7esST","./utils/component":"cVtDw","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],gdkSy:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return i});var o=e("@swc/helpers/_/_class_call_check"),a=e("@swc/helpers/_/_create_class"),s=e("./utils"),i=function(){function e(t){(0,o._)(this,e),this.art=t,this.timer=null}return(0,a._)(e,[{key:"show",set:function(e){var t=this.art,r=t.constructor,n=t.template,o=n.$player,a=n.$noticeInner;e?(a.innerText=e instanceof Error?e.message.trim():e,(0,s.addClass)(o,"art-notice-show"),clearTimeout(this.timer),this.timer=setTimeout(function(){a.innerText="",(0,s.removeClass)(o,"art-notice-show")},r.NOTICE_TIME)):(0,s.removeClass)(o,"art-notice-show")}}]),e}()},{"@swc/helpers/_/_class_call_check":"dRqgV","@swc/helpers/_/_create_class":"8RAzW","./utils":"7esST","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],kH8gp:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return c});var o=e("@swc/helpers/_/_class_call_check"),a=e("@swc/helpers/_/_inherits"),s=e("@swc/helpers/_/_create_super"),i=e("./utils"),l=e("./utils/component"),c=function(e){(0,a._)(r,e);var t=(0,s._)(r);function r(e){(0,o._)(this,r),(n=t.call(this,e)).name="mask";var n,a=e.template,s=e.icons,l=e.events,c=(0,i.append)(a.$state,s.state),u=(0,i.append)(a.$state,s.error);return(0,i.setStyle)(u,"display","none"),e.on("destroy",function(){(0,i.setStyle)(c,"display","none"),(0,i.setStyle)(u,"display",null)}),l.proxy(a.$state,"click",function(){return e.play()}),n}return r}(n.interopDefault(l).default)},{"@swc/helpers/_/_class_call_check":"dRqgV","@swc/helpers/_/_inherits":"aRPJQ","@swc/helpers/_/_create_super":"kqTtK","./utils":"7esST","./utils/component":"cVtDw","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],"1Mh7c":[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return ei});var o=e("@swc/helpers/_/_class_call_check"),a=e("@swc/helpers/_/_object_spread"),s=e("../utils"),i=e("bundle-text:./loading.svg"),l=n.interopDefault(i),c=e("bundle-text:./state.svg"),u=n.interopDefault(c),p=e("bundle-text:./check.svg"),f=n.interopDefault(p),d=e("bundle-text:./play.svg"),h=n.interopDefault(d),m=e("bundle-text:./pause.svg"),v=n.interopDefault(m),g=e("bundle-text:./volume.svg"),_=n.interopDefault(g),y=e("bundle-text:./volume-close.svg"),b=n.interopDefault(y),w=e("bundle-text:./screenshot.svg"),x=n.interopDefault(w),j=e("bundle-text:./setting.svg"),k=n.interopDefault(j),T=e("bundle-text:./arrow-left.svg"),S=n.interopDefault(T),M=e("bundle-text:./arrow-right.svg"),I=n.interopDefault(M),E=e("bundle-text:./playback-rate.svg"),F=n.interopDefault(E),C=e("bundle-text:./aspect-ratio.svg"),R=n.interopDefault(C),O=e("bundle-text:./config.svg"),P=n.interopDefault(O),D=e("bundle-text:./pip.svg"),A=n.interopDefault(D),z=e("bundle-text:./lock.svg"),V=n.interopDefault(z),$=e("bundle-text:./unlock.svg"),L=n.interopDefault($),q=e("bundle-text:./fullscreen-off.svg"),H=n.interopDefault(q),N=e("bundle-text:./fullscreen-on.svg"),W=n.interopDefault(N),B=e("bundle-text:./fullscreen-web-off.svg"),U=n.interopDefault(B),Y=e("bundle-text:./fullscreen-web-on.svg"),K=n.interopDefault(Y),X=e("bundle-text:./switch-on.svg"),G=n.interopDefault(X),J=e("bundle-text:./switch-off.svg"),Z=n.interopDefault(J),Q=e("bundle-text:./flip.svg"),ee=n.interopDefault(Q),et=e("bundle-text:./error.svg"),er=n.interopDefault(et),en=e("bundle-text:./close.svg"),eo=n.interopDefault(en),ea=e("bundle-text:./airplay.svg"),es=n.interopDefault(ea),ei=function e(t){var r=this,n=function(e){(0,s.def)(r,e,{get:function(){return(0,s.getIcon)(e,i[e])}})};(0,o._)(this,e);var i=(0,a._)({loading:l.default,state:u.default,play:h.default,pause:v.default,check:f.default,volume:_.default,volumeClose:b.default,screenshot:x.default,setting:k.default,pip:A.default,arrowLeft:S.default,arrowRight:I.default,playbackRate:F.default,aspectRatio:R.default,config:P.default,lock:V.default,flip:ee.default,unlock:L.default,fullscreenOff:H.default,fullscreenOn:W.default,fullscreenWebOff:U.default,fullscreenWebOn:K.default,switchOn:G.default,switchOff:Z.default,error:er.default,close:eo.default,airplay:es.default},t.option.icons);for(var c in i)n(c)}},{"@swc/helpers/_/_class_call_check":"dRqgV","@swc/helpers/_/_object_spread":"g42Vq","../utils":"7esST","bundle-text:./loading.svg":"pQkTz","bundle-text:./state.svg":"iICVw","bundle-text:./check.svg":"ctFmL","bundle-text:./play.svg":"9sJA7","bundle-text:./pause.svg":"6LoJ7","bundle-text:./volume.svg":"a4KiS","bundle-text:./volume-close.svg":"84uNU","bundle-text:./screenshot.svg":"aIgOs","bundle-text:./setting.svg":"39OST","bundle-text:./arrow-left.svg":"l6P81","bundle-text:./arrow-right.svg":"l7jjF","bundle-text:./playback-rate.svg":"8ywig","bundle-text:./aspect-ratio.svg":"aopUq","bundle-text:./config.svg":"aoLmP","bundle-text:./pip.svg":"7BoWr","bundle-text:./lock.svg":"bkCtG","bundle-text:./unlock.svg":"d9cJz","bundle-text:./fullscreen-off.svg":"bEc44","bundle-text:./fullscreen-on.svg":"jiUYL","bundle-text:./fullscreen-web-off.svg":"a8WfT","bundle-text:./fullscreen-web-on.svg":"iJ2rM","bundle-text:./switch-on.svg":"80RaN","bundle-text:./switch-off.svg":"fHsvm","bundle-text:./flip.svg":"dwdtS","bundle-text:./error.svg":"kYWrM","bundle-text:./close.svg":"d9UhH","bundle-text:./airplay.svg":"e4ZLS","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],pQkTz:[function(e,t,r){t.exports='<svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" viewBox="0 0 100 100" preserveAspectRatio="xMidYMid" class="uil-default"><path fill="none" class="bk" d="M0 0h100v100H0z"/><rect x="47" y="40" width="6" height="20" rx="5" ry="5" fill="#fff" transform="translate(0 -30)"><animate attributeName="opacity" from="1" to="0" dur="1s" begin="-1s" repeatCount="indefinite"/></rect><rect x="47" y="40" width="6" height="20" rx="5" ry="5" fill="#fff" transform="rotate(30 105.98 65)"><animate attributeName="opacity" from="1" to="0" dur="1s" begin="-0.9166666666666666s" repeatCount="indefinite"/></rect><rect x="47" y="40" width="6" height="20" rx="5" ry="5" fill="#fff" transform="rotate(60 75.98 65)"><animate attributeName="opacity" from="1" to="0" dur="1s" begin="-0.8333333333333334s" repeatCount="indefinite"/></rect><rect x="47" y="40" width="6" height="20" rx="5" ry="5" fill="#fff" transform="rotate(90 65 65)"><animate attributeName="opacity" from="1" to="0" dur="1s" begin="-0.75s" repeatCount="indefinite"/></rect><rect x="47" y="40" width="6" height="20" rx="5" ry="5" fill="#fff" transform="rotate(120 58.66 65)"><animate attributeName="opacity" from="1" to="0" dur="1s" begin="-0.6666666666666666s" repeatCount="indefinite"/></rect><rect x="47" y="40" width="6" height="20" rx="5" ry="5" fill="#fff" transform="rotate(150 54.02 65)"><animate attributeName="opacity" from="1" to="0" dur="1s" begin="-0.5833333333333334s" repeatCount="indefinite"/></rect><rect x="47" y="40" width="6" height="20" rx="5" ry="5" fill="#fff" transform="rotate(180 50 65)"><animate attributeName="opacity" from="1" to="0" dur="1s" begin="-0.5s" repeatCount="indefinite"/></rect><rect x="47" y="40" width="6" height="20" rx="5" ry="5" fill="#fff" transform="rotate(-150 45.98 65)"><animate attributeName="opacity" from="1" to="0" dur="1s" begin="-0.4166666666666667s" repeatCount="indefinite"/></rect><rect x="47" y="40" width="6" height="20" rx="5" ry="5" fill="#fff" transform="rotate(-120 41.34 65)"><animate attributeName="opacity" from="1" to="0" dur="1s" begin="-0.3333333333333333s" repeatCount="indefinite"/></rect><rect x="47" y="40" width="6" height="20" rx="5" ry="5" fill="#fff" transform="rotate(-90 35 65)"><animate attributeName="opacity" from="1" to="0" dur="1s" begin="-0.25s" repeatCount="indefinite"/></rect><rect x="47" y="40" width="6" height="20" rx="5" ry="5" fill="#fff" transform="rotate(-60 24.02 65)"><animate attributeName="opacity" from="1" to="0" dur="1s" begin="-0.16666666666666666s" repeatCount="indefinite"/></rect><rect x="47" y="40" width="6" height="20" rx="5" ry="5" fill="#fff" transform="rotate(-30 -5.98 65)"><animate attributeName="opacity" from="1" to="0" dur="1s" begin="-0.08333333333333333s" repeatCount="indefinite"/></rect></svg>'},{}],iICVw:[function(e,t,r){t.exports='<svg xmlns="http://www.w3.org/2000/svg" width="80" height="80" viewBox="0 0 24 24"><path fill="#fff" d="M9.5 9.325v5.35q0 .575.525.875t1.025-.05l4.15-2.65q.475-.3.475-.85t-.475-.85L11.05 8.5q-.5-.35-1.025-.05t-.525.875ZM12 22q-2.075 0-3.9-.788t-3.175-2.137q-1.35-1.35-2.137-3.175T2 12q0-2.075.788-3.9t2.137-3.175q1.35-1.35 3.175-2.137T12 2q2.075 0 3.9.788t3.175 2.137q1.35 1.35 2.138 3.175T22 12q0 2.075-.788 3.9t-2.137 3.175q-1.35 1.35-3.175 2.138T12 22Z"/></svg>'},{}],ctFmL:[function(e,t,r){t.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" style="width:100%;height:100%"><path d="M9 16.2 4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4L9 16.2z" fill="#fff"/></svg>'},{}],"9sJA7":[function(e,t,r){t.exports='<svg xmlns="http://www.w3.org/2000/svg" height="22" width="22"><path d="M17.982 9.275 8.06 3.27A2.013 2.013 0 0 0 5 4.994v12.011a2.017 2.017 0 0 0 3.06 1.725l9.922-6.005a2.017 2.017 0 0 0 0-3.45z"/></svg>'},{}],"6LoJ7":[function(e,t,r){t.exports='<svg xmlns="http://www.w3.org/2000/svg" height="22" width="22"><path d="M7 3a2 2 0 0 0-2 2v12a2 2 0 1 0 4 0V5a2 2 0 0 0-2-2zm8 0a2 2 0 0 0-2 2v12a2 2 0 1 0 4 0V5a2 2 0 0 0-2-2z"/></svg>'},{}],a4KiS:[function(e,t,r){t.exports='<svg xmlns="http://www.w3.org/2000/svg" height="22" width="22"><path d="M10.188 4.65 6 8H5a2 2 0 0 0-2 2v2a2 2 0 0 0 2 2h1l4.188 3.35a.5.5 0 0 0 .812-.39V5.04a.498.498 0 0 0-.812-.39zm4.258-.872a1 1 0 0 0-.862 1.804 6.002 6.002 0 0 1-.007 10.838 1 1 0 0 0 .86 1.806A8.001 8.001 0 0 0 19 11a8.001 8.001 0 0 0-4.554-7.222z"/><path d="M15 11a3.998 3.998 0 0 0-2-3.465v6.93A3.998 3.998 0 0 0 15 11z"/></svg>'},{}],"84uNU":[function(e,t,r){t.exports='<svg xmlns="http://www.w3.org/2000/svg" height="22" width="22"><path d="M15 11a3.998 3.998 0 0 0-2-3.465v2.636l1.865 1.865A4.02 4.02 0 0 0 15 11z"/><path d="M13.583 5.583A5.998 5.998 0 0 1 17 11a6 6 0 0 1-.585 2.587l1.477 1.477a8.001 8.001 0 0 0-3.446-11.286 1 1 0 0 0-.863 1.805zm5.195 13.195-2.121-2.121-1.414-1.414-1.415-1.415L13 13l-2-2-3.889-3.889-3.889-3.889a.999.999 0 1 0-1.414 1.414L5.172 8H5a2 2 0 0 0-2 2v2a2 2 0 0 0 2 2h1l4.188 3.35a.5.5 0 0 0 .812-.39v-3.131l2.587 2.587-.01.005a1 1 0 0 0 .86 1.806c.215-.102.424-.214.627-.333l2.3 2.3a1.001 1.001 0 0 0 1.414-1.416zM11 5.04a.5.5 0 0 0-.813-.39L8.682 5.854 11 8.172V5.04z"/></svg>'},{}],aIgOs:[function(e,t,r){t.exports='<svg xmlns="http://www.w3.org/2000/svg" height="22" width="22" viewBox="0 0 50 50"><path d="M19.402 6a5 5 0 0 0-4.902 4.012L14.098 12H9a5 5 0 0 0-5 5v21a5 5 0 0 0 5 5h32a5 5 0 0 0 5-5V17a5 5 0 0 0-5-5h-5.098l-.402-1.988A5 5 0 0 0 30.598 6ZM25 17c5.52 0 10 4.48 10 10s-4.48 10-10 10-10-4.48-10-10 4.48-10 10-10Zm0 2c-4.41 0-8 3.59-8 8s3.59 8 8 8 8-3.59 8-8-3.59-8-8-8Z"/></svg>'},{}],"39OST":[function(e,t,r){t.exports='<svg xmlns="http://www.w3.org/2000/svg" height="22" width="22"><circle cx="11" cy="11" r="2"/><path d="M19.164 8.861 17.6 8.6a6.978 6.978 0 0 0-1.186-2.099l.574-1.533a1 1 0 0 0-.436-1.217l-1.997-1.153a1.001 1.001 0 0 0-1.272.23l-1.008 1.225a7.04 7.04 0 0 0-2.55.001L8.716 2.829a1 1 0 0 0-1.272-.23L5.447 3.751a1 1 0 0 0-.436 1.217l.574 1.533A6.997 6.997 0 0 0 4.4 8.6l-1.564.261A.999.999 0 0 0 2 9.847v2.306c0 .489.353.906.836.986l1.613.269a7 7 0 0 0 1.228 2.075l-.558 1.487a1 1 0 0 0 .436 1.217l1.997 1.153c.423.244.961.147 1.272-.23l1.04-1.263a7.089 7.089 0 0 0 2.272 0l1.04 1.263a1 1 0 0 0 1.272.23l1.997-1.153a1 1 0 0 0 .436-1.217l-.557-1.487c.521-.61.94-1.31 1.228-2.075l1.613-.269a.999.999 0 0 0 .835-.986V9.847a.999.999 0 0 0-.836-.986zM11 15a4 4 0 1 1 0-8 4 4 0 0 1 0 8z"/></svg>'},{}],l6P81:[function(e,t,r){t.exports='<svg xmlns="http://www.w3.org/2000/svg" height="32" width="32"><path d="m19.41 20.09-4.58-4.59 4.58-4.59L18 9.5l-6 6 6 6z" fill="#fff"/></svg>'},{}],l7jjF:[function(e,t,r){t.exports='<svg xmlns="http://www.w3.org/2000/svg" height="32" width="32"><path d="m12.59 20.34 4.58-4.59-4.58-4.59L14 9.75l6 6-6 6z" fill="#fff"/></svg>'},{}],"8ywig":[function(e,t,r){t.exports='<svg height="24" width="24"><path d="M10 8v8l6-4-6-4zM6.3 5l-.6-.8C7.2 3 9 2.2 11 2l.1 1c-1.8.2-3.4.9-4.8 2zM5 6.3l-.8-.6C3 7.2 2.2 9 2 11l1 .1c.2-1.8.9-3.4 2-4.8zm0 11.4c-1.1-1.4-1.8-3.1-2-4.8L2 13c.2 2 1 3.8 2.2 5.4l.8-.7zm6.1 3.3c-1.8-.2-3.4-.9-4.8-2l-.6.8C7.2 21 9 21.8 11 22l.1-1zM22 12c0-5.2-3.9-9.4-9-10l-.1 1c4.6.5 8.1 4.3 8.1 9s-3.5 8.5-8.1 9l.1 1c5.2-.5 9-4.8 9-10z" fill="#fff" style="--darkreader-inline-fill:#a8a6a4"/></svg>'},{}],aopUq:[function(e,t,r){t.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 88 88" style="width:100%;height:100%;transform:translate(0,0)"><defs><clipPath id="__lottie_element_216"><path d="M0 0h88v88H0z"/></clipPath></defs><g style="display:block" clip-path="url(\'#__lottie_element_216\')"><path fill="#FFF" d="m12.438-12.702-2.82 2.82c-.79.79-.79 2.05 0 2.83l7.07 7.07-7.07 7.07c-.79.79-.79 2.05 0 2.83l2.82 2.83c.79.78 2.05.78 2.83 0l11.32-11.31c.78-.78.78-2.05 0-2.83l-11.32-11.31c-.78-.79-2.04-.79-2.83 0zm-24.88 0c-.74-.74-1.92-.78-2.7-.12l-.13.12-11.31 11.31a2 2 0 0 0-.12 2.7l.12.13 11.31 11.31a2 2 0 0 0 2.7.12l.13-.12 2.83-2.83c.74-.74.78-1.91.11-2.7l-.11-.13-7.07-7.07 7.07-7.07c.74-.74.78-1.91.11-2.7l-.11-.13-2.83-2.82zM28-28c4.42 0 8 3.58 8 8v40c0 4.42-3.58 8-8 8h-56c-4.42 0-8-3.58-8-8v-40c0-4.42 3.58-8 8-8h56z" style="--darkreader-inline-fill:#a8a6a4" transform="translate(44 44)"/></g></svg>'},{}],aoLmP:[function(e,t,r){t.exports='<svg height="24" width="24"><path d="M15 17h6v1h-6v-1zm-4 0H3v1h8v2h1v-5h-1v2zm3-9h1V3h-1v2H3v1h11v2zm4-3v1h3V5h-3zM6 14h1V9H6v2H3v1h3v2zm4-2h11v-1H10v1z" fill="#fff" style="--darkreader-inline-fill:#a8a6a4"/></svg>'},{}],"7BoWr":[function(e,t,r){t.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 36 36" height="32" width="32"><path d="M25 17h-8v6h8v-6Zm4 8V10.98C29 9.88 28.1 9 27 9H9c-1.1 0-2 .88-2 1.98V25c0 1.1.9 2 2 2h18c1.1 0 2-.9 2-2Zm-2 .02H9V10.97h18v14.05Z"/></svg>'},{}],bkCtG:[function(e,t,r){t.exports='<svg class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="20" height="20"><path d="M298.667 426.667v-85.334a213.333 213.333 0 1 1 426.666 0v85.334H768A85.333 85.333 0 0 1 853.333 512v256A85.333 85.333 0 0 1 768 853.333H256A85.333 85.333 0 0 1 170.667 768V512A85.333 85.333 0 0 1 256 426.667h42.667zM512 213.333a128 128 0 0 0-128 128v85.334h256v-85.334a128 128 0 0 0-128-128z" fill="#fff"/></svg>'},{}],d9cJz:[function(e,t,r){t.exports='<svg class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="20" height="20"><path d="m666.752 194.517-49.365 74.112A128 128 0 0 0 384 341.333l.043 85.334h384A85.333 85.333 0 0 1 853.376 512v256a85.333 85.333 0 0 1-85.333 85.333H256A85.333 85.333 0 0 1 170.667 768V512A85.333 85.333 0 0 1 256 426.667h42.667v-85.334a213.333 213.333 0 0 1 368.085-146.816z" fill="#fff"/></svg>'},{}],bEc44:[function(e,t,r){t.exports='<svg class="icon" width="22" height="22" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg"><path fill="#fff" d="M768 298.667h170.667V384h-256V128H768v170.667zM341.333 384h-256v-85.333H256V128h85.333v256zM768 725.333V896h-85.333V640h256v85.333H768zM341.333 640v256H256V725.333H85.333V640h256z"/></svg>'},{}],jiUYL:[function(e,t,r){t.exports='<svg class="icon" width="22" height="22" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg"><path fill="#fff" d="M625.778 256H768v142.222h113.778v-256h-256V256zM256 398.222V256h142.222V142.222h-256v256H256zm512 227.556V768H625.778v113.778h256v-256H768zM398.222 768H256V625.778H142.222v256h256V768z"/></svg>'},{}],a8WfT:[function(e,t,r){t.exports='<svg class="icon" width="18" height="18" viewBox="0 0 1152 1024" xmlns="http://www.w3.org/2000/svg"><path fill="#fff" d="M1075.2 0H76.8A76.8 76.8 0 0 0 0 76.8v870.4a76.8 76.8 0 0 0 76.8 76.8h998.4a76.8 76.8 0 0 0 76.8-76.8V76.8A76.8 76.8 0 0 0 1075.2 0zM1024 128v768H128V128h896zM896 512a64 64 0 0 1 7.488 127.552L896 640H768v128a64 64 0 0 1-56.512 63.552L704 832a64 64 0 0 1-63.552-56.512L640 768V582.592c0-34.496 25.024-66.112 61.632-70.208l8-.384H896zm-640 0a64 64 0 0 1-7.488-127.552L256 384h128V256a64 64 0 0 1 56.512-63.552L448 192a64 64 0 0 1 63.552 56.512L512 256v185.408c0 34.432-25.024 66.112-61.632 70.144l-8 .448H256z"/></svg>'},{}],iJ2rM:[function(e,t,r){t.exports='<svg class="icon" width="18" height="18" viewBox="0 0 1152 1024" xmlns="http://www.w3.org/2000/svg"><path fill="#fff" d="M1075.2 0H76.8A76.8 76.8 0 0 0 0 76.8v870.4a76.8 76.8 0 0 0 76.8 76.8h998.4a76.8 76.8 0 0 0 76.8-76.8V76.8A76.8 76.8 0 0 0 1075.2 0zM1024 128v768H128V128h896zm-576 64a64 64 0 0 1 7.488 127.552L448 320H320v128a64 64 0 0 1-56.512 63.552L256 512a64 64 0 0 1-63.552-56.512L192 448V262.592c0-34.432 25.024-66.112 61.632-70.144l8-.448H448zm256 640a64 64 0 0 1-7.488-127.552L704 704h128V576a64 64 0 0 1 56.512-63.552L896 512a64 64 0 0 1 63.552 56.512L960 576v185.408c0 34.496-25.024 66.112-61.632 70.208l-8 .384H704z"/></svg>'},{}],"80RaN":[function(e,t,r){t.exports='<svg class="icon" width="26" height="26" viewBox="0 0 1664 1024" xmlns="http://www.w3.org/2000/svg"><path fill="#648FFC" d="M1152 0H512a512 512 0 0 0 0 1024h640a512 512 0 0 0 0-1024zm0 960a448 448 0 1 1 448-448 448 448 0 0 1-448 448z"/></svg>'},{}],fHsvm:[function(e,t,r){t.exports='<svg class="icon" width="26" height="26" viewBox="0 0 1740 1024" xmlns="http://www.w3.org/2000/svg"><path fill="#fff" d="M511.898 1024h670.515c282.419-.41 511.18-229.478 511.18-511.898 0-282.419-228.761-511.488-511.18-511.897H511.898C229.478.615.717 229.683.717 512.102c0 282.42 228.761 511.488 511.18 511.898zm-.564-975.36A464.589 464.589 0 1 1 48.026 513.024 463.872 463.872 0 0 1 511.334 48.435v.205z"/></svg>'},{}],dwdtS:[function(e,t,r){t.exports='<svg class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="24" height="24"><path d="M554.667 810.667V896h-85.334v-85.333h85.334zm-384-632.662a42.667 42.667 0 0 1 34.986 18.219l203.904 291.328a42.667 42.667 0 0 1 0 48.896L205.611 827.776A42.667 42.667 0 0 1 128 803.328V220.672a42.667 42.667 0 0 1 42.667-42.667zm682.666 0a42.667 42.667 0 0 1 42.368 37.718l.299 4.949v582.656a42.667 42.667 0 0 1-74.24 28.63l-3.413-4.182-203.904-291.328a42.667 42.667 0 0 1-3.03-43.861l3.03-5.035 203.946-291.328a42.667 42.667 0 0 1 34.944-18.219zM554.667 640v85.333h-85.334V640h85.334zm-358.4-320.896V716.8L335.957 512 196.31 319.104zm358.4 150.23v85.333h-85.334v-85.334h85.334zm0-170.667V384h-85.334v-85.333h85.334zm0-170.667v85.333h-85.334V128h85.334z" fill="#fff"/></svg>'},{}],kYWrM:[function(e,t,r){t.exports='<svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="50" height="50"><path d="M593.818 168.55 949.82 763.76c26.153 43.746 10.732 99.738-34.447 125.052-14.397 8.069-30.72 12.308-47.37 12.308H155.976c-52.224 0-94.536-40.96-94.536-91.505 0-16.097 4.383-31.928 12.718-45.875l356.004-595.19c26.173-43.724 84.009-58.654 129.208-33.341a93.082 93.082 0 0 1 34.448 33.341zM512 819.2a61.44 61.44 0 1 0 0-122.88 61.44 61.44 0 0 0 0 122.88zm0-512a72.315 72.315 0 0 0-71.762 81.306l25.723 205.721a46.408 46.408 0 0 0 92.078 0l25.723-205.742A72.315 72.315 0 0 0 512 307.2z"/></svg>'},{}],d9UhH:[function(e,t,r){t.exports='<svg class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="22" height="22"><path d="m571.733 512 268.8-268.8c17.067-17.067 17.067-42.667 0-59.733-17.066-17.067-42.666-17.067-59.733 0L512 452.267l-268.8-268.8c-17.067-17.067-42.667-17.067-59.733 0-17.067 17.066-17.067 42.666 0 59.733l268.8 268.8-268.8 268.8c-17.067 17.067-17.067 42.667 0 59.733 8.533 8.534 19.2 12.8 29.866 12.8s21.334-4.266 29.867-12.8l268.8-268.8 268.8 268.8c8.533 8.534 19.2 12.8 29.867 12.8s21.333-4.266 29.866-12.8c17.067-17.066 17.067-42.666 0-59.733L571.733 512z"/></svg>'},{}],e4ZLS:[function(e,t,r){t.exports='<svg width="18" height="18" xmlns="http://www.w3.org/2000/svg"><g fill="#fff"><path d="M16 1H2a1 1 0 0 0-1 1v10a1 1 0 0 0 1 1h3v-2H3V3h12v8h-2v2h3a1 1 0 0 0 1-1V2a1 1 0 0 0-1-1Z"/><path d="M4 17h10l-5-6z"/></g></svg>'},{}],"2lyD4":[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return j});var o=e("@swc/helpers/_/_async_to_generator"),a=e("@swc/helpers/_/_class_call_check"),s=e("@swc/helpers/_/_create_class"),i=e("@swc/helpers/_/_inherits"),l=e("@swc/helpers/_/_to_consumable_array"),c=e("@swc/helpers/_/_create_super"),u=e("@swc/helpers/_/_ts_generator"),p=e("./flip"),f=n.interopDefault(p),d=e("./aspectRatio"),h=n.interopDefault(d),m=e("./playbackRate"),v=n.interopDefault(m),g=e("./subtitleOffset"),_=n.interopDefault(g),y=e("../utils/component"),b=n.interopDefault(y),w=e("../utils/error"),x=e("../utils"),j=function(e){(0,i._)(r,e);var t=(0,c._)(r);function r(e){(0,a._)(this,r),n=t.call(this,e);var n,o=e.option,s=e.controls,i=e.template.$setting;return n.name="setting",n.$parent=i,n.option=[],n.events=[],n.cache=new Map,o.setting&&(n.init(),e.on("blur",function(){n.show&&(n.show=!1,n.render(n.option))}),e.on("focus",function(e){var t=(0,x.includeFromEvent)(e,s.setting),r=(0,x.includeFromEvent)(e,n.$parent);!n.show||t||r||(n.show=!1,n.render(n.option))})),n}return(0,s._)(r,[{key:"defaultSettings",get:function(){var e=[],t=this.art.option;return t.playbackRate&&e.push((0,v.default)(this.art)),t.aspectRatio&&e.push((0,h.default)(this.art)),t.flip&&e.push((0,f.default)(this.art)),t.subtitleOffset&&e.push((0,_.default)(this.art)),e}},{key:"init",value:function(){var e=this.art.option,t=(0,l._)(this.defaultSettings).concat((0,l._)(e.settings));this.option=r.makeRecursion(t),this.destroy(),this.render(this.option)}},{key:"destroy",value:function(){for(var e=0;e<this.events.length;e++)this.art.events.remove(this.events[e]);this.$parent.innerHTML="",this.events=[],this.cache=new Map}},{key:"find",value:function(){for(var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.option,r=0;r<t.length;r++){var n=t[r];if(n.name===e)return n;var o=this.find(e,n.selector||[]);if(o)return o}}},{key:"remove",value:function(e){var t=this.find(e);(0,w.errorHandle)(t,"Can't find [".concat(e,"] from the [setting]"));var n=t.$parentItem?t.$parentItem.selector:this.option;return n.splice(n.indexOf(t),1),this.option=r.makeRecursion(this.option),this.destroy(),this.render(this.option),this.option}},{key:"update",value:function(e){var t=this.find(e.name);return t?(Object.assign(t,e),this.option=r.makeRecursion(this.option),this.destroy(),this.render(this.option)):this.add(e),this.option}},{key:"add",value:function(e){return this.option.push(e),this.option=r.makeRecursion(this.option),this.destroy(),this.render(this.option),this.option}},{key:"creatHeader",value:function(e){var t=this,r=this.art,n=r.icons,o=r.proxy,a=r.constructor,s=(0,x.createElement)("div");(0,x.setStyle)(s,"height","".concat(a.SETTING_ITEM_HEIGHT,"px")),(0,x.addClass)(s,"art-setting-item"),(0,x.addClass)(s,"art-setting-item-back");var i=(0,x.append)(s,'<div class="art-setting-item-left"></div>'),l=(0,x.createElement)("div");(0,x.addClass)(l,"art-setting-item-left-icon"),(0,x.append)(l,n.arrowLeft),(0,x.append)(i,l),(0,x.append)(i,e.$parentItem.html);var c=o(s,"click",function(){return t.render(e.$parentList)});return this.events.push(c),s}},{key:"creatItem",value:function(e,t){var r=this.art,n=r.icons,a=r.proxy,s=r.constructor,i=(0,x.createElement)("div");(0,x.addClass)(i,"art-setting-item"),(0,x.setStyle)(i,"height","".concat(s.SETTING_ITEM_HEIGHT,"px")),(0,x.isStringOrNumber)(t.name)&&(i.dataset.name=t.name),(0,x.isStringOrNumber)(t.value)&&(i.dataset.value=t.value);var l=(0,x.append)(i,'<div class="art-setting-item-left"></div>'),c=(0,x.append)(i,'<div class="art-setting-item-right"></div>'),p=(0,x.createElement)("div");switch((0,x.addClass)(p,"art-setting-item-left-icon"),e){case"switch":case"range":(0,x.append)(p,(0,x.isStringOrNumber)(t.icon)||t.icon instanceof Element?t.icon:n.config);break;case"selector":t.selector&&t.selector.length?(0,x.append)(p,(0,x.isStringOrNumber)(t.icon)||t.icon instanceof Element?t.icon:n.config):(0,x.append)(p,n.check)}(0,x.append)(l,p),t.$icon=p,(0,x.def)(t,"icon",{configurable:!0,get:function(){return p.innerHTML},set:function(e){(0,x.isStringOrNumber)(e)&&(p.innerHTML=e)}});var f=(0,x.createElement)("div");(0,x.addClass)(f,"art-setting-item-left-text"),(0,x.append)(f,t.html||""),(0,x.append)(l,f),t.$html=f,(0,x.def)(t,"html",{configurable:!0,get:function(){return f.innerHTML},set:function(e){(0,x.isStringOrNumber)(e)&&(f.innerHTML=e)}});var d=(0,x.createElement)("div");switch((0,x.addClass)(d,"art-setting-item-right-tooltip"),(0,x.append)(d,t.tooltip||""),(0,x.append)(c,d),t.$tooltip=d,(0,x.def)(t,"tooltip",{configurable:!0,get:function(){return d.innerHTML},set:function(e){(0,x.isStringOrNumber)(e)&&(d.innerHTML=e)}}),e){case"switch":var h=(0,x.createElement)("div");(0,x.addClass)(h,"art-setting-item-right-icon");var m=(0,x.append)(h,n.switchOn),v=(0,x.append)(h,n.switchOff);(0,x.setStyle)(t.switch?v:m,"display","none"),(0,x.append)(c,h),t.$switch=t.switch,(0,x.def)(t,"switch",{configurable:!0,get:function(){return t.$switch},set:function(e){t.$switch=e,e?((0,x.setStyle)(v,"display","none"),(0,x.setStyle)(m,"display",null)):((0,x.setStyle)(v,"display",null),(0,x.setStyle)(m,"display","none"))}});break;case"range":var g=(0,x.createElement)("div");(0,x.addClass)(g,"art-setting-item-right-icon");var _=(0,x.append)(g,'<input type="range">');_.value=t.range[0]||0,_.min=t.range[1]||0,_.max=t.range[2]||10,_.step=t.range[3]||1,(0,x.addClass)(_,"art-setting-range"),(0,x.append)(c,g),t.$range=_,(0,x.def)(t,"range",{configurable:!0,get:function(){return _.valueAsNumber},set:function(e){_.value=Number(e)}});break;case"selector":if(t.selector&&t.selector.length){var y=(0,x.createElement)("div");(0,x.addClass)(y,"art-setting-item-right-icon"),(0,x.append)(y,n.arrowRight),(0,x.append)(c,y)}}switch(e){case"switch":if(t.onSwitch){var b,w=this,j=a(i,"click",(b=(0,o._)(function(e){return(0,u._)(this,function(r){switch(r.label){case 0:return[4,t.onSwitch.call(w.art,t,i,e)];case 1:return t.switch=r.sent(),[2]}})}),function(e){return b.apply(this,arguments)}));this.events.push(j)}break;case"range":if(t.$range){if(t.onRange){var k,T=this,S=a(t.$range,"change",(k=(0,o._)(function(e){return(0,u._)(this,function(r){switch(r.label){case 0:return[4,t.onRange.call(T.art,t,i,e)];case 1:return t.tooltip=r.sent(),[2]}})}),function(e){return k.apply(this,arguments)}));this.events.push(S)}if(t.onChange){var M,I=this,E=a(t.$range,"input",(M=(0,o._)(function(e){return(0,u._)(this,function(r){switch(r.label){case 0:return[4,t.onChange.call(I.art,t,i,e)];case 1:return t.tooltip=r.sent(),[2]}})}),function(e){return M.apply(this,arguments)}));this.events.push(E)}}break;case"selector":var F,C=this,R=a(i,"click",(F=(0,o._)(function(e){var r,n,o;return(0,u._)(this,function(a){switch(a.label){case 0:if(!(t.selector&&t.selector.length))return[3,1];return C.render(t.selector,t.width),[3,3];case 1:for((0,x.inverseClass)(i,"art-current"),r=0;r<t.$parentItem.selector.length;r++)(n=t.$parentItem.selector[r]).default=n===t;if(t.$parentList&&C.render(t.$parentList),!(t.$parentItem&&t.$parentItem.onSelect))return[3,3];return[4,t.$parentItem.onSelect.call(C.art,t,i,e)];case 2:o=a.sent(),t.$parentItem.$tooltip&&(0,x.isStringOrNumber)(o)&&(t.$parentItem.$tooltip.innerHTML=o),a.label=3;case 3:return[2]}})}),function(e){return F.apply(this,arguments)}));this.events.push(R),t.default&&(0,x.addClass)(i,"art-current")}return i}},{key:"updateStyle",value:function(e){var t=this.art,r=t.controls,n=t.constructor,o=t.template,a=o.$player,s=o.$setting;if(r.setting&&!x.isMobile){var i=e||n.SETTING_WIDTH,l=(0,x.getRect)(r.setting),c=l.left,u=l.width,p=(0,x.getRect)(a),f=p.left,d=p.width,h=c-f+u/2-i/2;h+i>d?((0,x.setStyle)(s,"left",null),(0,x.setStyle)(s,"right",null)):((0,x.setStyle)(s,"left","".concat(h,"px")),(0,x.setStyle)(s,"right","auto"))}}},{key:"render",value:function(e,t){var r=this.art.constructor;if(this.cache.has(e)){var n=this.cache.get(e);(0,x.inverseClass)(n,"art-current"),(0,x.setStyle)(this.$parent,"width","".concat(n.dataset.width,"px")),(0,x.setStyle)(this.$parent,"height","".concat(n.dataset.height,"px")),this.updateStyle(Number(n.dataset.width))}else{var o=(0,x.createElement)("div");(0,x.addClass)(o,"art-setting-panel"),o.dataset.width=t||r.SETTING_WIDTH,o.dataset.height=e.length*r.SETTING_ITEM_HEIGHT,e[0]&&e[0].$parentItem&&((0,x.append)(o,this.creatHeader(e[0])),o.dataset.height=Number(o.dataset.height)+r.SETTING_ITEM_HEIGHT);for(var a=0;a<e.length;a++){var s=e[a];(0,x.has)(s,"switch")?(0,x.append)(o,this.creatItem("switch",s)):(0,x.has)(s,"range")?(0,x.append)(o,this.creatItem("range",s)):(0,x.append)(o,this.creatItem("selector",s))}(0,x.append)(this.$parent,o),this.cache.set(e,o),(0,x.inverseClass)(o,"art-current"),(0,x.setStyle)(this.$parent,"width","".concat(o.dataset.width,"px")),(0,x.setStyle)(this.$parent,"height","".concat(o.dataset.height,"px")),this.updateStyle(Number(o.dataset.width)),e[0]&&e[0].$parentItem&&e[0].$parentItem.mounted&&e[0].$parentItem.mounted.call(this.art,o,e[0].$parentItem)}}}],[{key:"makeRecursion",value:function(e,t,n){for(var o=0;o<e.length;o++){var a=e[o];a.$parentItem=t,a.$parentList=n,r.makeRecursion(a.selector||[],a,e)}return e}}]),r}(b.default)},{"@swc/helpers/_/_async_to_generator":"eFkS8","@swc/helpers/_/_class_call_check":"dRqgV","@swc/helpers/_/_create_class":"8RAzW","@swc/helpers/_/_inherits":"aRPJQ","@swc/helpers/_/_to_consumable_array":"lHEqu","@swc/helpers/_/_create_super":"kqTtK","@swc/helpers/_/_ts_generator":"7FJ4U","./flip":"fyWnH","./aspectRatio":"4PMSP","./playbackRate":"52qeC","./subtitleOffset":"hpVYB","../utils/component":"cVtDw","../utils/error":"d6sk8","../utils":"7esST","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],fyWnH:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return a});var o=e("../utils");function a(e){var t=e.i18n,r=e.icons,n=e.constructor,a=n.SETTING_ITEM_WIDTH,s=n.FLIP;function i(e,r,n){r&&(r.innerText=t.get((0,o.capitalize)(n)));var a=(0,o.queryAll)(".art-setting-item",e).find(function(e){return e.dataset.value===n});a&&(0,o.inverseClass)(a,"art-current")}return{width:a,name:"flip",html:t.get("Video Flip"),tooltip:t.get((0,o.capitalize)(e.flip)),icon:r.flip,selector:s.map(function(r){return{value:r,name:"aspect-ratio-".concat(r),default:r===e.flip,html:t.get((0,o.capitalize)(r))}}),onSelect:function(t){return e.flip=t.value,t.html},mounted:function(t,r){i(t,r.$tooltip,e.flip),e.on("flip",function(){i(t,r.$tooltip,e.flip)})}}}},{"../utils":"7esST","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],"4PMSP":[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return a});var o=e("../utils");function a(e){var t=e.i18n,r=e.icons,n=e.constructor,a=n.SETTING_ITEM_WIDTH,s=n.ASPECT_RATIO;function i(e){return"default"===e?t.get("Default"):e}function l(e,t,r){t&&(t.innerText=i(r));var n=(0,o.queryAll)(".art-setting-item",e).find(function(e){return e.dataset.value===r});n&&(0,o.inverseClass)(n,"art-current")}return{width:a,name:"aspect-ratio",html:t.get("Aspect Ratio"),icon:r.aspectRatio,tooltip:i(e.aspectRatio),selector:s.map(function(t){return{value:t,name:"aspect-ratio-".concat(t),default:t===e.aspectRatio,html:i(t)}}),onSelect:function(t){return e.aspectRatio=t.value,t.html},mounted:function(t,r){l(t,r.$tooltip,e.aspectRatio),e.on("aspectRatio",function(){l(t,r.$tooltip,e.aspectRatio)})}}}},{"../utils":"7esST","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],"52qeC":[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return a});var o=e("../utils");function a(e){var t=e.i18n,r=e.icons,n=e.constructor,a=n.SETTING_ITEM_WIDTH,s=n.PLAYBACK_RATE;function i(e){return 1===e?t.get("Normal"):e.toFixed(1)}function l(e,t,r){t&&(t.innerText=i(r));var n=(0,o.queryAll)(".art-setting-item",e).find(function(e){return Number(e.dataset.value)===r});n&&(0,o.inverseClass)(n,"art-current")}return{width:a,name:"playback-rate",html:t.get("Play Speed"),tooltip:i(e.playbackRate),icon:r.playbackRate,selector:s.map(function(t){return{value:t,name:"aspect-ratio-".concat(t),default:t===e.playbackRate,html:i(t)}}),onSelect:function(t){return e.playbackRate=t.value,t.html},mounted:function(t,r){l(t,r.$tooltip,e.playbackRate),e.on("video:ratechange",function(){l(t,r.$tooltip,e.playbackRate)})}}}},{"../utils":"7esST","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],hpVYB:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");function o(e){var t=e.i18n,r=e.icons;return{width:e.constructor.SETTING_ITEM_WIDTH,name:"subtitle-offset",html:t.get("Subtitle Offset"),icon:r.subtitle,tooltip:"0s",range:[0,-5,5,.1],onChange:function(t){return e.subtitleOffset=t.range,t.range+"s"}}}n.defineInteropFlag(r),n.export(r,"default",function(){return o})},{"@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],"38YEE":[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return i});var o=e("@swc/helpers/_/_class_call_check"),a=e("@swc/helpers/_/_create_class"),s=e("@swc/helpers/_/_define_property"),i=function(){function e(){(0,o._)(this,e),this.name="artplayer_settings",this.settings={}}return(0,a._)(e,[{key:"get",value:function(e){try{var t=JSON.parse(window.localStorage.getItem(this.name))||{};return e?t[e]:t}catch(t){return e?this.settings[e]:this.settings}}},{key:"set",value:function(e,t){try{var r=Object.assign({},this.get(),(0,s._)({},e,t));window.localStorage.setItem(this.name,JSON.stringify(r))}catch(r){this.settings[e]=t}}},{key:"del",value:function(e){try{var t=this.get();delete t[e],window.localStorage.setItem(this.name,JSON.stringify(t))}catch(t){delete this.settings[e]}}},{key:"clear",value:function(){try{window.localStorage.removeItem(this.name)}catch(e){this.settings={}}}}]),e}()},{"@swc/helpers/_/_class_call_check":"dRqgV","@swc/helpers/_/_create_class":"8RAzW","@swc/helpers/_/_define_property":"ceYdR","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],b3Yxu:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return g});var o=e("@swc/helpers/_/_class_call_check"),a=e("@swc/helpers/_/_create_class"),s=e("../utils"),i=e("./miniProgressBar"),l=n.interopDefault(i),c=e("./autoOrientation"),u=n.interopDefault(c),p=e("./autoPlayback"),f=n.interopDefault(p),d=e("./fastForward"),h=n.interopDefault(d),m=e("./lock"),v=n.interopDefault(m),g=function(){function e(t){(0,o._)(this,e),this.art=t,this.id=0;var r=t.option;r.miniProgressBar&&!r.isLive&&this.add(l.default),r.lock&&s.isMobile&&this.add(v.default),r.autoPlayback&&!r.isLive&&this.add(f.default),r.autoOrientation&&s.isMobile&&this.add(u.default),r.fastForward&&s.isMobile&&!r.isLive&&this.add(h.default);for(var n=0;n<r.plugins.length;n++)this.add(r.plugins[n])}return(0,a._)(e,[{key:"add",value:function(e){var t=this;this.id+=1;var r=e.call(this.art,this.art);return r instanceof Promise?r.then(function(r){return t.next(e,r)}):this.next(e,r)}},{key:"next",value:function(e,t){var r=t&&t.name||e.name||"plugin".concat(this.id);return(0,s.errorHandle)(!(0,s.has)(this,r),"Cannot add a plugin that already has the same name: ".concat(r)),(0,s.def)(this,r,{value:t}),this}}]),e}()},{"@swc/helpers/_/_class_call_check":"dRqgV","@swc/helpers/_/_create_class":"8RAzW","../utils":"7esST","./miniProgressBar":"8jU3c","./autoOrientation":"hrET2","./autoPlayback":"6nThf","./fastForward":"foG6r","./lock":"1zrgm","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],"8jU3c":[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return a});var o=e("../utils");function a(e){return e.on("control",function(t){t?(0,o.removeClass)(e.template.$player,"art-mini-progress-bar"):(0,o.addClass)(e.template.$player,"art-mini-progress-bar")}),{name:"mini-progress-bar"}}},{"../utils":"7esST","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],hrET2:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return i});var o=e("@swc/helpers/_/_async_to_generator"),a=e("@swc/helpers/_/_ts_generator"),s=e("../utils");function i(e){var t,r=e.constructor,n=e.template,i=n.$player,l=n.$video;return e.on("fullscreenWeb",function(t){if(t){var n=l.videoWidth,o=l.videoHeight,a=document.documentElement,c=a.clientWidth,u=a.clientHeight;(n>o&&c<u||n<o&&c>u)&&setTimeout(function(){(0,s.setStyle)(i,"width","".concat(u,"px")),(0,s.setStyle)(i,"height","".concat(c,"px")),(0,s.setStyle)(i,"transform-origin","0 0"),(0,s.setStyle)(i,"transform","rotate(90deg) translate(0, -".concat(c,"px)")),(0,s.addClass)(i,"art-auto-orientation"),e.isRotate=!0,e.emit("resize")},r.AUTO_ORIENTATION_TIME)}else(0,s.hasClass)(i,"art-auto-orientation")&&((0,s.removeClass)(i,"art-auto-orientation"),e.isRotate=!1,e.emit("resize"))}),e.on("fullscreen",(t=(0,o._)(function(e){var t,r,n,o,c,u,p,f,d;return(0,a._)(this,function(a){switch(a.label){case 0:if(!(null===(r=screen)||void 0===r?void 0:null===(t=r.orientation)||void 0===t?void 0:t.lock))return[2];if(n=screen.orientation.type,!e)return[3,3];if(o=l.videoWidth,c=l.videoHeight,p=(u=document.documentElement).clientWidth,f=u.clientHeight,!(o>c&&p<f||o<c&&p>f))return[3,2];return d=n.startsWith("portrait")?"landscape":"portrait",[4,screen.orientation.lock(d)];case 1:a.sent(),(0,s.addClass)(i,"art-auto-orientation-fullscreen"),a.label=2;case 2:return[3,5];case 3:if(!(0,s.hasClass)(i,"art-auto-orientation-fullscreen"))return[3,5];return[4,screen.orientation.lock(n)];case 4:a.sent(),(0,s.removeClass)(i,"art-auto-orientation-fullscreen"),a.label=5;case 5:return[2]}})}),function(e){return t.apply(this,arguments)})),{name:"autoOrientation",get state(){return(0,s.hasClass)(i,"art-auto-orientation")}}}},{"@swc/helpers/_/_async_to_generator":"eFkS8","@swc/helpers/_/_ts_generator":"7FJ4U","../utils":"7esST","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],"6nThf":[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return a});var o=e("../utils");function a(e){var t=e.i18n,r=e.icons,n=e.storage,a=e.constructor,s=e.proxy,i=e.template.$poster,l=e.layers.add({name:"auto-playback",html:'\n<div class="art-auto-playback-close"></div>\n<div class="art-auto-playback-last"></div>\n<div class="art-auto-playback-jump"></div>\n '}),c=(0,o.query)(".art-auto-playback-last",l),u=(0,o.query)(".art-auto-playback-jump",l),p=(0,o.query)(".art-auto-playback-close",l);return e.on("video:timeupdate",function(){if(e.playing){var t=n.get("times")||{},r=Object.keys(t);r.length>a.AUTO_PLAYBACK_MAX&&delete t[r[0]],t[e.option.id||e.option.url]=e.currentTime,n.set("times",t)}}),e.on("ready",function(){var f=(n.get("times")||{})[e.option.id||e.option.url];f&&f>=a.AUTO_PLAYBACK_MIN&&((0,o.append)(p,r.close),(0,o.setStyle)(l,"display","flex"),c.innerText="".concat(t.get("Last Seen")," ").concat((0,o.secondToTime)(f)),u.innerText=t.get("Jump Play"),s(p,"click",function(){(0,o.setStyle)(l,"display","none")}),s(u,"click",function(){e.seek=f,e.play(),(0,o.setStyle)(i,"display","none"),(0,o.setStyle)(l,"display","none")}),e.once("video:timeupdate",function(){setTimeout(function(){(0,o.setStyle)(l,"display","none")},a.AUTO_PLAYBACK_TIMEOUT)}))}),{name:"auto-playback",get times(){return n.get("times")||{}},clear:function(){return n.del("times")},delete:function(e){var t=n.get("times")||{};return delete t[e],n.set("times",t),t}}}},{"../utils":"7esST","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],foG6r:[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return a});var o=e("../utils");function a(e){var t=e.constructor,r=e.proxy,n=e.template,a=n.$player,s=n.$video,i=null,l=!1,c=1,u=function(){clearTimeout(i),l&&(l=!1,e.playbackRate=c,(0,o.removeClass)(a,"art-fast-forward"))};return r(s,"touchstart",function(r){1===r.touches.length&&e.playing&&!e.isLock&&(i=setTimeout(function(){l=!0,c=e.playbackRate,e.playbackRate=t.FAST_FORWARD_VALUE,(0,o.addClass)(a,"art-fast-forward")},t.FAST_FORWARD_TIME))}),r(document,"touchmove",u),r(document,"touchend",u),{name:"fastForward",get state(){return(0,o.hasClass)(a,"art-fast-forward")}}}},{"../utils":"7esST","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}],"1zrgm":[function(e,t,r){var n=e("@parcel/transformer-js/src/esmodule-helpers.js");n.defineInteropFlag(r),n.export(r,"default",function(){return a});var o=e("../utils");function a(e){var t=e.layers,r=e.icons,n=e.template.$player;function a(){return(0,o.hasClass)(n,"art-lock")}function s(){(0,o.addClass)(n,"art-lock"),e.isLock=!0,e.emit("lock",!0)}function i(){(0,o.removeClass)(n,"art-lock"),e.isLock=!1,e.emit("lock",!1)}return t.add({name:"lock",mounted:function(t){var n=(0,o.append)(t,r.lock),a=(0,o.append)(t,r.unlock);(0,o.setStyle)(n,"display","none"),e.on("lock",function(e){e?((0,o.setStyle)(n,"display","inline-flex"),(0,o.setStyle)(a,"display","none")):((0,o.setStyle)(n,"display","none"),(0,o.setStyle)(a,"display","inline-flex"))})},click:function(){a()?i():s()}}),{name:"lock",get state(){return a()},set state(value){value?s():i()}}}},{"../utils":"7esST","@parcel/transformer-js/src/esmodule-helpers.js":"hTt7M"}]},["7Csdv"],"7Csdv","parcelRequireb749");