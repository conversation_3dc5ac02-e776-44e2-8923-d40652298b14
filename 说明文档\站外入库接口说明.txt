﻿

站外入库接口一般是给第三方软件（如：火车头、ET等）提供的入库方案。

使用前请在后台系统-站外入库配置 设置好 免登录入库密码，和 分类名称对应系统分类规则。

系统默认api入库接口文件为http://域名/api.php，根据需要可修改入库文件。

下载官方提供的 苹果CMS-V10 火车头入库模块！

火车头配置
   网站根地址填写 http://域名/api.php/

   点击测试，输入验证密码，何必要信息测试入库。

接口必要信息是：
   名称， 分类ID 或 分类名称 （如果同时存在以 分类ID为准）

入库接口地址
视频/api.php/receive/vod；
文章/api.php/receive/art；
演员/api.php/receive/actor；
角色/api.php/receive/role；
网址/api.php/receive/website；






















