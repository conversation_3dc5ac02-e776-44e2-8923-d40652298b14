<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no">
    <title>系统安全验证</title>
    <link rel="stylesheet" href="__STATIC__/css/home.css">
    <style>
        body{background:#F9FAFD;color:#818181;}
        input{
            border: 1px solid #ccc;
            padding: 7px 0px;
            border-radius: 3px;
            padding-left:5px;
            -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075);
            box-shadow: inset 0 1px 1px rgba(0,0,0,.075);
            -webkit-transition: border-color ease-in-out .15s,-webkit-box-shadow ease-in-out .15s;
            -o-transition: border-color ease-in-out .15s,box-shadow ease-in-out .15s;
            transition: border-color ease-in-out .15s,box-shadow ease-in-out .15s
        }
        .mac_verify_img{
            padding: 7px 0px;border-radius: 3px;
            padding-left:5px;
        }
    </style>
    <script src="{$maccms.path}static/js/jquery.js"></script>
    <script>var maccms={"path":"__ROOT__","mid":"{$maccms['mid']}","aid":"{$maccms['aid']}","url":"{$maccms['site_url']}","wapurl":"{$maccms['site_wapurl']}","mob_status":"{$maccms['mob_status']}"};</script>
	<script src="{$maccms.path}static/js/home.js"></script>
    <script>
        $(function(){
            $('.mac_verify').focus();
            $("input[name='verify']").bind('keypress',function(event){
                if(event.keyCode == "13") {
                    if($("input[name='verify']").val()!=''){
                        $('.verify_submit').click();
                    }
                }
            });
            $('.verify_submit').click(function(){
                var v = $('input[name="verify"]').val();
                MAC.Ajax(maccms.path+'/index.php/ajax/verify_check?type={$type}&verify='+v,'post','json','',function(r){
                    if(r.code==1){
                        location.reload();
                    }
                    else{
                        alert(r.msg);
                        MAC.Verify.Refresh();
                    }
                },function(){
                    alert('请求失败，请重试');
                    MAC.Verify.Refresh();
                });
            });
        });
    </script>
</head>
<body>
<div class="mac_msg_jump">
    <div class="msg_jump_tit">安全验证...</div>
    <div class="title">请输入验证码：</div>
    <div class="text">
        <input type="text" name="verify" class="mac_verify">
    </div>
    <div class="jump">
        <input type="button" class="verify_submit submit_btn" value="提交验证">
    </div>
</div>
</body>
</html>