{include file="../../../application/admin/view/public/head" /}
<div class="page-container p10">

    <div class="layui-tab layui-tab-brief" lay-filter="tabs">
        <ul class="layui-tab-title">
            <li class="btn-local" ><a href="{:url('index')}">{:lang('return')}</a></li>
            <li class="layui-this"><a href="{:url('add')}">{:lang('import')}</a></li>
        </ul>

        <div class="layui-tab-content">
            <blockquote class="layui-elem-quote layui-quote-nm">
                {:lang('admin/vodplayer/import_tip')}
            </blockquote>
            <input type="hidden" id="token" name="__token__" value="{$Request.token}" />
            <button type="button" class="layui-btn layui-upload" id="upload1">{:lang('upload')}</button>
        </div>
    </div>
</div>

{include file="../../../application/admin/view/public/foot" /}


<script type="text/javascript">
    var url='';
    layui.use(['form','laypage', 'layer','upload','element'], function() {
        // 操作对象
        var form = layui.form
            , layer = layui.layer
            , upload = layui.upload
            ,element = layui.element;

        upload.render({
            elem: '.layui-upload'
            ,url: "{:url('vodplayer/import')}?__token__=" + $('#token').val()
            ,method: 'post'
            ,exts:'txt'
            ,before: function(input) {
                layer.msg("{:lang('upload_ing')}", {time:3000000});
            },done: function(res, index, upload) {
                var obj = this.item;
                layer.msg(res.msg);
                setTimeout(function () {
                    layer.closeAll();
                    location.reload();
                },2000);
            }
        });

    });


</script>
</body>
</html>