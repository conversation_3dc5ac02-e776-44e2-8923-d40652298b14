<?php
return array (
  'dplayer' => 
  array (
    'status' => '1',
    'from' => 'dplayer',
    'show' => 'DPlayer-H5播放器',
    'des' => 'dplayer.js.org',
    'target' => '_self',
    'ps' => '0',
    'parse' => '',
    'sort' => '908',
    'tip' => '无需安装任何插件',
    'id' => 'dplayer',
  ),
  'videojs' => 
  array (
    'status' => '1',
    'sort' => '907',
    'from' => 'videojs',
    'show' => 'videojs-H5播放器',
    'des' => 'videojs.com',
    'parse' => '',
    'ps' => '0',
    'tip' => '无需安装任何插件',
    'id' => 'videojs',
  ),
  'iva' => 
  array (
    'status' => '1',
    'from' => 'iva',
    'show' => 'iva-H5播放器',
    'des' => 'videojj.com',
    'target' => '_self',
    'ps' => '0',
    'parse' => '',
    'sort' => '906',
    'tip' => '无需安装任何插件',
    'id' => 'iva',
  ),
  'iframe' => 
  array (
    'status' => '1',
    'from' => 'iframe',
    'show' => 'iframe外链数据',
    'des' => 'iframe外链数据',
    'ps' => '0',
    'parse' => '',
    'sort' => '905',
    'tip' => '无需安装任何插件',
    'id' => 'iframe',
  ),
  'link' => 
  array (
    'status' => '1',
    'sort' => '904',
    'from' => 'link',
    'show' => '外链数据',
    'des' => '外部网站播放链接',
    'ps' => '0',
    'parse' => '',
    'tip' => '无需安装任何插件',
    'id' => 'link',
  ),
  'swf' => 
  array (
    'status' => '1',
    'sort' => '903',
    'from' => 'swf',
    'show' => 'Flash文件',
    'des' => 'swf',
    'parse' => '',
    'ps' => '0',
    'tip' => '无需安装任何插件',
    'id' => 'swf',
  ),
  'flv' => 
  array (
    'status' => '1',
    'from' => 'flv',
    'show' => 'Flv文件',
    'des' => 'flv',
    'target' => '_self',
    'ps' => '0',
    'parse' => '',
    'sort' => '902',
    'tip' => '无需安装任何插件	',
    'id' => 'flv',
  ),
);