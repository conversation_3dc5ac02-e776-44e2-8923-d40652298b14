{include file="../../../application/admin/view/public/head" /}
<div class="page-container p10">

    <div class="my-toolbar-box" >
        <div class="center mb10">
            <form class="layui-form " method="post" action="{:url('index')}">
                <div class="layui-input-inline w150">
                    <select name="type">
                        <option value="">{:lang('select_genre')}</option>
                        <option value="1" {if condition="$param['type'] eq '1'"}selected {/if}>{:lang('admin/plog/points_recharge')}</option>
                        <option value="2" {if condition="$param['type'] eq '2'"}selected {/if}>{:lang('admin/plog/reg_promote')}</option>
                        <option value="3" {if condition="$param['type'] eq '3'"}selected {/if}>{:lang('admin/plog/visit_promote')}</option>
                        <option value="4" {if condition="$param['type'] eq '4'"}selected {/if}>{:lang('one_level_distribution')}</option>
                        <option value="5" {if condition="$param['type'] eq '5'"}selected {/if}>{:lang('two_level_distribution')}</option>
                        <option value="6" {if condition="$param['type'] eq '6'"}selected {/if}>{:lang('three_level_distribution')}</option>
                        <option value="7" {if condition="$param['type'] eq '7'"}selected {/if}>{:lang('admin/plog/points_upgrade')}</option>
                        <option value="8" {if condition="$param['type'] eq '8'"}selected {/if}>{:lang('admin/plog/points_buy')}</option>
                        <option value="9" {if condition="$param['type'] eq '9'"}selected {/if}>{:lang('admin/plog/points_withdrawal')}</option>
                    </select>
                </div>
                <div class="layui-input-inline">
                    <input type="text" autocomplete="off" placeholder="{:lang('wd')}" class="layui-input" name="wd" value="{$param['wd']|mac_filter_xss}">
                </div>
                <button class="layui-btn mgl-20 j-search" >{:lang('btn_search')}</button>
            </form>
        </div>

        <div class="layui-btn-group">
            <a data-href="{:url('del')}" class="layui-btn layui-btn-primary j-page-btns confirm"><i class="layui-icon">&#xe640;</i>{:lang('del')}</a>
            <a data-href="{:url('del')}?ids=1&all=1" class="layui-btn layui-btn-primary j-ajax" confirm="{:lang('clear_confirm')}"><i class="layui-icon">&#xe640;</i>{:lang('clear')}</a>
        </div>
    </div>

     <form class="layui-form " method="post" id="pageListForm">
         <table class="layui-table" lay-size="sm">
            <thead>
            <tr>
                <th width="25"><input type="checkbox" lay-skin="primary" lay-filter="allChoose"></th>
                <th width="80">{:lang('id')}</th>
                <th width="100">{:lang('user')}</th>
                <th width="50">{:lang('genre')}</th>
                <th width="50">{:lang('points')}</th>
                <th width="200">{:lang('remarks')}</th>
                <th width="140">{:lang('admin/plog/log_time')}</th>
                <th width="50">{:lang('opt')}</th>
            </tr>
            </thead>

            {volist name="list" id="vo"}
            <tr>
                <td><input type="checkbox" name="ids[]" value="{$vo.ulog_id}" class="layui-checkbox checkbox-ids" lay-skin="primary"></td>
                <td>{$vo.plog_id}</td>
                <td>[{$vo.user_id}]{$vo.user_name}</td>
                <td>{$vo.plog_type|mac_get_plog_type_text}</td>
                <td>{if condition="in_array($vo.plog_type,[1,2,3,4,5,6])"}+{else/}-{/if}{$vo.plog_points}</td>
                <td>{$vo.plog_remarks}</td>
                <td>{$vo.plog_time|mac_day='color'}</td>
                <td>
                    <a class="layui-badge-rim j-tr-del" data-href="{:url('del?ids='.$vo['plog_id'])}" href="javascript:;" title="{:lang('del')}">{:lang('del')}</a>
                </td>
            </tr>
            {/volist}
            </tbody>
        </table>

        <div id="pages" class="center"></div>

    </form>
</div>

{include file="../../../application/admin/view/public/foot" /}


<script type="text/javascript">
    var curUrl="{:url('plog/index',$param)}";
    layui.use(['laypage', 'layer'], function() {
        var laypage = layui.laypage
                , layer = layui.layer;

        laypage.render({
            elem: 'pages'
            ,count: {$total}
            ,limit: {$limit}
            ,curr: {$page}
            ,layout: ['count', 'prev', 'page', 'next', 'limit', 'skip']
            ,jump: function(obj,first){
                if(!first){
                    location.href = curUrl.replace('%7Bpage%7D',obj.curr).replace('%7Blimit%7D',obj.limit);
                }
            }
        });
    });
</script>
</body>
</html>