1，确认空间支持rewrite组件。
2，按照 伪静态rewrite 目录下的说明文档操作。
3，后台设置浏览模式为 rewrite伪静态 。


如果使用rewrite伪静态模式，请注意把配置文件复制到网站根目录。
如果静态文件后缀配置的不是html，则请把rewrite配置文件中的.html替换为对应的后缀如.html

iis6.x   下使用 httpd.ini

iis7.x   下使用web.config

apache下使用 .htaccess  
(编辑.htaccess文件，把 RewriteBase /maccms10 修改为你苹果CMS所在目录)

nginx 下使用 maccms.conf
(使用vps或者服务器的可以在你的主机的conf里 用 include xxxxx.conf   也就是包含下伪静态规则文件
如果用的是虚拟主机版的nginx 就找你的主机商给你添加规则就行，你把规则发给他。)
