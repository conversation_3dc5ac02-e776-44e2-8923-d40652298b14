{include file="../../../application/admin/view/public/head" /}
<div class="page-container p10">

    <div class="my-toolbar-box" >
        <div class="center mb10">
            <form class="layui-form " method="post" action="{:url('index')}">
                <div class="layui-input-inline w150">
                    <select name="mid">
                        <option value="">{:lang('select_model')}</option>
                        <option value="6" {if condition="$param['mid'] eq '6'"}selected {/if}>{:lang('user')}</option>
                        <option value="11" {if condition="$param['mid'] eq '11'"}selected {/if}>{:lang('website')}</option>
                    </select>
                </div>
                <div class="layui-input-inline w150">
                    <select name="time">
                        <option value="">{:lang('select_time')}</option>
                        <option value="0" {if condition="$param['time'] eq '0'"}selected {/if}>{:lang('that_day')}</option>
                        <option value="7" {if condition="$param['time'] eq '7'"}selected {/if}>{:lang('in_a_week')}</option>
                        <option value="30" {if condition="$param['time'] eq '30'"}selected {/if}>{:lang('in_a_month')}</option>
                    </select>
                </div>
                <div class="layui-input-inline">
                    <input type="text" autocomplete="off" placeholder="{:lang('wd')}" class="layui-input" name="wd" value="{$param['wd']|mac_filter_xss}">
                </div>
                <button class="layui-btn mgl-20 j-search" >{:lang('btn_search')}</button>
            </form>
        </div>

        <div class="layui-btn-group">
            <a data-href="{:url('del')}" class="layui-btn layui-btn-primary j-page-btns confirm"><i class="layui-icon">&#xe640;</i>{:lang('del')}</a>
            <a data-href="{:url('del')}?ids=1&all=1" class="layui-btn layui-btn-primary j-ajax" confirm="{:lang('clear_confirm')}"><i class="layui-icon">&#xe640;</i>{:lang('clear')}</a>
        </div>
    </div>

     <form class="layui-form " method="post" id="pageListForm">
         <table class="layui-table" lay-size="sm">
            <thead>
            <tr>
                <th width="25"><input type="checkbox" lay-skin="primary" lay-filter="allChoose"></th>
                <th width="80">{:lang('id')}</th>
                <th width="100">{:lang('user')}</th>
                <th width="50">{:lang('model')}</th>
                <th >{:lang('from')}</th>
                <th width="130">{:lang('time')}</th>
                <th width="50">{:lang('opt')}</th>
            </tr>
            </thead>

            {volist name="list" id="vo"}
            <tr>
                <td><input type="checkbox" name="ids[]" value="{$vo.ulog_id}" class="layui-checkbox checkbox-ids" lay-skin="primary"></td>
                <td>{$vo.visit_id}</td>
                <td>[{$vo.user_id}]{$vo.user_name}</td>
                <td>{$vo.visit_mid|mac_get_mid_text}</td>
                <td>{$vo.visit_ly}</td>
                <td>{$vo.visit_time|mac_day='color'}</td>
                <td>
                    <a class="layui-badge-rim j-tr-del" data-href="{:url('del?ids='.$vo['visit_id'])}" href="javascript:;" title="{:lang('del')}">{:lang('del')}</a>
                </td>
            </tr>
            {/volist}
            </tbody>
        </table>

        <div id="pages" class="center"></div>

    </form>
</div>

{include file="../../../application/admin/view/public/foot" /}


<script type="text/javascript">
    var curUrl="{:url('visit/index',$param)}";
    layui.use(['laypage', 'layer'], function() {
        var laypage = layui.laypage
                , layer = layui.layer;

        laypage.render({
            elem: 'pages'
            ,count: {$total}
            ,limit: {$limit}
            ,curr: {$page}
            ,layout: ['count', 'prev', 'page', 'next', 'limit', 'skip']
            ,jump: function(obj,first){
                if(!first){
                    location.href = curUrl.replace('%7Bpage%7D',obj.curr).replace('%7Blimit%7D',obj.limit);
                }
            }
        });
    });
</script>
</body>
</html>