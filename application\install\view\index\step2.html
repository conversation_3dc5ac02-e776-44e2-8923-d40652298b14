{include file="../../../application/install/view/index/head" /}
<style type="text/css">
.layui-table td, .layui-table th{text-align:left;}
.layui-table tbody tr.no{background-color:#f00;color:#fff;}
</style>
<div class="install-box">
    <fieldset class="layui-elem-field layui-field-title">
        <legend>{:lang('install/environment_title')}</legend>
    </fieldset>
    <table class="layui-table" lay-skin="line">
        <thead>
            <tr>
                <th>{:lang('install/environment_name')}</th>
                <th>{:lang('install/required_config')}</th>
                <th>{:lang('install/current_config')}</th>
            </tr> 
        </thead>
        <tbody>
            {volist name="data.env" id="vo"}
            <tr class="{$vo[4]}">
                <td>{$vo[0]}</td>
                <td>{$vo[2]}</td>
                <td>{$vo[3]}</td>
            </tr>
            {/volist}
        </tbody>
    </table>
    <table class="layui-table" lay-skin="line">
        <thead>
            <tr>
                <th>{:lang('install/dir_file')}</th>
                <th>{:lang('install/required_popedom')}</th>
                <th>{:lang('install/current_popedom')}</th>
            </tr> 
        </thead>
        <tbody>
            {volist name="data.dir" id="vo"}
            <tr class="{$vo[4]}">
                <td>{$vo[1]}</td>
                <td>{$vo[2]}</td>
                <td>{$vo[3]}</td>
            </tr>
            {/volist}
        </tbody>
    </table>
    <table class="layui-table" lay-skin="line">
        <thead>
            <tr>
                <th>{:lang('install/func_ext')}</th>
                <th>{:lang('install/type')}</th>
                <th>{:lang('install/result')}</th>
            </tr> 
        </thead>
        <tbody>
            {volist name="data.func" id="vo"}
            <tr class="{$vo[2]}">
                <td>{$vo[0]}</td>
                <td>{$vo[3]}</td>
                <td>{$vo[1]}</td>
            </tr>
            {/volist}
        </tbody>
    </table>
    <div class="step-btns">
        <a href="?index" class="layui-btn layui-btn-primary layui-btn-big fl">{:lang('install/back_step')}</a>

        <a href="?step=3" class="layui-btn layui-btn-big layui-btn-normal fr">{:lang('install/next_step')}</a>
        <a target="_blank" href="http://www.maccms.la/doc/v10/faq.html" class="layui-btn layui-btn-big layui-btn-danger fr">{:lang('install/question')}</a>
    </div>
</div>
{include file="../../../application/install/view/index/foot" /}