<!DOCTYPE html>
<html>
<head>
	<title>Iva html5播放器</title>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
	<style type="text/css">
	html,body{height:100%; padding:0; margin:0;}
	#playerCnt{width:100%;height:100%;margin:0 auto;background-origin:content-box;background-size:cover;}
	</style>
	<!-- 引入Iva脚本文件 -->
	<script type="text/javascript" src="http://cytron.cdn.videojj.com/latest/cytron.core.js"></script>
	<!-- /引入Iva脚本文件 -->
</head>
<body>
<div id="playerCnt"></div>
<script type="text/javascript">
	function GetQS(name)
	{
		var reg = new RegExp("(^|&)"+ name +"=([^&]*)(&|$)");
		var r = window.location.search.substr(1).match(reg);
		if (r!=null) return decodeURIComponent(r[2]); return ''; 
	}
	
	var playfrom = GetQS('f');
	var playurl = GetQS('u');
	var ivaInstance = new Iva('playerCnt', {
		appkey: 'EyPKeiUt',
		video: playurl,
		type: 0,
		title: '',
		cover: '',
		cms:'maccms',
		logoPosition: 'right',
		editorEnable: false,
		vorEnable: true,
		vorStartGuideEnable: false,
		autoplay: true
	});
	try{
		document.getElementById('playerCnt').style.height = parent.MacPlayer.Height + 'px';
	}
	catch(e){}
</script>
</body>
</html>