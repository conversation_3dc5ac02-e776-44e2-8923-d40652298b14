<?php
/*韩文
*當前語言包是系統全局語言包；自定義模塊語言包請在模塊的lang目錄下創建比如 application/admin/lang/顯示的時候會優先於全局包
*全局key：單詞可能在系統內任何地方使用
*模塊key：開頭  admin/ 、 install/ 、只在模塊內使用
*後臺菜單key：開頭menu/
*內部處理key：開頭 model/、controller/、只在模塊內使用
*/
return [
    'lang_ver'=>'3021+',
    'hello'  => '웹사이트 이용을 환영합니다',
    'maccms_name'=>'Apple CMS-v10',
    'maccms_copyright'=>'© MacCMS All Rights Reserved.',

    'vod'=>'동영상',
    'art'=>'기사',
    'topic'=>'특집',
    'comment'=>'댓글',
    'gbook'=>'방명록',
    'user'=>'사용자',
    'label'=>'사용자 지정 페이지',
    'actor'=>'배우',
    'role'=>'역할',
    'plot'=>'에피소드 줄거리',
    'website'=>'웹사이트',
    'domain'=>'도메인',
    'or'=>'또는',
    'all'=>'전체',
    'open'=>'열기',
    'close'=>'닫기',
    'task'=>'작업',
    'status'=>'상태',
    'status_parse'=>'분석 상태',
    'test'=>'테스트',
    'copy'=>'복사',
    'run'=>'실행',
    'run_ok'=>'실행 성공',
    'skip'=>'건너뛰기',
    'jump'=>'점프',
    'jump_over'=>'건너뛰기',
    'quantity'=>'수량',
    'start'=>'시작',
    'end'=>'마감',
    'save'=>'저장',
    'level'=>'추천',
    'lock'=>'잠금',
    'unlock'=>'잠금 해제',
    'disable'=>'비활성화',
    'enable'=>'활성화',
    'pause'=>'일시 중지',
    'normal'=>'정상',
    'abnormal'=>'비정상',
    'back_link'=>'백링크',
    'page_limit'=>'페이지당 항목 수',
    'copyright'=>'저작권',
    'browse'=>'찾아보기',
    'favorites'=>'즐겨찾기',
    'want_see'=>'보고 싶어요',
    'play'=>'재생',
    'down'=>'다운로드',
    'website'=>'웹사이트',
    'site_name'=>'웹사이트 이름',
    'keyword'=>'키워드',
    'description'=>'설명',
    'data_name'=>'데이터 이름',
    'return'=>'돌아가기',
    'integral_recharge'=>'포인트 충전',
    'registration_promotion'=>'가입 프로모션',
    'visit_promotion'=>'방문 프로모션',
    'one_level_distribution'=>'1차 배포',
    'two_level_distribution'=>'2차 배포',
    'three_level_distribution'=>'3차 배포',
    'points_upgrade'=>'포인트 업그레이드',
    'integral_consumption'=>'포인트 소비',
    'integral_withdrawal'=>'포인트 출금',
    'not_sale'=>'판매되지 않음',
    'sold'=>'판매됨',
    'not_used'=>'사용되지 않음',
    'used'=>'사용됨',
    'not_paid'=>'미결제',
    'paid'=>'결제됨',
    'slice'=>'편',
    'drama'=>'드라마',
    'the'=>'제',
    'episode'=>'화',
    'issue'=>'호',
    'just'=>'방금',
    'day_after_tomorrow'=>'모레',
    'tomorrow'=>'내일',
    'year'=>'년',
    'years'=>'년',
    'month'=>'월',
    'day'=>'일',
    'yes'=>'예',
    'not'=>'아니요',
    'seconds'=>'초 전',
    'yesterday'=>'어제',
    'day_before_yesterday'=>'그저께',
    'seconds_ago'=>'초 전',
    'minutes_ago'=>'분 전',
    'hours_ago'=>'시간 전',
    'continue_in_second'=>'초 후 계속',
    'audit'=>'심사',
    'reviewed'=>'심사됨',
    'reviewed_not'=>'심사되지 않음',
    'last_run_time'=>'마지막 실행 시간',
    'wait_submit'=>'데이터 제출 중...',
    'wait_time'=>'대기 시간',
    'audit_time'=>'심사 시간',
    'bank'=>'은행',
    'request_err'=>'요청 실패',
    'group'=>'회원 그룹',
    'access'=>'계정',
    'test_ok'=>'테스트 성공',
    'test_err'=>'테스트 실패',
    'browser_jump'=>'브라우저가 자동으로 이동하지 않으면 여기를 클릭하십시오.',
    'filed_empty'=>'내용을 입력하십시오',
    'page_auto'=>'페이지 자동',
    'param_err'=>'매개변수 오류',
    'name_empty'=>'이름을 입력하십시오',
    'pass_empty'=>'비밀번호를 입력하십시오',
    'pass_err'=>'비밀번호 오류',
    'verify_empty'=>'인증 코드를 입력하십시오',
    'verify_err'=>'인증 코드 오류',
    'url_empty'=>'URL을 입력하십시오',
    'cancel_level'=>'추천 취소',
    'select'=>'선택',
    'select_return'=>'선택 후 돌아가기',
    'select_data'=>'데이터 선택',
    'select_opt'=>'작업 선택',
    'select_level'=>'추천 선택',
    'select_type'=>'카테고리 선택',
    'select_status'=>'상태 선택',
    'select_pic'=>'사진 선택',
    'select_sort'=>'정렬 선택',
    'select_lock'=>'잠금 선택',
    'select_sale_status'=>'판매 상태 선택',
    'select_use_status'=>'사용 상태 선택',
    'select_reply_status'=>'답변 상태 선택',
    'select_order_status'=>'주문 상태 선택',
    'select_time'=>'시간 선택',
    'select_please'=>'선택하십시오',
    'select_model'=>'모듈 선택',
    'select_report'=>'신고 선택',
    'select_template'=>'템플릿 선택',
    'select_genre'=>'유형 선택',
    'select_group'=>'사용자 그룹 선택',
    'select_area'=>'지역 선택',
    'default_val'=>'기본값',
    'related_data'=>'관련 데이터',
    'detect'=>'감지',
    'genre'=>'유형',
    'portrait'=>'프로필 사진',
    'tpl_dir'=>'템플릿 디렉토리',
    'ads_dir'=>'광고 디렉토리',
    'reply'=>'답변',
    'reply_yes'=>'답변됨',
    'reply_not'=>'답변되지 않음',
    'report_yes'=>'신고됨',
    'report_not'=>'신고되지 않음',
    'current'=>'현재',
    'blank'=>'새 창',
    'report'=>'신고',

    'use'=>'사용',
    'the_last_time'=>'마지막 시간',
    'that_day'=>'당일',
    'in_a_week'=>'1주일 이내',
    'in_a_month'=>'1개월 이내',

    'calcel_level'=>'추천 취소',
    'pic_empty'=>'사진 없음',
    'pic_remote'=>'원격 사진',
    'pic_sync_err'=>'동기화 오류 사진',
    'pic_local'=>'로컬 사진',
    'pic_sync'=>'사진 동기화',
    'not_pic_sync_err'=>'오류 없는 사진',
    'not_pic_sync_today_err'=>'오늘 오류 없는 사진',
    'pic_err'=>'오류 사진',

    'sort'=>'정렬',
    'add'=>'추가',
    'edit'=>'수정',
    'del'=>'삭제',
    'del_confirm'=>'삭제하시겠습니까?',
    'del_multi'=>'일괄 삭제',
    'del_data'=>'데이터 삭제',
    'del_ok'=>'삭제 성공',
    'del_err'=>'삭제 실패',
    'del_empty'=>'삭제할 항목이 없습니다.',
    'add_group'=>'그룹 추가',

    'id'=>'번호',
    'sub'=>'부제목',
    'hits'=>'조회수',
    'hits_all'=>'총 조회수',
    'hits_month'=>'월간 조회수',
    'hits_week'=>'주간 조회수',
    'hits_day'=>'일일 조회수',
    'no'=>'번호',
    'area'=>'지역',
    'lang'=>'언어',
    'sex'=>'성별',
    'sum'=>'총',
    'opt'=>'작업',
    'opt_content'=>'콘텐츠 작업',
    'name'=>'이름',
    'height'=>'키',
    'weight'=>'몸무게',
    'type'=>'카테고리',
    'wd'=>'키워드',
    'slide'=>'슬라이드',
    'param'=>'매개변수',
    'base_info'=>'기본 정보',
    'other_info'=>'기타 정보',
    'male'=>'남성',
    'female'=>'여성',
    'path'=>'경로',
    'actor_name'=>'배우 이름',
    'alias'=>'별칭',
    'en'=>'병음',
    'letter'=>'첫 글자',
    'color'=>'색상',
    'blood'=>'혈액형',
    'birtharea'=>'출생지',
    'birthday'=>'생일',
    'starsign'=>'별자리',
    'school'=>'졸업 학교',
    'view'=>'보기',
    'multi_set'=>'일괄 설정',
    'multi_separate_tip'=>'쉼표(,)로 구분',
    'multi_del_ok'=>'일괄 삭제 완료',
    'multi_set_ok'=>'일괄 설정 완료',
    'multi_opt_ok'=>'일괄 작업 완료',
    'tip'=>'팁',
    'target'=>'대상 창',
    'start_exec'=>'실행 시작',
    'min_val'=>'최소값',
    'max_val'=>'최대값',

    'vod_name'=>'동영상 이름',
    'role_name'=>'역할 이름',
    'set_ok'=>'설정 성공',
    'set_err'=>'설정 실패',

    'remarks'=>'비고',
    'works'=>'대표작',
    'serial_num'=>'일련 번호',
    'auto_make'=>'자동 생성',
    'make_page'=>'페이지 생성',
    'make_all'=>'전체 생성',

    'class'=>'확장 카테고리',
    'pic'=>'사진',
    'pic_thumb'=>'썸네일',
    'pic_slide'=>'포스터',
    'pic_screenshot'=>'스크린샷',
    'upload'=>'업로드',
    'upload_pic'=>'사진 업로드',
    'blurb'=>'개요',
    'content'=>'자세히 보기',
    'blurb_auto_tip'=>'비워 두면 첫 번째 페이지의 자세히 보기에서 처음 100자를 자동으로 가져옵니다.',
    'up'=>'좋아요',
    'hate'=>'싫어요',
    'rnd_make'=>'무작위 생성',
    'reset_zero'=>'영으로 초기화',
    'score'=>'평균 점수',
    'score_all'=>'총점',
    'score_num'=>'총 평가 횟수',
    'tpl'=>'독립 템플릿',
    'jumpurl'=>'URL 이동',
    'upload_ing'=>'파일 업로드 중...',
    'install_ok'=>'설치 성공',
    'install_err'=>'설치 실패',
    'uninstall_ok'=>'제거 성공',
    'uninstall_err'=>'제거 실패',
    'url'=>'웹사이트',

    'rel_vod'=>'관련 동영상',
    'rel_art'=>'관련 기사',

    'opt_ok'=>'작업 성공',
    'opt_err'=>'작업 실패',
    'update_ok'=>'업데이트 성공',
    'update_err'=>'업데이트 실패',
    'follow_global'=>'전역 설정 따르기',

    'btn_save' =>'저 장',
    'btn_reset' =>'초기화',
    'btn_search' =>'검색',
    'search_data'=>'데이터 검색',

    'save_ok'=>'저장 성공!',
    'save_err'=>'저장 실패!',

    'write_err'=>'파일 쓰기 실패, 다시 시도하십시오!',
    'write_err_config'=>'구성 파일 쓰기 실패, 다시 시도하십시오!',
    'write_err_database'=>'데이터베이스 구성 쓰기 실패, 다시 시도하십시오!',
    'write_err_route'=>'라우팅 구성 쓰기 실패, 다시 시도하십시오!',
    'wirte_err_codefile'=>'코드 파일 저장 실패, 다시 시도하십시오!',
    'import_err'=>'가져오기 실패, 파일 형식을 확인하십시오',
    'import_ok'=>'가져오기 완료',
    'import'=>'가져오기',
    'import_all'=>'전체 가져오기',
    'export'=>'내보내기',
    'code'=>'코드',
    'sms_not_config'=>'SMS 전송 서비스가 구성되지 않았습니다.',
    'email_not_config'=>'메일함 전송 서비스가 구성되지 않았습니다.',
    'phone_format_err'=>'휴대폰 번호 형식이 올바르지 않습니다.',
    'email_format_err'=>'메일함 형식이 올바르지 않습니다.',
    'format_err'=>'형식 오류',
    'title_not_empty'=>'제목은 비워 둘 수 없습니다.',
    'body_not_empty'=>'제목은 비워 둘 수 없습니다.',
    'tpl_not'=>'템플릿 번호는 비워 둘 수 없습니다.',
    'sms_not'=>'해당 SMS 전송 방법을 찾을 수 없습니다.',
    'email_not'=>'해당 이메일 전송 방법을 찾을 수 없습니다.',

    'counting_points'=>'포인트 계산',
    'counting_times'=>'시간 계산',
    'counting_ips'=>'IP 범위',

    'mobile'=>'휴대폰',
    'email'=>'메일함',
    'verify'=>'인증 코드',
    'account'=>'계정',
    'local_app'=>'로컬 애플리케이션',
    'online_app'=>'앱 스토어',
    'local_setup'=>'오프라인 설치',
    'bind_no'=>'바인딩 계정',
    'bind'=>'바인딩',
    'unbind'=>'바인딩 해제',

    'install'=>'설치',
    'uninstall'=>'제거',
    'detail'=>'자세히 보기',
    'config'=>'구성',
    'author'=>'저자',
    'intro'=>'소개',
    'ver'=>'버전',
    'time'=>'시간',

    'update_time'=>'업데이트 시간',
    'add_time'=>'추가 시간',
    'use_time'=>'사용 시간',
    'cj_time'=>'수집 시간',
    'reply_time'=>'답변 시간',
    'log_time'=>'로그 시간',
    'reg_time'=>'가입 시간',
    'related'=>'관련',

    'card_no'=>'카드 번호',
    'money'=>'금액',
    'rule'=>'규칙',
    'mixing'=>'혼합',
    'number'=>'숫자',
    'abc'=>'문자',

    'seo_key'=>'SEO 키워드',
    'seo_des'=>'SEO 설명',
    'seo_title'=>'SEO 제목',
    'transfer'=>'이동',
    'parent_type_id'=>'상위 카테고리 번호',
    'type_id'=>'카테고리 번호',
    'type_name'=>'카테고리 이름',
    'last_login_time'=>'마지막 로그인 시간',
    'last_login_ip'=>'마지막 로그인 IP',
    'login_num'=>'로그인 횟수',
    'popedom'=>'권한',
    'check_all'=>'전체 선택',
    'check_other'=>'선택 반전',
    'pass'=>'비밀번호',
    'clear_confirm'=>'데이터를 지우시겠습니까?',
    'audit_confirm'=>'데이터를 심사하시겠습니까?',
    'blacklist_keywords' => '블랙리스트 키워드',
    'blacklist_ip' => '블랙리스트 IP',

    'clear'=>'지우기',
    'del_auto_keep_min'=>'중복 삭제[작은 ID 유지]',
    'del_auto_keep_max'=>'중복 삭제[큰 ID 유지]',
    'update_repeat_cache'=>'캐시 업데이트',
    'num_id'=>'숫자 ID',
    'encode_id'=>'암호화 ID',
    'vod_id'=>'동영상 ID',
    'art_id'=>'기사 ID',
    'type_id'=>'카테고리 ID',
    'topic_id'=>'특집 ID',
    'actor_id'=>'배우 ID',
    'role_id'=>'역할 ID',
    'website_id'=>'웹사이트 ID',
    'extend_class'=>'확장 카테고리',
    'extend_area'=>'확장 지역',
    'extend_lang'=>'확장 언어',
    'extend_year'=>'확장 연도',
    'page_title'=>'페이지 제목',
    'page_note'=>'페이지 메모',
    'page_content'=>'페이지 콘텐츠',
    'page_add'=>'페이지 콘텐츠 추가',
    'from'=>'출처',
    'paging'=>'페이지 매김',
    'referer'=>'리퍼러',
    'access_pwd'=>'액세스 비밀번호',
    'pwd_url'=>'비밀번호 링크',
    'pwd_play'=>'재생 페이지 비밀번호',
    'pwd_detail'=>'콘텐츠 페이지 비밀번호',
    'play_group'=>'재생 그룹',
    'down_group'=>'다운로드 그룹',

    'pwd_down'=>'다운로드 페이지 비밀번호',
    'not_static_is_ok'=>'비정적 모드에서 사용 가능',
    'points'=>'포인트',
    'points_all'=>'전체 포인트',
    'points_play'=>'재생 포인트',
    'points_down'=>'다운로드 포인트',
    'points_detail'=>'페이지당 포인트',
    'model'=>'모듈',
    'total'=>'총',
    'nickname'=>'닉네임',
    'data_needs_processed'=>'개의 데이터를 처리해야 합니다.',
    'per_page'=>'페이지당',
    'data'=>'개',
    'page'=>'페이지',
    'processing'=>'처리 중',
    'permission_denied'=>'이 페이지에 액세스할 권한이 없습니다.',
    'illegal_request'=>'잘못된 요청입니다.',
    'token_err'=>'양식을 반복해서 제출하지 마십시오.',
    'dir'=>'폴더',
    'file'=>'파일',
    'file_name'=>'파일 이름',
    'file_size'=>'파일 크기',
    'file_time'=>'파일 시간',
    'file_des'=>'파일 설명',
    'occupies'=>'차지',
    'space'=>'공간',
    'return_parent_dir'=>'상위 디렉토리로 돌아가기',
    'phone'=>'전화',
    'server_rest'=>'서버를 잠시 쉬게 한 다음 계속하십시오.',
    'to'=>'~까지',
    'director'=>'감독',
    'clear_ok'=>'정리 성공',
    'clear_err'=>'정리 실패, 다시 시도하십시오.',
    'unknown'=>'알 수 없음',
    'unknown_type'=>'알 수 없는 카테고리',
    'obtain_ok'=>'가져오기 성공',
    'obtain_err'=>'가져오기 실패',
    'download_ok'=>'다운로드 성공',
    'download_err'=>'다운로드 실패',
    'expand_all'=>'전체 확장',
    'fold_all'=>'전체 축소',
    'data'=>'데이터',
    'today_data'=>'오늘의 데이터',
    'no_make_data'=>'생성되지 않은 데이터',
    'get_info_err'=>'정보 가져오기 실패',
    'rnd_data'=>'무작위 데이터',
    'success'=>'성공',
    'diy_ids'=>'사용자 지정 ID',
    'fail'=>'실패',
    'duplicate_data'=>'중복 데이터',
    'distinct_into'=>'중복 제거 후 데이터베이스에 입력',
    'comment_name'=>'댓글 닉네임',
    'comment_content'=>'댓글 내용',

    'page_not_found'=>'페이지를 찾을 수 없습니다.',
    'search_close'=>'검색 기능이 닫혔습니다.',
    'show_close'=>'필터 페이지 기능이 닫혔습니다.',
    'ajax_close'=>'Ajax 페이지 기능이 닫혔습니다.',
    'frequently'=>'너무 자주 작동하지 마십시오.',
    'search_frequently'=>'너무 자주 작동하지 마십시오. 검색 간격은 ',
    'score_ok'=>'참여해 주셔서 감사합니다. 평가가 완료되었습니다.',
    'suggest_close'=>'연관 검색 기능이 닫혔습니다.',

    'please_try_again'=>'다시 시도하십시오',
    'data_list'=>'데이터 목록',
    'data_not_found'=>'데이터를 찾을 수 없습니다.',
    'unverified'=>'확인되지 않음',
    'verified'=>'확인됨',
    'registered'=>'가입됨',
    'register'=>'가입',
    'findpass'=>'비밀번호 찾기',
    'access_or_pass_err'=>'계정 또는 비밀번호가 잘못되었습니다.',
    'playurl'=>'재생 주소',
    'downurl'=>'다운로드 주소',
    'serial'=>'연재 수',
    'writer'=>'작가',
    'version'=>'리소스 버전',
    'state'=>'리소스 카테고리',
    'tv'=>'TV 채널',
    'weekday'=>'프로그램 주기',
    'isend'=>'완결',
    'total'=>'총 화수',
    'replace'=>'바꾸기',
    'merge'=>'병합',
    'douban_id'=>'Douban ID',
    'rel_name'=>'관련 데이터 이름',
    'preview'=>'미리보기',
    'screenshot_preview'=>'스크린샷 미리보기',
    'screenshot_tip'=>'줄당 하나의 이미지 주소, 원격 URL, 로컬 경로, 사용자 지정 이름 메모 지원, 예:
사진1$upload/test.jpg
사진2$https://www.baidu.com/logo.png
https://www.baidu.com/123.jpg
    ',

    'menu/index'=>'홈페이지',
    'menu/welcome'=>'환영 페이지',
    'menu/quickmenu'=>'사용자 지정 메뉴 구성',
    'menu/system'=>'시스템',
    'menu/config'=>'웹사이트 매개변수 구성',
    'menu/configseo'=>'SEO 매개변수 구성',
    'menu/configuser'=>'회원 매개변수 구성',
    'menu/configcomment'=>'댓글 메시지 구성',
    'menu/configupload'=>'첨부 파일 매개변수 구성',
    'menu/configurl'=>'URL 주소 구성',
    'menu/configplay'=>'플레이어 매개변수 구성',
    'menu/configcollect'=>'수집 매개변수 구성',
    'menu/configinterface'=>'외부 데이터베이스 구성',
    'menu/configapi'=>'오픈 API 구성',
    'menu/configconnect'=>'통합 로그인 구성',
    'menu/configpay'=>'온라인 결제 구성',
    'menu/configweixin'=>'WeChat 연결 구성',
    'menu/configemail'=>'이메일 전송 구성',
    'menu/configsms'=>'SMS 전송 구성',
    'menu/timming'=>'예약 작업 구성',
    'menu/domain'=>'웹사이트 그룹 관리 구성',
    'menu/base'=>'기본',
    'menu/type'=>'카테고리 관리',
    'menu/topic'=>'특집 관리',
    'menu/link'=>'링크 관리',
    'menu/gbook'=>'방명록 관리',
    'menu/comment'=>'댓글 관리',
    'menu/images'=>'첨부 파일 관리',
    'menu/art'=>'기사',
    'menu/art_data'=>'기사 데이터',
    'menu/art_add'=>'기사 추가',
    'menu/art_data_lock'=>'잠긴 기사',
    'menu/art_data_audit'=>'심사되지 않은 기사',
    'menu/art_batch'=>'기사 일괄 작업',
    'menu/art_repeat'=>'중복된 기사 데이터',
    'menu/vod'=>'동영상',
    'menu/server'=>'서버 그룹',
    'menu/player'=>'플레이어',
    'menu/downer'=>'다운로더',
    'menu/vod_data'=>'동영상 데이터',
    'menu/vod_add'=>'동영상 추가',
    'menu/vod_data_url_empty'=>'주소 없는 동영상',
    'menu/vod_data_lock'=>'잠긴 동영상',
    'menu/vod_data_audit'=>'심사되지 않은 동영상',
    'menu/vod_data_points'=>'포인트 필요한 동영상',
    'menu/vod_data_plot'=>'에피소드 줄거리가 있는 동영상',
    'menu/vod_batch'=>'동영상 일괄 작업',
    'menu/vod_repeat'=>'중복된 동영상 데이터',
    'menu/actor'=>'배우',
    'menu/role'=>'역할',
    'menu/website'=>'웹사이트',
    'menu/website_data'=>'웹사이트 데이터',
    'menu/website_add'=>'웹사이트 추가',
    'menu/website_data_lock'=>'잠긴 웹사이트',
    'menu/website_data_audit'=>'심사되지 않은 웹사이트',
    'menu/website_batch'=>'웹사이트 일괄 작업',
    'menu/website_repeat'=>'중복된 웹사이트 데이터',
    'menu/users'=>'사용자',
    'menu/admin'=>'관리자',
    'menu/group'=>'회원 그룹',
    'menu/user'=>'회원',
    'menu/card'=>'충전 카드',
    'menu/order'=>'회원 주문',
    'menu/ulog'=>'액세스 로그',
    'menu/plog'=>'포인트 로그',
    'menu/cash'=>'출금 기록',
    'menu/templates'=>'템플릿',
    'menu/template'=>'템플릿 관리',
    'menu/ads'=>'광고 공간 관리',
    'menu/wizard'=>'태그 마법사',
    'menu/make'=>'생성',
    'menu/make_opt'=>'생성 옵션',
    'menu/make_index'=>'홈페이지 생성',
    'menu/make_index_wap'=>'WAP 홈페이지 생성',
    'menu/make_map'=>'지도 생성',
    'menu/cjs'=>'수집',
    'menu/union'=>'리소스 추천',
    'menu/collect_timming'=>'예약 수집',
    'menu/collect'=>'사용자 지정 인터페이스',
    'menu/cj'=>'사용자 지정 규칙',
    'menu/db'=>'데이터베이스',
    'menu/database'=>'데이터베이스 관리',
    'menu/database_sql'=>'SQL 문 실행',
    'menu/database_rep'=>'데이터 일괄 바꾸기',
    'menu/database_inspect'=>'악성코드 검사',
    'menu/apps'=>'애플리케이션',
    'menu/addon'=>'앱 마켓',
    'menu/urlsend'=>'URL 푸시',
    'menu/safety_file'=>'파일 보안 검사',
    'menu/safety_data'=>'데이터 악성코드 검사',

    'model/admin/update_login_err'=>'로그인 정보 업데이트 실패',
    'model/admin/login_ok'=>'로그인 성공',
    'model/admin/logout_ok'=>'로그아웃 성공',
    'model/admin/not_login'=>'로그인하지 않았습니다.',
    'model/admin/haved_login'=>'로그인했습니다.',

    'model/card/not_found'=>'충전 카드 정보가 잘못되었습니다. 다시 시도하십시오.',
    'model/card/update_user_points_err'=>'사용자 포인트 업데이트 실패, 다시 시도하십시오.',
    'model/card/update_card_status_err'=>'충전 카드 상태 업데이트 실패, 다시 시도하십시오.',
    'model/card/used_card_ok'=>'충전 성공, 포인트 추가됨【%s】',

    'model/cash/not_open'=>'출금 기능이 활성화되지 않았습니다!',
    'model/cash/min_money_err'=>'최소 출금 금액!',
    'model/cash/mush_money_err'=>'출금 금액이 너무 많습니다. 포인트가 부족합니다!',

    'model/collect/flag_err'=>'플래그 식별자가 잘못되었습니다. 불법 요청을 하지 마십시오!',
    'model/collect/cjurl_err'=>'수집 링크가 잘못되었거나 로컬 링크일 수 없습니다.',
    'model/collect/get_html_err'=>'API 인터페이스 연결 실패, 일반적으로 서버 네트워크가 불안정하거나 IP가 차단되었거나 관련 함수가 비활성화되었기 때문입니다!',
    'model/collect/json_err'=>'JSON 형식이 올바르지 않아 수집을 지원하지 않습니다.',
    'model/collect/xml_err'=>'XML 형식이 올바르지 않아 수집을 지원하지 않습니다.',
    'model/collect/data_tip1'=>'현재 수집 작업 <strong class="green">%s</strong>/<span class="green">%s</span>페이지 수집 주소&nbsp;%s',
    'model/collect/type_err'=>'카테고리가 바인딩되지 않았습니다. 건너뛰기 오류',
    'model/collect/name_in_filter_err'=>'데이터가 필터 목록에 있습니다. 건너뛰기 오류',
    'model/collect/name_err'=>'데이터가 완전하지 않습니다. 건너뛰기 오류',
    'model/collect/not_check_add'=>'데이터 작업에서 추가를 선택하지 않았습니다. 건너뛰기.',
    'model/collect/not_check_update'=>'데이터 작업에서 업데이트를 선택하지 않았습니다. 건너뛰기.',
    'model/collect/add_ok'=>'데이터베이스에 성공적으로 추가되었습니다. 성공.',
    'model/collect/uprule_empty'=>'2차 업데이트 항목이 설정되지 않았습니다. 건너뛰기.',
    'model/collect/data_lock'=>'데이터가 이미 잠겨 있습니다. 건너뛰기.',
    'model/collect/not_need_update'=>'업데이트할 필요가 없습니다.',
    'model/collect/is_over'=>'데이터 수집이 완료되었습니다.',
    'model/collect/not_found_rel_vod'=>'관련 동영상을 찾을 수 없어 연결할 수 없습니다. 건너뛰기.',
    'model/collect/not_found_rel_data'=>'관련 데이터를 찾을 수 없어 연결할 수 없습니다. 건너뛰기.',
    'model/collect/role_data_require'=>'데이터가 완전하지 않습니다. role_name, role_actor, vod_name은 필수입니다. 건너뛰기 오류.',
    'model/collect/actor_data_require'=>'데이터가 완전하지 않습니다. actor_name, actor_sex는 필수입니다. 건너뛰기 오류.',
    'model/collect/comment_data_require'=>'데이터가 완전하지 않습니다. comment_content, comment_name, rel_name은 필수입니다. 건너뛰기 오류.',
    'model/collect/playurl_same'=>'재생 주소가 동일합니다. 건너뛰기.',
    'model/collect/playfrom_empty'=>'플레이어 유형이 비어 있습니다. 건너뛰기.',
    'model/collect/downurl_same'=>'다운로드 주소가 동일합니다. 건너뛰기.',
    'model/collect/downfrom_empty'=>'다운로더 유형이 비어 있습니다. 건너뛰기.',

    'model/collect/playgroup_add_ok'=>'재생 그룹(%s), 추가 성공.',
    'model/collect/playgroup_same'=>'재생 그룹(%s), 업데이트할 필요가 없습니다.',
    'model/collect/playgroup_update_ok'=>'재생 그룹(%s), 업데이트 성공.',

    'model/collect/downgroup_add_ok'=>'다운로드 그룹(%s), 추가 성공.',
    'model/collect/downgroup_same'=>'다운로드 그룹(%s), 업데이트할 필요가 없습니다.',
    'model/collect/downgroup_update_ok'=>'다운로드 그룹(%s), 업데이트 성공.',

    'model/group/have_user'=>'사용자 그룹에 아직 사용자가 있습니다.',
    'model/order/pay_over'=>'주문이 이미 결제되었습니다.',
    'model/order/update_status_err'=>'주문 상태 업데이트 실패',
    'model/order/update_user_points_err'=>'회원 포인트 업데이트 실패',
    'model/order/pay_ok'=>'충전 완료, 콜백 함수 실행 성공',

    'model/type/to_info_err'=>'대상 카테고리 정보 가져오기 실패',
    'model/type/move_err'=>'이동 실패',
    'model/type/move_ok'=>'이동 실패',

    'model/user/not_open_reg'=>'가입이 열려 있지 않습니다.',
    'model/user/input_require'=>'필수 항목을 작성하십시오.',
    'model/user/pass_not_pass2'=>'비밀번호와 비밀번호 확인이 일치하지 않습니다.',
    'model/user/haved_reg'=>'사용자 이름이 이미 사용 중입니다. 변경하십시오.',
    'model/user/name_contain'=>'사용자 이름에는 문자와 숫자만 포함될 수 있습니다. 변경하십시오.',
    'model/user/name_filter'=>'사용자 이름에는 %s와 같은 문자가 포함될 수 없습니다. 다시 시도하십시오.',
    'model/user/ip_limit'=> 'IP당 매일 %s회 가입 제한',
    'model/user/phone_haved'=> '휴대폰 번호가 이미 사용 중입니다. 변경하십시오.',
    'model/user/email_haved'=> '이미 사용된 이메일 주소입니다. 변경해 주세요.',
    'model/user/reg_err'=> '가입 실패, 다시 시도하십시오.',
    'model/user/reg_ok'=>'가입 성공, 로그인 후 회원 센터에서 개인 정보를 입력하십시오.',
    'model/user/input_old_pass'=> '기존 비밀번호를 입력하십시오.',
    'model/user/old_pass_err'=> '기존 비밀번호가 잘못되었습니다.',
    'model/user/pass_not_same_pass2'=> '새 비밀번호를 두 번 입력한 것이 일치하지 않습니다.',
    'model/user/not_found'=>'사용자 정보를 찾을 수 없습니다.',
    'model/user/update_login_err'=>'로그인 정보 업데이트 실패',
    'model/user/update_expire_err'=>'회원 그룹 만료 정보 업데이트 실패',
    'model/user/update_expire_ok'=>'만료 정보 업데이트 성공',
    'model/user/login_ok'=>'로그인 성공',
    'model/user/logout_ok'=>'로그아웃 성공',
    'model/user/not_login'=>'로그인하지 않았습니다.',
    'model/user/haved_login'=>'로그인했습니다.',
    'model/user/findpass_not_found'=>'사용자를 찾을 수 없습니다. 계정, 질문 또는 답변이 올바르지 않을 수 있습니다.',
    'model/user/findpass_ok'=>'비밀번호 찾기 성공',
    'model/user/select_diy_group_err'=>'사용자 지정 유료 회원 그룹을 선택하십시오.',
    'model/user/group_not_found'=>'회원 그룹 정보를 찾을 수 없습니다.',
    'model/user/group_is_close'=>'회원 그룹이 이미 닫혀 업그레이드할 수 없습니다.',
    'model/user/potins_not_enough'=>'포인트가 부족하여 업그레이드할 수 없습니다.',
    'model/user/update_group_err'=>'회원 그룹 업그레이드 실패',
    'model/user/update_group_ok'=>'회원 그룹 업그레이드 성공',
    'model/user/msg_not_found'=>'인증 정보가 잘못되었습니다. 다시 시도하십시오.',
    'model/user/do_not_send_frequently'=>'너무 자주 보내지 마십시오.',
    'model/user/msg_send_ok'=>'인증 코드가 성공적으로 전송되었습니다. 확인하십시오.',
    'model/user/msg_send_err'=>'인증 코드 전송 실패',
    'model/user/update_bind_err'=>'사용자 바인딩 정보 업데이트 실패',
    'model/user/update_bind_ok'=>'바인딩 성공',
    'model/user/update_unbind_ok'=>'바인딩 해제 성공',
    'model/user/pass_length_err'=>'비밀번호는 6자 이상이어야 합니다.',
    'model/user/email_format_err'=>'메일함 주소 형식이 올바르지 않습니다.',
    'model/user/email_err'=>'메일함 주소가 올바르지 않습니다.',
    'model/user/email_host_not_allowed'=>'메일함 도메인이 허용되지 않습니다.',
    'model/user/phone_format_err'=>'휴대폰 번호 형식이 올바르지 않습니다.',
    'model/user/phone_err'=>'휴대폰 번호가 올바르지 않습니다.',
    'model/user/pass_reset_err'=>'비밀번호 재설정 실패, 다시 시도하십시오.',
    'model/user/pass_reset_ok'=>'비밀번호 재설정 성공',
    'model/user/id_err'=>'사용자 번호 오류',
    'model/user/visit_tip'=>'하루에 %s회만 프로모션 방문 포인트를 받을 수 있습니다.',
    'model/user/visit_err'=>'프로모션 레코드 삽입 실패, 다시 시도하십시오.',
    'model/user/visit_ok'=>'프로모션 성공',
    'model/user/reward_tip'=>'사용자【%s、%s】가 %s 포인트를 소비하고 %s 포인트를 보상으로 받았습니다.',
    'model/user/reward_ok'=>'배포 수수료 성공',

    'model/website/refer_max'=> '하루에 %s회만 리퍼러 레코드를 기록할 수 있습니다.',
    'model/website/visit_err'=>'리퍼러 기록 실패, 다시 시도하십시오.',
    'model/website/visit_ok'=>'리퍼러 기록 성공',

    'controller/no_popedom'=>'이 데이터에 액세스할 권한이 없습니다. 회원을 업그레이드하십시오.',
    'controller/pay_play_points'=>'이 데이터를 보려면 【%s】 포인트를 지불해야 합니다. 결제하시겠습니까?',
    'controller/pay_down_points'=>'이 데이터를 다운로드하려면 【%s】 포인트를 지불해야 합니다. 결제하시겠습니까?',
    'controller/in_try_see'=>'미리보기 모드로 들어갑니다.',
    'controller/charge_data'=>'이 페이지는 유료 데이터입니다. 로그인 후 액세스하십시오!',
    'controller/try_see_end'=>'미리보기가 끝났습니다. [%s] 포인트를 지불하고 전체 데이터를 보시겠습니까? [%s] 포인트가 남았습니다. 먼저 충전하십시오!',
    'controller/not_enough_points'=>'죄송합니다. 이 페이지 데이터를 보려면 [%s] 포인트가 필요합니다. [%s] 포인트가 남았습니다. 먼저 충전하십시오!',
    'controller/popedom_ok'=>'권한 인증 통과',
    'controller/an_error_occurred'=>'오류 발생',
    'controller/visitor'=>'방문자',
    'controller/get_type_err'=>'카테고리를 가져오지 못했습니다. 다른 카테고리를 선택하십시오!',

    'index/require_login'=>'로그인 후 메시지를 게시할 수 있습니다.',
    'index/require_content'=>'내용은 비워 둘 수 없습니다.',
    'index/require_cn'=>'내용에는 중국어가 포함되어야 합니다. 다시 입력하십시오.',
    'index/mid_err'=>'모델 MID 오류',
    'index/thanks_msg_audit'=>'감사합니다. 메시지를 최대한 빨리 검토하겠습니다!',
    'index/thanks_msg'=>'메시지 감사합니다!',
    'index/blacklist_keyword'=>'당신의 댓글에 민감한 단어가 포함되어 있습니다. 수정 후 다시 제출해 주세요！',
    'index/blacklist_ip'=>'댓글 금지!',
    'index/blacklist_placeholder'=>'블랙리스트 키워드를 입력해주세요. 각 키워드는 한 줄에 하나씩',
    'index/blacklist_placeholder_ip'=>'블랙리스트 IP를 입력해주세요. 각 IP는 한 줄에 하나씩. IP 형식이 아닌 제출은 제출 후 필터링됩니다.',
    'index/payment_status'=>'이 결제 옵션이 활성화되지 않았습니다!',
    'index/payment_not'=>'결제 옵션을 찾을 수 없습니다!',
    'index/payment_ok'=>'결제 완료!',
    'index/haved'=>'이미 참여했습니다!',
    'index/ok'=>'작업 성공!',
    'index/pwd_repeat'=>'반복해서 인증하지 마십시오!',
    'index/pwd_frequently'=>'너무 자주 요청하지 마십시오. 나중에 다시 시도하십시오!',
    'index/pwd_repeat'=>'반복해서 인증하지 마십시오!',
    'index/no_login'=>'로그인하지 않았습니다.',
    'index/ulog_fee'=>'유료 영수증은 별도로 기록해야 합니다.',
    'index/buy_popedom1'=>'이미 이 데이터를 구입했습니다. 다시 지불할 필요가 없습니다. 페이지를 새로 고침하고 다시 시도하십시오!',
    'index/buy_popedom2'=>'죄송합니다. 사용자 포인트 정보 업데이트에 실패했습니다. 새로 고침하고 다시 시도하십시오!',
    'index/buy_popedom3'=>'죄송합니다. 이 페이지 데이터를 보려면 [%s] 포인트가 필요합니다. [%s] 포인트가 남았습니다. 먼저 충전하십시오!',
    'index/bind_haved'=>'이미 이 계정에 바인딩되었습니다.',
    'index/bind_ok'=>'바인딩 성공',
    'index/logincallback1'=>'동기화 정보 등록 실패, 관리자에게 문의하십시오.',
    'index/logincallback2'=>'타사 사용자 정보를 가져오지 못했습니다. 다시 시도하십시오.',
    'index/reg_ok'=> '가입 성공',
    'index/portrait_tip1'=> '사용자 지정 프로필 사진 기능이 활성화되지 않았습니다.',
    'index/portrait_no_upload'=> '업로드된 파일을 찾을 수 없습니다(이유: 양식 이름이 잘못되었을 수 있습니다. 기본 양식 이름은 "file" 또는 "imgdata"입니다)!',
    'index/portrait_ext'=> '시스템에서 허용하지 않는 업로드 형식입니다!',
    'index/upload_err'=>'파일 업로드 실패!',
    'index/portrait_err'=>'회원 프로필 사진 정보 업데이트 실패!',
    'index/portrait_thumb_err'=>'축소판 프로필 사진 이미지 파일 생성 실패!',
    'index/min_pay'=>'최소 충전 금액은 %s원보다 낮을 수 없습니다.',
    'index/order_not'=>'주문을 찾을 수 없습니다.',
    'index/order_payed'=>'이 주문은 이미 결제되었습니다.',
    'index/page_type'=>'목록 페이지',
    'index/page_detail'=>'콘텐츠 페이지',
    'index/page_play'=>'재생 페이지',
    'index/page_down'=>'다운로드 페이지',
    'index/try_see'=>'미리보기',

    'admin/public/head/title'=>'보안을 위해 백엔드 주소를 누출하지 마십시오. - Copyright by Apple CMS 콘텐츠 관리 시스템',
    'admin/public/jump/title'=>'이동 팁',

    'admin/index/login/title'=>'백엔드 관리 센터 - Copyright by Apple CMS 콘텐츠 관리 시스템',
    'admin/index/login/tip_welcome'=>'웹사이트 이용을 환영합니다',
    'admin/index/login/tip_sys'=>'시스템 관리',
    'admin/index/login/filed_no'=>'계정',
    'admin/index/login/filed_pass'=>'비밀번호',
    'admin/index/login/filed_verify'=>'인증 코드',
    'admin/index/login/btn_submit'=>'로그인',
    'admin/index/login/tip_declare'=>'면책 조항',
    'admin/index/login/tip_declare_txt'=>'이 프로그램은 오픈 소스이며 영구적으로 무료이며 내장 데이터가 없습니다. 현지 법률을 준수하여 사용하십시오. 사용 과정에서 발생하는 정보 내용에 대해 이 프로그램은 책임을 지지 않습니다! 자유! 평등! 공유! 오픈 소스!',
    'admin/index/login/verify_no'=>'사용자 이름을 입력하십시오.',
    'admin/index/login/verify_pass'=>'비밀번호를 입력하십시오.',
    'admin/index/login/verify_verify'=>'인증 코드를 입력하십시오.',

    'admin/index/index/name' =>'슈퍼 제어판',
    'admin/index/index/menu_switch' =>'왼쪽 탐색 열기/닫기',
    'admin/index/index/menu_index' =>'웹사이트 홈페이지',
    'admin/index/index/menu_lock' =>'잠금 화면 작업',
    'admin/index/index/menu_logout' =>'로그아웃',
    'admin/index/index/menu_cache' =>'캐시',
    'admin/index/index/menu_cache_clear' =>'캐시 지우기',
    'admin/index/index/menu_welcome' =>'환영 페이지',
    'admin/index/index/menu_opt' =>'작업',
    'admin/index/index/menu_close_all' =>'전체 닫기',
    'admin/index/index/menu_close_other' =>'기타 닫기',
    'admin/index/index/menu_max' =>'최대 10개의 탭 페이지를 열 수 있습니다.',
    'admin/index/index/menu_close_empty' =>'닫을 수 있는 창이 없습니다. @_@',

    'admin/index/quickmenu/name' =>'사용자 지정 바로 가기 메뉴',
    'admin/index/quickmenu/tip' =>'형식 요구 사항: 1. 메뉴 이름, 메뉴 링크 주소; 2. 각 바로 가기 메뉴는 한 줄을 차지합니다. <br>
        1. 원격 주소 지원, 예: 업데이트 로그, //www.baidu.com/ <br>
        2. 플러그인 파일 지원, 예: 플러그인 파일 메뉴, /application/xxxx.html <br>
        3. 시스템 모듈 지원, 예: 기사 관리, art/data <br>
        4. 줄 구분 기호 지원, 예: 구분 기호, ###',


    'admin/index/welcome/filed_os' =>'운영 환경',
    'admin/index/welcome/filed_host' =>'서버 IP/포트',
    'admin/index/welcome/filed_php_ver' =>'PHP 버전',
    'admin/index/welcome/filed_thinkphp_ver' =>'ThinkPHP 버전',
    'admin/index/welcome/filed_max_upload' =>'최대 업로드 제한',
    'admin/index/welcome/filed_date' =>'서버 날짜',
    'admin/index/welcome/filed_ver' =>'프로그램 버전',
    'admin/index/welcome/filed_license' =>'라이선스 유형',
    'admin/index/welcome/tip_update_db' =>'데이터베이스 업데이트 팁',
    'admin/index/welcome/tip_update_db_txt' =>'팁: 로컬에 데이터베이스 업그레이드 스크립트가 있는 것으로 감지되었습니다. 업그레이드 작업을 실행하시겠습니까? 실행이 완료되면 스크립트가 자동으로 삭제됩니다!',
    'admin/index/welcome/tip_update_go'=>'【데이터베이스 업그레이드 스크립트 업그레이드로 이동하려면 클릭】',
    'admin/index/welcome/filed_login_num' =>'로그인 횟수',
    'admin/index/welcome/filed_last_login_ip' =>'마지막 로그인 IP',
    'admin/index/welcome/filed_last_login_time' =>'마지막 로그인 시간',
    'admin/index/welcome/tip_warn' =>'업그레이드 시 오류가 발생하지 않도록 시스템 파일을 수정하지 마십시오! 이 프로그램에는 데이터가 내장되어 있지 않으며 데이터 추가는 개인의 행위입니다! 법률을 준수하여 프로그램을 사용하십시오. 그렇지 않으면 책임은 귀하에게 있습니다!',

    'admin/index/quick_tit'=>'↓↓↓사용자 지정 메뉴 영역↓↓↓',
    'admin/index/title'=>'백엔드 관리 센터',
    'admin/index/welcome/title'=>'환영 페이지',
    'admin/index/quickmenu/title'=>'바로 가기 메뉴 구성',
    'admin/index/cache_data'=>'구성 캐시가 감지되었습니다. 즉시 지우십시오...',
    'admin/index/clear_ok'=>'캐시 지우기 성공',
    'admin/index/clear_err'=>'캐시 지우기 실패',
    'admin/index/iframe'=>'레이아웃 전환 성공, 이동 중',
    'admin/index/pass_err'=>'비밀번호 오류',
    'admin/index/unlock_ok'=>'잠금 해제 성공',
    'admin/index/title'=>'백엔드 관리 센터',


    'admin/system/config/title'=>'웹사이트 매개변수 구성',
    'admin/system/config/base'=>'기본 설정',
    'admin/system/config/performance'=>'성능 최적화',
    'admin/system/config/parameters'=>'예약 매개변수',
    'admin/system/config/backstage'=>'백엔드 설정',
    'admin/system/config/site_name'=>'웹사이트 이름',
    'admin/system/config/site_url'=>'웹사이트 도메인',
    'admin/system/config/site_url_tip'=>'예: www.test.com, http://를 추가하지 마십시오.',
    'admin/system/config/site_wapurl'=>'모바일 사이트 도메인',
    'admin/system/config/site_wapurl_tip'=>'예: wap.test.com, http://를 추가하지 마십시오.',
    'admin/system/config/site_keywords'=>'키워드',
    'admin/system/config/site_description'=>'설명 정보',
    'admin/system/config/site_icp'=>'ICP 등록 번호',
    'admin/system/config/site_qq'=>'고객 서비스 QQ',
    'admin/system/config/site_email'=>'고객 서비스 이메일',
    'admin/system/config/install_dir'=>'설치 디렉토리',

    'admin/system/config/install_dir_tip'=>'루트 디렉토리 ＂/＂, 2차 디렉토리 ＂/maccms/＂ 등',
    'admin/system/config/site_logo'=>'기본 LOGO',
    'admin/system/config/site_waplogo'=>'모바일 사이트 LOGO',
    'admin/system/config/template_dir'=>'웹사이트 템플릿',
    'admin/system/config/site_polyfill'=>'이전 버전과의 호환성',
    'admin/system/config/site_polyfill_tip'=>'활성화하면 이전 버전의 브라우저와의 호환성을 위해 polyfill이 도입됩니다.',
    'admin/system/config/site_logo_tip'=>'이미지 주소 또는 경로',
    'admin/system/config/html_dir'=>'템플릿 디렉토리',
    'admin/system/config/mob_status'=>'반응형 모바일',
    'admin/system/config/mob_status_tip'=>'다중 도메인: WAP 도메인에 액세스하면 모바일 템플릿이 자동으로 사용됩니다. 단일 도메인: 모바일에서 액세스하면 모바일 템플릿이 자동으로 사용됩니다.',
    'admin/system/config/mob_template_dir'=>'모바일 템플릿',
    'admin/system/config/mob_one'=>'단일 도메인',
    'admin/system/config/mob_multiple'=>'다중 도메인',
    'admin/system/config/site_tj'=>'통계 코드',
    'admin/system/config/site_status'=>'사이트 상태',
    'admin/system/config/site_close_tip'=>'닫기 팁',
    'admin/system/config/pathinfo_depr'=>'PATH 구분 기호',
    'admin/system/config/pathinfo_depr_tip'=>'PATHINFO 구분 기호 수정하면 비정적 모드의 URL 주소가 변경됩니다.',
    'admin/system/config/xg'=>'슬래시 /',
    'admin/system/config/zhx'=>'대시 -',
    'admin/system/config/xhx'=>'밑줄 _',
    'admin/system/config/suffix'=>'페이지 접미사',

    'admin/system/config/wall_filter'=>'가짜 방화벽 방어',
    'admin/system/config/wall_unicode'=>'인코딩 방식',
    'admin/system/config/wall_blank'=>'공백 방식',
    'admin/system/config/wall_filter_tip'=>'활성화하면 일부 페이지에 전달된 매개변수가 페이지에 표시될 때 인코딩되거나 공백으로 바뀌어 가짜 방화벽 위협을 해결합니다.',
    'admin/system/config/popedom_filter'=>'데이터 권한 필터링',
    'admin/system/config/popedom_filter_tip'=>'활성화하면 권한이 없는 카테고리와 데이터가 숨겨집니다.',
    'admin/system/config/cache_type'=>'캐시 방식',
    'admin/system/config/cache_host'=>'서버',
    'admin/system/config/cache_port'=>'포트',
    'admin/system/config/cache_username'=>'계정',
    'admin/system/config/cache_password'=>'비밀번호',
    'admin/system/config/cache_host_tip'=>'캐시 서버 IP',
    'admin/system/config/cache_port_tip'=>'캐시 서버 포트',
    'admin/system/config/cache_username_tip'=>'캐시 서비스 계정, 없으면 비워 두십시오.',
    'admin/system/config/cache_password_tip'=>'캐시 서비스 비밀번호, 없으면 비워 두십시오.',
    'admin/system/config/cache_test'=>'연결 테스트',

    'admin/system/config/cache_flag'=>'캐시 식별자',
    'admin/system/config/cache_flag_tip'=>'여러 사이트에서 memcache, redis 서버를 공유하는 경우 구분해야 합니다.',
    'admin/system/config/cache_flag_auto'=>'비워 두면 자동으로 생성됩니다.',
    'admin/system/config/cache_core'=>'데이터 캐시',
    'admin/system/config/cache_time'=>'데이터 캐시 시간',
    'admin/system/config/cache_time_tip'=>'단위는 초이며 3600 이상으로 설정하는 것이 좋습니다.',
    'admin/system/config/cache_page'=>'페이지 캐시',
    'admin/system/config/cache_time_page'=>'페이지 캐시 시간',
    'admin/system/config/compress'=>'페이지 압축',
    'admin/system/config/search'=>'검색 스위치',
    'admin/system/config/search_verify'=>'검색 인증 코드',
    'admin/system/config/search_timespan'=>'검색 간격',
    'admin/system/config/search_timespan_tip'=>'단위는 초이며 3초 이상으로 설정하는 것이 좋습니다.',
    'admin/system/config/search_len'=>'검색 매개변수 길이',
    'admin/system/config/search_len_tip'=>'검색 페이지 + 필터 페이지 단일 매개변수 길이 제한, 기본값은 10자이며 초과하면 자동으로 잘립니다.',
    'admin/system/config/404'=>'404 페이지',
    'admin/system/config/404_tip'=>'사용자 지정 404 페이지, 페이지는 템플릿의 public 디렉토리에 접미사 없이 배치되며 기본값은 jump입니다.',
    'admin/system/config/show'=>'필터 페이지 스위치',
    'admin/system/config/show_verify'=>'필터 인증 코드',
    'admin/system/config/input_type'=>'매개변수 가져오기 방식',
    'admin/system/config/input_type_tip'=>'GET 방식을 사용하는 것이 좋습니다. 안전하고 로그를 쉽게 분석할 수 있습니다.',
    'admin/system/config/ajax_page'=>'예약 Ajax 스위치',
    'admin/system/config/ajax_page_tip'=>'시스템의 각 페이지 요청에는 해당 Ajax 메서드가 예약되어 있으며 필요하지 않으면 닫는 것이 좋습니다. 예: vod/search 및 vod/ajax_search',
    'admin/system/config/search_vod_rule'=>'동영상 검색 규칙',
    'admin/system/config/search_rule_tip'=>'참고: wd 매개변수에만 영향을 미치며 너무 많이 선택하면 성능에 영향을 미치므로 3개 이내로 선택하는 것이 좋습니다.',
    'admin/system/config/search_art_rule'=>'기사 검색 규칙',
    'admin/system/config/vod_search_optimise'=>'동영상 검색 최적화',
    'admin/system/config/vod_search_optimise/frontend'=>'프런트엔드',
    'admin/system/config/vod_search_optimise/collect'=>'수집',
    'admin/system/config/vod_search_optimise_tip'=>'LIKE 퍼지 쿼리의 결과 캐시',
    'admin/system/config/vod_search_optimise_cache_minutes'=>'검색 캐시 분',
    'admin/system/config/vod_search_optimise_cache_minutes_tip'=>'동영상 LIKE 퍼지 쿼리 캐시 시간(분), 최소 1분, 60분 이상 권장',
    'admin/system/config/copyright_status'=>'저작권 팁',
    'admin/system/config/copyright_msg'=>'팁 정보',
    'admin/system/config/copyright_jump_detail'=>'콘텐츠 페이지 이동',
    'admin/system/config/copyright_jump_play'=>'재생 페이지 이동',
    'admin/system/config/copyright_jump_iframe'=>'iframe 재생 페이지 이동',
    'admin/system/config/copyright_notice'=>'저작권 팁 정보',
    'admin/system/config/browser_junmp'=>'리디렉션 차단',
    'admin/system/config/browser_junmp_tip'=>'WeChat, QQ를 사용하여 액세스하면 리디렉션 팁 페이지가 직접 표시됩니다.',
    'admin/system/config/collect_timespan'=>'수집 간격',
    'admin/system/config/collect_timespan_tip'=>'단위는 초이며 3초 이상으로 설정하는 것이 좋습니다.',
    'admin/system/config/pagesize'=>'백엔드 페이지당 항목 수',
    'admin/system/config/pagesize_tip'=>'페이지당 표시되는 데이터 양, 일반적으로 약 20개로 설정',
    'admin/system/config/lang'=>'백엔드 언어 팩',

    'admin/system/config/makesize'=>'페이지당 생성 수',
    'admin/system/config/makesize_tip'=>'일괄 생성 시 매번 생성되는 페이지 수가 적습니다. 일반적으로 약 20개로 설정',
    'admin/system/config/admin_login_verify'=>'백엔드 로그인 인증 코드',
    'admin/system/config/editor'=>'리치 텍스트 편집기',
    'admin/system/config/editor_tip'=>'시스템에는 기본적으로 ueditor가 포함되어 있으며 다른 편집기를 사용하려면 먼저 공식 웹사이트에서 확장 팩을 다운로드하십시오.',
    'admin/system/config/player_sort'=>'플레이어 순서',
    'admin/system/config/player_sort_tip'=>'프런트엔드에 표시되는 플레이어 순서',
    'admin/system/config/global'=>'전역',
    'admin/system/config/encrypt'=>'암호화된 주소',
    'admin/system/config/encrypt_not'=>'암호화되지 않음',
    'admin/system/config/search_hot'=>'인기 검색어',
    'admin/system/config/art_extend_class'=>'기사 확장 카테고리',
    'admin/system/config/vod_extend_class'=>'동영상 확장 카테고리',
    'admin/system/config/vod_extend_state'=>'동영상 리소스',
    'admin/system/config/vod_extend_version'=>'동영상 버전',
    'admin/system/config/vod_extend_weekday'=>'동영상 주기',
    'admin/system/config/vod_extend_area'=>'동영상 지역',
    'admin/system/config/vod_extend_lang'=>'동영상 언어',
    'admin/system/config/vod_extend_year'=>'동영상 연도',
    'admin/system/config/actor_extend_area'=>'배우 지역',
    'admin/system/config/filter_words'=>'단어 필터링',
    'admin/system/config/filter_words_tip'=>'검색 매개변수, 댓글, 메시지에 사용할 수 없는 단어에 적용됩니다. 쉼표(,)로 구분',
    'admin/system/config/extra_var'=>'사용자 지정 매개변수',
    'admin/system/config/extra_var_tip'=>'줄당 하나의 변수, 예: aa$$$저는 왕입니다. 템플릿 호출 방법 $GLOBALS[\'config\'][\'extra\'][\'aa\']',
    'admin/system/config/test_err'=>'오류가 발생했습니다. 확장 라이브러리와 구성 항목이 활성화되어 있는지 확인하십시오!',

    'admin/system/configapi/title'=>'수집 인터페이스 API 구성',
    'admin/system/configapi/vod'=>'동영상 API 설정',
    'admin/system/configapi/art'=>'기사 API 설정',
    'admin/system/configapi/actor'=>'배우 API 설정',
    'admin/system/configapi/role'=>'역할 API 설정',
    'admin/system/configapi/website'=>'웹사이트 API 설정',
    'admin/system/configapi/vod_tip'=>'팁 정보: <br>
                            1. 동영상 목록 주소 /api.php/provide/vod/?ac=list <br>
                            2. 동영상 세부 정보 주소 /api.php/provide/vod/?ac=detail',
    'admin/system/configapi/status'=>'인터페이스 스위치',
    'admin/system/configapi/charge'=>'유료 여부',
    'admin/system/configapi/detail_inc_hits'=>'클릭 수 증가',
    'admin/system/configapi/detail_inc_hits_tip'=>'ac=detail이고 ID가 하나뿐일 때 클릭 수 +1',
    'admin/system/configapi/pagesize'=>'목록의 페이지당 표시 수',
    'admin/system/configapi/pagesize_tip'=>'데이터의 페이지당 표시 수, 50개를 초과하지 않는 것이 좋습니다.',
    'admin/system/configapi/imgurl'=>'이미지 도메인',
    'admin/system/configapi/imgurl_tip'=>'전체 이미지 액세스 경로를 표시하는 데 필요하며 http(s):로 시작하고 /로 끝나며 업로드 디렉토리를 포함하지 않습니다.',
    'admin/system/configapi/typefilter'=>'카테고리 필터링 매개변수',
    'admin/system/configapi/typefilter_tip'=>'표시할 카테고리 ID 나열, 예: 11,12,13',
    'admin/system/configapi/datafilter'=>'데이터 필터링 매개변수',
    'admin/system/configapi/datafilter_tip'=>'SQL 쿼리 조건, 예: vod_status=1',
    'admin/system/configapi/datafilter_tip_art'=>'SQL 쿼리 조건, 예: art_status=1',
    'admin/system/configapi/datafilter_tip_actor'=>'SQL 쿼리 조건, 예: actor_status=1',
    'admin/system/configapi/datafilter_tip_role'=>'SQL 쿼리 조건, 예: role_status=1',
    'admin/system/configapi/datafilter_tip_website'=>'SQL 쿼리 조건, 예: website_status=1',
    'admin/system/configapi/cachetime'=>'데이터 캐시 시간',
    'admin/system/configapi/cachetime_tip'=>'데이터 캐시 시간',
    'admin/system/configapi/from'=>'재생 그룹 지정',
    'admin/system/configapi/from_tip'=>'쉼표(,)로 구분, 예: youku,iqiyi,qvod',
    'admin/system/configapi/auth'=>'권한 부여 도메인',
    'admin/system/configapi/art_tip'=>'팁 정보: <br>
                            1. 기사 목록 주소 /api.php/provide/art/?ac=list <br>
                            2. 기사 세부 정보 주소 /api.php/provide/art/?ac=detail',

    'admin/system/configapi/actor_tip'=>'팁 정보: <br>
                            1. 배우 목록 주소 /api.php/provide/actor/?ac=list <br>
                            2. 배우 세부 정보 주소 /api.php/provide/actor/?ac=detail',
    'admin/system/configapi/role_tip'=>'팁 정보: <br>
                            1. 역할 목록 주소 /api.php/provide/role/?ac=list <br>
                            2. 역할 세부 정보 주소 /api.php/provide/role/?ac=detail',
    'admin/system/configapi/website_tip'=>'팁 정보: <br>
                            1. 웹사이트 목록 주소 /api.php/provide/website/?ac=list <br>
                            2. 웹사이트 세부 정보 주소 /api.php/provide/website/?ac=detail',




    'admin/system/configcollect/title'=>'수집 매개변수 구성',
    'admin/system/configcollect/vod'=>'동영상 수집 설정',
    'admin/system/configcollect/art'=>'기사 수집 설정',
    'admin/system/configcollect/actor'=>'배우 수집 설정',
    'admin/system/configcollect/role'=>'역할 수집 설정',
    'admin/system/configcollect/website'=>'웹사이트 수집 설정',
    'admin/system/configcollect/comment'=>'댓글 수집 설정',
    'admin/system/configcollect/status'=>'데이터 상태',
    'admin/system/configcollect/words'=>'수집 어휘 설정',
    'admin/system/configcollect/hits_rnd'=>'무작위 클릭 수',
    'admin/system/configcollect/updown_rnd'=>'무작위 좋아요/싫어요',
    'admin/system/configcollect/score_rnd'=>'무작위 평점',
    'admin/system/configcollect/sync_pic'=>'자동 이미지 동기화',
    'admin/system/configcollect/auto_tag'=>'자동 태그 생성',
    'admin/system/configcollect/class_filter'=>'확장 카테고리 최적화',
    'admin/system/configcollect/class_filter_tip'=>'확장 카테고리 이름에서 [편, 드라마]를 자동으로 필터링합니다. 예를 들어 액션 영화는 액션으로, 미국 드라마는 미국으로 바뀝니다.',
    'admin/system/configcollect/psename'=>'이름 동의어 바꾸기',
    'admin/system/configcollect/psename_tip'=>'이름 동의어를 자동으로 변환하여 중복률을 줄입니다. 예: 시즌 1=시즌1;',
    'admin/system/configcollect/psernd'=>'자세히 보기에 무작위 문장 삽입',
    'admin/system/configcollect/psesyn'=>'자세히 보기 동의어 바꾸기',
    'admin/system/configcollect/pseplayer'=>'플레이어 동의어 바꾸기',
    'admin/system/configcollect/psearea'=>'지역 동의어 바꾸기',
    'admin/system/configcollect/pselang'=>'언어 동의어 바꾸기',
    'admin/system/configcollect/inrule'=>'데이터베이스 중복 규칙',
    'admin/system/configcollect/inrule_tip_role'=>'Douban ID와 동영상 이름이 전달되면 Douban ID가 우선합니다.',
    'admin/system/configcollect/inrule_tip_comment'=>'관련 데이터 이름 또는 Douban ID가 전달되면 Douban ID가 우선합니다(Douban ID는 동영상 모듈에서만 작동합니다).',
    'admin/system/configcollect/uprule'=>'2차 업데이트 규칙',
    'admin/system/configcollect/filter'=>'데이터 필터링',
    'admin/system/configcollect/urlrole'=>'주소 2차 업데이트 규칙',
    'admin/system/configcollect/urlrole/use_more'=>'에피소드 수가 많은 것이 우선',
    // 'admin/system/configcollect/urlrole_tip'=>'2차 업데이트 주소에서 동일한 유형의 플레이어를 만났을 때. 바꾸기: 새로 제출된 주소만 유지합니다. 병합: 기존 주소와 새 주소를 통합하여 중복을 제거합니다. 에피소드 수가 많은 것이 우선: 두 리소스에서 연재 에피소드 수가 많은 것이 우선적으로 바뀝니다.',
    'admin/system/configcollect/urlrole_tip'=>'2차 업데이트 주소에서 동일한 유형의 플레이어를 만났을 때. 바꾸기: 새로 제출된 주소만 유지합니다. 병합: 기존 주소와 새 주소를 통합하여 중복을 제거합니다.',
    'admin/system/configcollect/content'=>'자세히 보기',
    'admin/system/configcollect/playurl'=>'재생 주소',
    'admin/system/configcollect/downurl'=>'다운로드 주소',
    'admin/system/configcollect/words_tip'=>'동의어 사전: 줄당 하나씩, 빈 줄 없이, 형식: 바꾸기 전=바꾸기 후, # 기호는 허용되지 않습니다. <br>
                    무작위 단어 사전: 문자 필드는 일반적으로 약 20개 항목이면 충분하며 농담이나 짧은 이야기가 될 수 있으며 하이퍼링크를 추가할 수도 있습니다. <br>
                    이 기능은 수집 성능에 영향을 미치므로 한 번에 너무 많은 항목을 추가하지 마십시오. 적절한 의사 원본은 검색 엔진 수집에 도움이 됩니다. <br>
                    단어 사전 기능을 사용하지 않으려면 [수집 매개변수 설정에서 비활성화]하십시오.',
    'admin/system/configcollect/vod_namewords'=>'동영상 이름 동의어 사전',
    'admin/system/configcollect/vod_thesaurus'=>'동영상 자세히 보기 동의어 사전',
    'admin/system/configcollect/vod_playerwords'=>'플레이어 동의어 사전',
    'admin/system/configcollect/vod_areawords'=>'지역 동의어 사전',
    'admin/system/configcollect/vod_langwords'=>'언어 동의어 사전',
    'admin/system/configcollect/vod_words'=>'동영상 자세히 보기 무작위 단어 사전',
    'admin/system/configcollect/art_thesaurus'=>'기사 자세히 보기 동의어 사전',
    'admin/system/configcollect/art_words'=>'기사 자세히 보기 무작위 단어 사전',
    'admin/system/configcollect/actor_thesaurus'=>'배우 자세히 보기 동의어 사전',
    'admin/system/configcollect/actor_words'=>'배우 자세히 보기 무작위 단어 사전',
    'admin/system/configcollect/role_thesaurus'=>'역할 자세히 보기 동의어 사전',
    'admin/system/configcollect/role_words'=>'역할 자세히 보기 무작위 단어 사전',
    'admin/system/configcollect/website_thesaurus'=>'웹사이트 자세히 보기 동의어 사전',
    'admin/system/configcollect/website_words'=>'웹사이트 자세히 보기 무작위 단어 사전',
    'admin/system/configcollect/comment_thesaurus'=>'댓글 자세히 보기 동의어 사전',
    'admin/system/configcollect/comment_words'=>'댓글 자세히 보기 무작위 단어 사전',

    'admin/system/configcomment/title'=>'댓글 메시지 구성',
    'admin/system/configcomment/gbook'=>'방명록',
    'admin/system/configcomment/gbook_tip'=>'방명록 활성화 여부',
    'admin/system/configcomment/audit'=>'심사 여부',
    'admin/system/configcomment/login'=>'로그인 후 메시지 작성',
    'admin/system/configcomment/verify'=>'인증 코드',
    'admin/system/configcomment/pagesize'=>'페이지당 항목 수',
    'admin/system/configcomment/pagesize_tip'=>'20개 이상으로 설정하는 것이 좋습니다.',
    'admin/system/configcomment/timespan'=>'시간 간격',
    'admin/system/configcomment/timespan_tip'=>'단위는 초이며 3초 이상으로 설정하는 것이 좋습니다.',
    'admin/system/configcomment/comment'=>'댓글 상태',
    'admin/system/configcomment/comment_tip'=>'댓글 활성화 여부',




    'admin/system/configconnect/title'=>'통합 로그인 구성',
    'admin/system/configconnect/tip'=>'팁 정보: <br>
                        1. QQ 로그인 주소 /index.php/user/oauth/?type=qq <br>
                        2. WeChat 로그인 주소 /index.php/user/oauth/?type=weixin <br>
                        3. 콜백 주소 /index.php/user/logincallback/?type=qq 또는 /index.php/user/logincallback/?type=weixin',
    'admin/system/configconnect/qq'=>'QQ 로그인',
    'admin/system/configconnect/go_reg'=>'등록하려면 클릭',
    'admin/system/configconnect/wx'=>'WeChat 로그인',



    'admin/system/configemail/title'=>'이메일 전송 구성',
    'admin/system/configemail/tip'=>'팁 정보: <br>
                        수정 후 저장을 클릭한 다음 테스트 전송하십시오. 내용은 {$maccms.***} 태그, {$user.***} 태그, {$code} 인증 코드, {$time} 유효 시간을 지원합니다.',
    'admin/system/configemail/type'=>'전송 방식',
    'admin/system/configemail/time'=>'유효 기간',
    'admin/system/configemail/time_tip'=>'이메일 인증 코드가 몇 분 후에 만료됩니까?',
    'admin/system/configemail/nick'=>'보낸 사람 닉네임',
    'admin/system/configemail/test'=>'테스트 주소',
    'admin/system/configemail/btn_test'=>'테스트 이메일 보내기',
    'admin/system/configemail/test_title'=>'테스트 제목',
    'admin/system/configemail/test_body'=>'테스트 본문',
    'admin/system/configemail/user_reg_title'=>'사용자 가입 제목',
    'admin/system/configemail/user_reg_body'=>'사용자 가입 본문',
    'admin/system/configemail/user_bind_title'=>'사용자 바인딩 제목',
    'admin/system/configemail/user_bind_body'=>'사용자 바인딩 본문',
    'admin/system/configemail/user_findpass_title'=>'사용자 비밀번호 찾기 제목',
    'admin/system/configemail/user_findpass_body'=>'사용자 비밀번호 찾기 본문',
    'admin/system/configemail/test_err'=>'오류가 발생했습니다. 해당 확장 라이브러리가 활성화되어 있는지 확인하십시오.',

    'admin/system/configinterface/pass_check'=>'저장 실패, 보안을 위해 데이터베이스 비밀번호는 16자 이상이어야 합니다!',
    'admin/system/configinterface/title'=>'외부 데이터베이스 구성',
    'admin/system/configinterface/tip'=>'팁 정보: <br>
                        1. 카테고리 변환은 각각 한 줄을 차지합니다. <br>
                        2. 로컬 카테고리가 앞에 오고 수집 카테고리가 뒤에 옵니다(액션 영화=액션). <br>
                        3. 불필요한 빈 줄이 없어야 합니다. <br>
                        4. 동영상 플레이어, 메모, 주소, 서버 그룹, 기사 페이지 매김 등 다중 페이지 데이터 연결 기호는 모두 $$$입니다. <br>
                        5. 데이터베이스 인터페이스 주소 동영상 /api.php/receive/vod; 기사 /api.php/receive/art; 배우 /api.php/receive/actor; 역할 /api.php/receive/role; 웹사이트 /api.php/receive/website; <br>',
    'admin/system/configinterface/status'=>'인터페이스 스위치',
    'admin/system/configinterface/pass'=>'데이터베이스 로그인 없이 비밀번호 입력',
    'admin/system/configinterface/pass_tip'=>'무차별 대입 공격을 방지하기 위해 비밀번호를 대소문자, 숫자 및 특수 문자를 포함하여 16자 이상으로 설정하거나 api.php 엔트리 파일 이름을 수정하는 것이 좋습니다.',
    'admin/system/configinterface/vod_type'=>'동영상 카테고리 변환',
    'admin/system/configinterface/art_type'=>'기사 카테고리 변환',
    'admin/system/configinterface/actor_type'=>'배우 카테고리 변환',
    'admin/system/configinterface/website_type'=>'웹사이트 카테고리 변환',
    'admin/system/configinterface/title'=>'외부 데이터베이스 구성',
    'admin/system/configinterface/title'=>'외부 데이터베이스 구성',
    'admin/system/configinterface/title'=>'외부 데이터베이스 구성',



    'admin/system/configpay/title'=>'온라인 결제 구성',
    'admin/system/configpay/card'=>'카드 비밀번호',
    'admin/system/configpay/config'=>'결제 구성',
    'admin/system/configpay/notify'=>'콜백 알림 주소',
    'admin/system/configpay/notify_tip'=>'결제 인터페이스 알림 콜백 주소',
    'admin/system/configpay/min'=>'최소 충전 금액',
    'admin/system/configpay/min_tip'=>'단위는 RMB 위안이며 최소 1위안입니다.',
    'admin/system/configpay/scale'=>'환전 비율',
    'admin/system/configpay/scale_tip'=>'1위안은 몇 포인트입니까?',
    'admin/system/configpay/card_config'=>'카드 비밀번호 구성',
    'admin/system/configpay/card_url'=>'판매 웹사이트',
    'admin/system/configpay/card_url_tip'=>'타사 카드 비밀번호 플랫폼',

    'admin/system/configplay/title'=>'플레이어 매개변수 구성',
    'admin/system/configplay/tip'=>'팁 정보: <br>
                        1. 플레이어 크기는 px 픽셀과 % 백분율의 두 가지 단위를 지원합니다. <br>
                        2. 설정된 값에 단위가 포함되어 있지 않으면 100%로 처리됩니다.',

    'admin/system/configplay/width'=>'플레이어 너비',
    'admin/system/configplay/width_tip'=>'예: 540px 또는 100%',
    'admin/system/configplay/height'=>'플레이어 높이',
    'admin/system/configplay/height_tip'=>'예: 460px 또는 100%',
    'admin/system/configplay/widthmob'=>'모바일 재생 너비',
    'admin/system/configplay/heightmob'=>'모바일 재생 높이',
    'admin/system/configplay/widthpop'=>'팝업 창 너비',
    'admin/system/configplay/heightpop'=>'팝업 창 높이',
    'admin/system/configplay/second'=>'미리 로드 시간',
    'admin/system/configplay/second_tip'=>'예: 5, 단위는 초입니다.',
    'admin/system/configplay/prestrain'=>'미리 로드 팁',
    'admin/system/configplay/prestrain_tip'=>'큰따옴표, 작은따옴표 등 특수 문자를 사용하지 마십시오.',
    'admin/system/configplay/buffer'=>'버퍼링 팁',
    'admin/system/configplay/buffer_tip'=>'큰따옴표, 작은따옴표 등 특수 문자를 사용하지 마십시오.',
    'admin/system/configplay/parse'=>'인터페이스 주소',
    'admin/system/configplay/parse_tip'=>'타사 처리 인터페이스',
    'admin/system/configplay/autofull'=>'자동 전체 화면',
    'admin/system/configplay/showtop'=>'헤더 스위치',
    'admin/system/configplay/showlist'=>'목록 스위치',
    'admin/system/configplay/flag'=>'플레이어 파일',
    'admin/system/configplay/flag_tip'=>'로컬 플레이어',
    'admin/system/configplay/colors'=>'플레이어 색상',
    'admin/system/configplay/select_colors'=>'색상 선택',
    'admin/system/configplay/select_colors_tip'=>'색상은 16진수 표기법을 사용하며 # 기호 없이 쉼표로 구분되며 총 15가지 색상을 구성할 수 있습니다!
                        <br>순서대로: 배경색, 텍스트 색상, 링크 색상, 그룹 제목 배경색, 그룹 제목 색상, 현재 그룹 제목 색상, 현재 에피소드 색상, 에피소드 목록 스크롤바 돌출 부분의 색상, 스크롤바 위아래 버튼의 위쪽 삼각형 화살표 색상, 스크롤바의 배경색, 스크롤바의 빈 부분의 색상, 스크롤바의 입체 스크롤바 그림자 색상, 스크롤바의 밝은 테두리 색상, 스크롤바의 강한 그림자 색상, 스크롤바의 기본 색상',


    'admin/system/configweixin/title'=>'WeChat 연결 구성',
    'admin/system/configweixin/tip'=>'공식 계정 연결 도메인, 공식 계정 검색 도메인, 웹사이트 도메인은 일반적으로 동일합니다. 공식 계정에서 발생할 수 있는 도메인 차단 문제로 인해 연결을 위해 도메인을 별도로 설정할 수 있습니다. <br>
                    인터페이스 주소 /api.php/wechat',
    'admin/system/configweixin/duijie'=>'연결 도메인',
    'admin/system/configweixin/duijie_tip'=>'http 또는 https로 시작하십시오. 공식 계정 백엔드의 연결 주소는 [http://wx.test.com/inc/weixin.php]입니다.',
    'admin/system/configweixin/sousuo'=>'검색 도메인',
    'admin/system/configweixin/sousuo_tip'=>'공식 계정에 표시되는 데 사용되는 도메인이며 일반적으로 이 도메인을 변경하면 됩니다. http 또는 https로 시작하십시오.',
    'admin/system/configweixin/token'=>'연결 토큰',
    'admin/system/configweixin/token_tip'=>'공식 계정 백엔드에 연결된 토큰 키',
    'admin/system/configweixin/guanzhu'=>'팔로우 응답',
    'admin/system/configweixin/guanzhu_tip'=>'사용자가 공식 계정을 팔로우할 때 자동으로 응답하는 문장',
    'admin/system/configweixin/wuziyuan'=>'리소스 없음 응답',
    'admin/system/configweixin/wuziyuan_tip'=>'사용자가 리소스를 찾을 수 없을 때 기본적으로 반환되는 정보',
    'admin/system/configweixin/wuziyuanlink'=>'리소스 없음 응답 링크',
    'admin/system/configweixin/wuziyuanlink_tip'=>'리소스 없음 응답 링크 또는 콘텐츠',
    'admin/system/configweixin/pagelink'=>'반환 페이지 주소',
    'admin/system/configweixin/pagelink_detail'=>'콘텐츠 페이지',
    'admin/system/configweixin/pagelink_play'=>'재생 페이지',
    'admin/system/configweixin/pagelink_search'=>'검색 페이지',
    'admin/system/configweixin/msgtype'=>'반환 콘텐츠 유형',
    'admin/system/configweixin/msgtype_pic'=>'이미지 및 텍스트',
    'admin/system/configweixin/msgtype_font'=>'텍스트',
    'admin/system/configweixin/msgtype_tip'=>'WeChat의 새로운 규정에 따라 이미지 및 텍스트는 1개 항목만 반환할 수 있습니다.',
    'admin/system/configweixin/gjc'=>'사용자 지정 키워드',
    'admin/system/configweixin/keyword'=>'키워드',
    'admin/system/configweixin/return_text'=>'반환 텍스트',
    'admin/system/configweixin/return_pic'=>'반환 이미지',
    'admin/system/configweixin/return_link'=>'반환 링크',

    'admin/system/configuser/title'=>'회원 매개변수 구성',
    'admin/system/configuser/tip'=>'팁 정보: <br>
                        1. 미리보기 기능을 활성화하면 재생 창에서 iframe 동적 페이지 방식으로 로드되어 성능에 영향을 미칠 수 있습니다. <br>',
    'admin/system/configuser/model'=>'회원 모듈',
    'admin/system/configuser/reg_open'=>'가입 스위치',
    'admin/system/configuser/reg_status'=>'기본 가입 상태',
    'admin/system/configuser/phone_reg_verify'=>'휴대폰 가입 인증',
    'admin/system/configuser/email_reg_verify'=>'이메일 가입 인증',
    'admin/system/configuser/email_white_hosts'=>'이메일 화이트리스트',
    'admin/system/configuser/email_white_hosts_tip'=>"작성 후 화이트리스트에 있는 이메일 호스트 이름만 가입할 수 있습니다. 쉼표(,) 또는 줄 바꿈으로 구분합니다. 예: qq.com,360.com\\n참고: 블랙리스트와 화이트리스트를 모두 작성하면 정책이 동시에 적용됩니다.",
    'admin/system/configuser/email_black_hosts'=>'이메일 블랙리스트',
    'admin/system/configuser/email_black_hosts_tip'=>"작성 후 블랙리스트에 있는 이메일 호스트 이름은 가입할 수 없습니다. 쉼표(,) 또는 줄 바꿈으로 구분합니다. 예: protonmail.com,gmail.com\\n참고: 블랙리스트와 화이트리스트를 모두 작성하면 정책이 동시에 적용됩니다.",
    'admin/system/configuser/reg_verify'=>'가입 인증 코드',
    'admin/system/configuser/login_verify'=>'로그인 인증 코드',
    'admin/system/configuser/reg_points'=>'가입 보너스 포인트',
    'admin/system/configuser/reg_points_tip'=>'사용자가 가입에 성공하면 기본적으로 포인트가 제공됩니다.',
    'admin/system/configuser/reg_num'=>'IP당 제한',
    'admin/system/configuser/reg_num_tip'=>'IP당 매일 가입 횟수 제한',
    'admin/system/configuser/invite_reg_points'=>'초대 가입 포인트',
    'admin/system/configuser/invite_reg_points_tip'=>'사용자를 성공적으로 초대하여 보상 포인트를 얻습니다.',
    'admin/system/configuser/invite_visit_points'=>'프로모션 방문 포인트',
    'admin/system/configuser/invite_visit_points_tip'=>'성공적인 초대 방문으로 보상 포인트를 얻습니다.',
    'admin/system/configuser/invite_visit_num_tip'=>'IP당 매일 몇 번의 프로모션 방문 포인트를 받을 수 있는지 제한',
    'admin/system/configuser/reward_status'=>'3단계 배포 상태',
    'admin/system/configuser/reward_ratio'=>'1단계 수수료 비율',
    'admin/system/configuser/reward_ratio2'=>'2단계 수수료 비율',
    'admin/system/configuser/reward_ratio3'=>'3단계 수수료 비율',
    'admin/system/configuser/reward_unit'=>'단위 백분율',
    'admin/system/configuser/reward_tip'=>'사용자가 결제에 성공하면 배포 프로모터는 일정 비율의 포인트를 받을 수 있으며 수수료가 1포인트 미만이면 무시됩니다.',
    'admin/system/configuser/cash_status'=>'출금 상태',
    'admin/system/configuser/cash_ratio'=>'환전 비율',
    'admin/system/configuser/cash_ratio_tip'=>'환전 비율 1위안=몇 포인트',
    'admin/system/configuser/cash_min'=>'최소 출금 금액',
    'admin/system/configuser/cash_min_tip'=>'최소 출금 금액',
    'admin/system/configuser/trysee'=>'미리보기 시간',
    'admin/system/configuser/trysee_tip'=>'전역 설정 권한이 없는 경우 포인트 온디맨드 미리보기 시간이 필요하며 단위는 분입니다. 0은 전역 미리보기를 닫는 것을 의미합니다.',
    'admin/system/configuser/vod_points_type'=>'동영상 유료 방식',
    'admin/system/configuser/vod_points_0'=>'에피소드당',
    'admin/system/configuser/vod_points_1'=>'데이터당',
    'admin/system/configuser/art_points_type'=>'기사 유료 방식',
    'admin/system/configuser/art_points_0'=>'페이지당',
    'admin/system/configuser/art_points_1'=>'데이터당',
    'admin/system/configuser/portrait_status'=>'프로필 사진 업로드',
    'admin/system/configuser/portrait_size'=>'프로필 사진 크기',
    'admin/system/configuser/portrait_size_tip'=>'크기는 100x100으로 하는 것이 좋습니다.',
    'admin/system/configuser/filter_words'=>'사용자 이름 필터링',
    'admin/system/configuser/filter_words_tip'=>'쉼표(,)로 구분',

    'admin/system/configurl/title'=>'URL 매개변수 구성',
    'admin/system/configurl/view'=>'찾아보기 모드 설정',
    'admin/system/configurl/html'=>'정적 경로 설정',
    'admin/system/configurl/route'=>'라우팅 URL 재작성 설정',
    'admin/system/configurl/dynamic'=>'동적 모드',
    'admin/system/configurl/static'=>'정적 모드',
    'admin/system/configurl/static_one'=>'정적 에피소드당 한 페이지',
    'admin/system/configurl/index'=>'홈페이지',
    'admin/system/configurl/map'=>'지도',
    'admin/system/configurl/search'=>'검색',
    'admin/system/configurl/label'=>'사용자 지정 페이지',
    'admin/system/configurl/vod_type'=>'동영상 카테고리',
    'admin/system/configurl/vod_show'=>'동영상 카테고리 필터링',
    'admin/system/configurl/art_type'=>'기사 카테고리',
    'admin/system/configurl/art_show'=>'기사 카테고리 필터링',
    'admin/system/configurl/topic_index'=>'특집 홈페이지',
    'admin/system/configurl/topic_detail'=>'특집 자세히 보기',
    'admin/system/configurl/vod_detail'=>'동영상 자세히 보기',
    'admin/system/configurl/vod_play'=>'동영상 재생',
    'admin/system/configurl/vod_down'=>'동영상 다운로드',
    'admin/system/configurl/art_detail'=>'기사 자세히 보기',
    'admin/system/configurl/variable'=>'변수',
    'admin/system/configurl/structure'=>'일반적인 구조',
    'admin/system/configurl/multipage_connector'=>'다중 페이지 연결 기호',
    'admin/system/configurl/multipage_connector_tip'=>'예: 구분 기호가 -이면 두 번째 페이지는 type/index-2.html입니다.',
    'admin/system/configurl/common_connector'=>'다중 페이지 연결 기호',
    'admin/system/configurl/file_ext'=>'파일 접미사',
    'admin/system/configurl/file_ext_tip'=>'예: html로 하는 것이 좋습니다.',
    'admin/system/configurl/common_ext'=>'파일 접미사',
    'admin/system/configurl/route_tip'=>'팁 정보: <br>
                        1. 동적 모드에서 라우팅 상태를 활성화하면 URL이 자동으로 다시 작성됩니다. <br>
                        2. 라우팅 규칙은 줄당 하나씩이며 =>로 구분되며 왼쪽은 라우팅 표현식이고 오른쪽은 라우팅 주소입니다. <br>
                        3. 라우팅 주소는 시스템에서 제공하며 원칙적으로 변경되지 않으며 라우팅 표현식만 조정하면 됩니다. <br>
                        4. 불필요한 빈 줄이 없어야 합니다. <br>
                        5. ID 유형은 필요에 따라 선택할 수 있으며 병음을 선택할 때 데이터에 중복된 병음이 없어야 합니다. 그렇지 않으면 데이터를 가져올 때 문제가 발생합니다. <br>
                        6. URL 구분 기호는 / 및 -를 지원합니다. 다른 기호를 사용하지 않는 것이 좋습니다.',

    'admin/system/configurl/suffix_hide'=>'접미사 숨기기',
    'admin/system/configurl/route_status'=>'라우팅 상태',
    'admin/system/configurl/rewrite_status'=>'URL 재작성 상태',
    'admin/system/configurl/route_rule'=>'라우팅 규칙',
    'admin/system/configurl/encode_key'=>'암호화 키',
    'admin/system/configurl/encode_len'=>'암호화 길이',
    'admin/system/configurl/encode_tip'=>'키를 변경하면 URL도 변경됩니다. 길이는 암호화된 길이가 원래 숫자 길이보다 작지 않음을 나타냅니다.',

    'admin/system/configupload/title'=>'첨부 파일 매개변수 구성',
    'admin/system/configupload/tip'=>'팁: 로컬 업로드 또는 타사 저장소를 사용하는 경우 먼저 로컬에 업로드한 다음 타사 저장소로 전송해야 합니다.<br>
                        따라서 로컬 운영 체제의 임시 파일 디렉토리에는 쓰기 권한이 있어야 합니다. 그렇지 않으면 파일 업로드가 실패합니다.<br>
                        PHP 임시 파일 디렉토리 수정 방법은 PHP 구성 파일에서 sys_temp_dir을 검색하십시오.<br>
                        현재 운영 체제 임시 파일 디렉토리:',
    'admin/system/configupload/write_ok'=>'임시 파일에 쓰기 테스트 성공, 업로드 상태 정상',
    'admin/system/configupload/write_err'=>'임시 파일에 쓰기 테스트 실패, 임시 파일 디렉토리 권한을 확인하십시오.',
    'admin/system/configupload/thumb_tip'=>'이미지를 업로드할 때 썸네일을 자동으로 생성할지 여부',
    'admin/system/configupload/thumb_size'=>'크기',
    'admin/system/configupload/thumb_size_tip'=>'썸네일 크기, 예: 길이x너비, 예: 300x300',
    'admin/system/configupload/thumb_type'=>'자르기 방식',
    'admin/system/configupload/thumb_type1'=>'비율에 맞게 축소',
    'admin/system/configupload/thumb_type2'=>'축소 후 채우기',
    'admin/system/configupload/thumb_type3'=>'가운데 자르기',
    'admin/system/configupload/thumb_type4'=>'왼쪽 상단 자르기',
    'admin/system/configupload/thumb_type5'=>'오른쪽 하단 자르기',
    'admin/system/configupload/thumb_type6'=>'고정 크기 축소',
    'admin/system/configupload/watermark'=>'텍스트 워터마크',
    'admin/system/configupload/watermark_location'=>'워터마크 위치',
    'admin/system/configupload/watermark_location1'=>'왼쪽 상단',
    'admin/system/configupload/watermark_location2'=>'위쪽 가운데',
    'admin/system/configupload/watermark_location3'=>'오른쪽 상단',
    'admin/system/configupload/watermark_location4'=>'왼쪽 가운데',
    'admin/system/configupload/watermark_location5'=>'가운데',
    'admin/system/configupload/watermark_location6'=>'오른쪽 가운데',
    'admin/system/configupload/watermark_location7'=>'왼쪽 하단',
    'admin/system/configupload/watermark_location8'=>'아래쪽 가운데',
    'admin/system/configupload/watermark_location9'=>'오른쪽 하단',
    'admin/system/configupload/watermark_content'=>'워터마크 내용',
    'admin/system/configupload/watermark_size'=>'글꼴 크기',
    'admin/system/configupload/watermark_size_tip'=>'단위: px(픽셀)',
    'admin/system/configupload/watermark_color'=>'워터마크 색상',
    'admin/system/configupload/watermark_color_tip'=>'형식: #000000',
    'admin/system/configupload/protocol'=>'타사 액세스 프로토콜',
    'admin/system/configupload/protocol_tip'=>'타사 저장소를 사용하면 mac://로 시작하도록 변환됩니다. 즉, 템플릿에 표시되는 이미지 링크에서 mac을 http 또는 https로 바꿉니다.',
    'admin/system/configupload/mode'=>'저장 방식',
    'admin/system/configupload/mode_local'=>'로컬 저장',
    'admin/system/configupload/mode_remote'=>'원격 액세스',
    'admin/system/configupload/remoteurl'=>'이미지 원격 URL',
    'admin/system/configupload/remoteurl_tip'=>'로컬 이미지가 원격에 있는 경우 이 기능을 사용할 수 있습니다.',
    'admin/system/configupload/img_key'=>'핫링크 방지 식별자',
    'admin/system/configupload/img_key_tip'=>'핫링크 방지가 필요한 도메인 또는 키워드가 여러 개인 경우 |로 연결하십시오.',
    'admin/system/configupload/img_api'=>'핫링크 방지 인터페이스',
    'admin/system/configupload/img_api_tip'=>'핫링크 방지 이미지를 처리하는 인터페이스 주소',
    'admin/system/configupload/keep_local'=>'로컬 유지',
    'admin/system/configupload/keep_local_tip'=>'원격으로 업로드하도록 선택하면 업로드가 완료된 후 로컬에도 복사본이 유지됩니다.',

    'admin/system/configsms/title'=>'SMS 전송 구성',
    'admin/system/configsms/tip'=>'팁 정보: <br>
                        SMS 인터페이스 서비스 제공업체의 요구 사항에 따라 SMS 서명 및 SMS 내용을 설정하십시오.<br>',
    'admin/system/configsms/type'=>'서비스 제공업체',
    'admin/system/configsms/sign'=>'SMS 서명',
    'admin/system/configsms/tpl_code_reg'=>'가입 템플릿 번호',
    'admin/system/configsms/tpl_code_tip'=>'템플릿 번호는 서비스 제공업체의 SMS 제어판에서 신청해야 합니다.',
    'admin/system/configsms/tpl_code_bind'=>'바인딩 템플릿 번호',
    'admin/system/configsms/tpl_code_findpass'=>'비밀번호 찾기 템플릿 번호',
    'admin/system/configsms/test_err'=>'오류가 발생했습니다. 해당 확장 라이브러리가 활성화되어 있는지 확인하십시오!',



    'admin/system/configseo/vod_index'=>'동영상 홈페이지',
    'admin/system/configseo/art_index'=>'기사 홈페이지',
    'admin/system/configseo/actor_index'=>'배우 홈페이지',
    'admin/system/configseo/role_index'=>'역할 홈페이지',
    'admin/system/configseo/plot_index'=>'줄거리 홈페이지',
    'admin/system/configseo/website_index'=>'웹사이트 홈페이지',
    'admin/system/configseo/tit'=>'제목',
    'admin/system/configseo/key'=>'키워드',
    'admin/system/configseo/des'=>'설명',
    'admin/system/configseo/tip_des'=>'팁 정보',










    'admin/actor/title'=>'배우 관리',
    'admin/addon/title'=>'플러그인 관리',
    'admin/addon/get_dir_err'=>'플러그인 디렉토리를 가져오지 못했습니다.',
    'admin/addon/get_addon_info_err'=>'플러그인 정보를 가져오지 못했습니다.',
    'admin/addon/lack_config_err'=>'플러그인 구성 파일 info.ini가 없습니다.',
    'admin/addon/name_empty_err'=>'플러그인 이름은 비워 둘 수 없습니다.',
    'admin/addon/haved_err'=>'플러그인이 이미 있습니다.',
    'admin/addon/path_err'=>'잘못된 디렉토리 요청',
    'admin/addon/add_tip'=>'팁: <br>
                1. 타사 플러그인이 프로그램 개발 사양을 준수하는지 확인하십시오.
                2. 사용하기 전에 보안 검사를 수행하여 보안 문제가 발생하지 않도록 하십시오.',


    'admin/admin/title'=>'관리자 관리',
    'admin/admin/del_cur_err'=>'현재 로그인 계정을 삭제할 수 없습니다.',
    'admin/admin/popedom_tip'=>'팁: <br>
                    1. 권한 제어는 각 작업에 대해 정확하며 설립자 ID가 1인 관리자는 모든 권한을 갖습니다.
                    2. --로 시작하는 것은 페이지 내 버튼 작업 옵션입니다.',


    'admin/art/title'=>'기사 관리',
    'admin/card/title'=>'충전 카드 관리',
    'admin/card/make_num'=>'생성 수량',
    'admin/card/please_input_make_num'=>'생성 수량을 입력하십시오.',
    'admin/card/please_input_money'=>'충전 카드 액면가를 입력하십시오.',
    'admin/card/please_input_points'=>'충전 카드 포인트를 입력하십시오.',


    'admin/card/import_tip'=>'카드 번호, 비밀번호, 생성 시간',
    'admin/cash/title'=>'출금 관리',
    'admin/vodserver/title'=>'서버 그룹 관리',
    'admin/vodserver/url'=>'서버 그룹 주소',
    'admin/vodplayer/title'=>'플레이어 관리',
    'admin/voddowner/title'=>'다운로더 관리',
    'admin/vodplayer/alone_api_url'=>'독립 인터페이스 주소',
    'admin/vodplayer/alone_api_url'=>'독립 인터페이스 주소',
    'admin/vodplayer/code_tip'=>'고유 식별자 영어, 숫자만 있는 경우 자동으로 _가 추가되며 ./\\ 등의 기호는 사용할 수 없습니다.',
    'admin/vodplayer/name_tip'=>'중국어 이름',
    'admin/vodplayer/api_url'=>'인터페이스 주소',
    'admin/vodplayer/api_url_tip'=>'독립 인터페이스 주소, 전역 플레이어 설정의 인터페이스보다 우선 순위가 높습니다.',
    'admin/vodplayer/sort_tip'=>'값이 클수록 순위가 높습니다.',
    'admin/vodplayer/code_empty'=>'코드를 입력하십시오.',
    'admin/vodplayer/import_tip'=>'팁: <br>
                       1. 가져오기 파일 형식이 올바른지 확인하십시오.',


    'admin/timming/title'=>'예약 작업 관리',
    'admin/timming/unique_id'=>'고유 식별자 영어',
    'admin/timming/call_method'=>'호출 방법',
    'admin/timming/exec_file'=>'실행 파일',
    'admin/timming/collect'=>'사용자 지정 수집 collect',
    'admin/timming/make'=>'정적 생성 make',
    'admin/timming/cj'=>'사용자 지정 수집 규칙 cj',
    'admin/timming/cache'=>'캐시 지우기 cache',
    'admin/timming/urlsend'=>'URL 푸시 urlsend',
    'admin/timming/attach_param'=>'추가 매개변수',
    'admin/timming/attach_param_tip'=>'비워 둘 수 있습니다. 예: ac=timming&id=1',
    'admin/timming/exec_cycle'=>'실행 주기',
    'admin/timming/exec_time'=>'실행 시간',

    'monday'=>'월요일',
    'tuesday'=>'화요일',
    'wednesday'=>'수요일',
    'thursday'=>'목요일',
    'friday'=>'금요일',
    'saturday'=>'토요일',
    'sunday'=>'일요일',


    'admin/domain/title'=>'웹사이트 그룹 관리',
    'admin/domain/help_tip'=>'팁 정보: <br>
                        1. 이 기능은 비정적 모드에서 동일한 데이터베이스, 다른 도메인이 다른 템플릿과 웹사이트 구성 정보를 표시하도록 지원합니다. <br>
                        2. 도메인 웹사이트 수에 제한이 없습니다. <br>
                        3. 가져오기 텍스트 형식은 도메인$웹사이트 이름$키워드$설명$템플릿$템플릿 디렉토리$광고 디렉토리입니다. 줄당 하나의 웹사이트입니다. 기존 데이터를 지웁니다. <br>',

    'admin/domain/title'=>'웹사이트 그룹 관리',


    'admin/website/title'=>'웹사이트 관리',
    'admin/website/referer'=>'총 리퍼러',
    'admin/website/referer_month'=>'월간 리퍼러',
    'admin/website/referer_week'=>'주간 리퍼러',
    'admin/website/referer_day'=>'일일 리퍼러',




    'admin/vod/title'=>'동영상 관리',
    'admin/vod/no'=>'없음',
    'admin/vod/have'=>'있음',
    'admin/vod/plot/title'=>'에피소드 줄거리 관리',
    'admin/vod/del_play_must_select_play'=>'재생 그룹을 삭제할 때 플레이어 매개변수를 선택해야 합니다.',
    'admin/vod/del_down_must_select_down'=>'다운로드 그룹을 삭제할 때 다운로더 매개변수를 선택해야 합니다.',
    'admin/vod/select_weekday'=>'주기 선택',
    'admin/vod/select_area'=>'지역 선택',
    'admin/vod/select_lang'=>'언어 선택',
    'admin/vod/select_player'=>'플레이어 선택',
    'admin/vod/select_server'=>'서버 선택',
    'admin/vod/player_empty'=>'빈 플레이어',
    'admin/vod/select_downer'=>'다운로더 선택',
    'admin/vod/downer_empty'=>'빈 다운로더',
    'admin/vod/select_isend'=>'완결 선택',
    'admin/vod/select_copyright'=>'저작권 선택',
    'admin/vod/is_end'=>'완결됨',
    'admin/vod/no_end'=>'완결되지 않음',
    'admin/vod/del_player'=>'재생 그룹 삭제',
    'admin/vod/del_downer'=>'다운로드 그룹 삭제',
    'admin/vod/episode_plot'=>'에피소드 줄거리',
    'admin/vod/plot'=>'에피소드 줄거리',
    'admin/vod/plot_name'=>'줄거리 제목',
    'admin/vod/move_up'=>'위로 이동',
    'admin/vod/move_down'=>'아래로 이동',
    'admin/vod/copyright_open'=>'저작권 처리 활성화',
    'admin/vod/copyright_close'=>'저작권 처리 비활성화',
    'admin/vod/move_behind'=>'비하인드 스토리',
    'admin/vod/total'=>'총 에피소드 수',
    'admin/vod/serial'=>'연재 수',
    'admin/vod/pubdate'=>'개봉일',
    'admin/vod/director'=>'감독',
    'admin/vod/writer'=>'작가',
    'admin/vod/tv'=>'TV 채널',
    'admin/vod/weekday'=>'프로그램 주기',
    'admin/vod/duration'=>'동영상 길이',
    'admin/vod/douban_score'=>'Douban 평점',
    'admin/vod/douban_id'=>'Douban ID',
    'admin/vod/douban_id_empty'=>'Douban ID를 입력하십시오.',
    'admin/vod/rel_vod_tip'=>'예: "트랜스포머" 1, 2, 3부의 ID는 각각 11, 12, 13이거나 각 부분을 "트랜스포머"로 채웁니다.',
    'admin/vod/rel_art_tip'=>'예: "트랜스포머 정보" 1, 2, 3부의 ID는 각각 11, 12, 13이거나 각 부분을 "트랜스포머 정보"로 채웁니다.',
    'admin/vod/version'=>'리소스 버전',
    'admin/vod/state'=>'리소스 카테고리',
    'admin/vod/isend'=>'완결',
    'admin/vod/tpl'=>'콘텐츠 페이지 템플릿',
    'admin/vod/tpl_play'=>'재생 페이지 템플릿',
    'admin/vod/tpl_down'=>'다운로드 페이지 템플릿',
    'admin/vod/correct'=>'수정',
    'admin/vod/reverse_order'=>'역순',
    'admin/vod/del_prefix'=>'접두사 제거',
    'admin/vod/complete_works'=>'전체 작품',
    'admin/vod/stint_play'=>'에피소드당 필요한 포인트',
    'admin/vod/stint_down'=>'에피소드당 다운로드에 필요한 포인트',
    'admin/vod/select_plot'=>'에피소드 줄거리 선택',
    'admin/vod/copyright'=>'저작권',
    'admin/vod/serialize'=>'연재',
    'admin/vod/add_group_play'=>'재생 그룹 추가',
    'admin/vod/add_group_down'=>'다운로드 그룹 추가',
    'admin/batch_tip'=>'총 %s개의 데이터를 처리해야 하며 페이지당 %s개, 총 %s페이지, 현재 %s페이지 데이터를 처리 중입니다.',


    'admin/template/title'=>'템플릿 관리',
    'admin/template/ads/title'=>'광고 공간 관리',
    'admin/template/wizard/title'=>'태그 마법사 관리',
    'admin/template/ext_safe_tip'=>'보안 팁, 접미사는 htm, html, js, xml만 허용됩니다.',
    'admin/template/php_safe_tip'=>'보안 팁, 템플릿에 PHP 코드가 포함된 경우 백엔드에서 편집할 수 없습니다.',
    'admin/template/call_code'=>'호출 코드',
    'admin/template/current_dir'=>'현재 경로',
    'admin/template/name_tip'=>'접미사는 html, htm, js, xml만 허용됩니다. 사용자 지정 페이지는 label_로 시작합니다.',
    'admin/template/reverse_order'=>'역순',
    'admin/template/positive_order'=>'정순',
    'admin/template/filter_search'=>'필터 검색 링크',
    'admin/template/reply_content'=>'답변 내용',


    'admin/cj/title'=>'사용자 지정 수집 관리',
    'admin/cj/url/title'=>'수집 URL 주소',
    'admin/cj/publish/title'=>'콘텐츠 게시 관리',
    'admin/cj/url_list_err'=>'URL 정보를 가져오지 못했습니다.',
    'admin/cj/url_cj_complete'=>'URL 수집 완료',
    'admin/cj/content/tip'=>'콘텐츠를 수집 중이며 총 【%s】개, %s페이지로 나누어 페이지당 %s개를 수집하고 현재 %s페이지입니다.',
    'admin/cj/content_cj_complete'=>'콘텐츠 수집 완료',
    'admin/cj/cj_complete'=>'수집 완료',
    'admin/cj/content_into/tip'=>'콘텐츠를 가져오는 중이며 총 【%s】개, %s페이지로 나누어 페이지당 %s개를 수집하고 현재 %s페이지입니다.',
    'admin/cj/content_into/complete'=>'콘텐츠 데이터베이스 입력 완료',
    'admin/cj/cj_url'=>'수집 URL',
    'admin/cj/cj_content'=>'수집 콘텐츠',
    'admin/cj/content_publish'=>'콘텐츠 게시',
    'admin/cj/publish_plan'=>'게시 계획',
    'admin/cj/collected'=>'수집됨',
    'admin/cj/collected_not'=>'수집되지 않음',
    'admin/cj/published'=>'게시됨',
    'admin/cj/trim_space'=>'공백 제거',
    'admin/cj/label_data_rel'=>'태그와 데이터베이스 간의 대응 관계',
    'admin/cj/data_column'=>'데이터베이스 필드',
    'admin/cj/label_column'=>'태그 필드',
    'admin/cj/processing_function'=>'처리 함수',
    'admin/cj/rule_url'=>'URL 규칙',
    'admin/cj/rule_content'=>'콘텐츠 규칙',
    'admin/cj/rule_diy'=>'사용자 지정 규칙',
    'admin/cj/adv_config'=>'고급 구성',
    'admin/cj/rule_name'=>'규칙 이름',
    'admin/cj/rule_name_en'=>'규칙 영어 이름',
    'admin/cj/page_charset'=>'웹 페이지 인코딩',
    'admin/cj/cj_model'=>'수집 모듈',
    'admin/cj/url_collect'=>'URL 수집',
    'admin/cj/url_type'=>'URL 유형',
    'admin/cj/sequence_url'=>'순차 URL',
    'admin/cj/multi_url'=>'여러 URL',
    'admin/cj/one_url'=>'단일 URL',
    'admin/cj/wildcard_tip'=>'와일드카드로 사용',
    'admin/cj/page_num_config'=>'페이지 번호 구성',
    'admin/cj/page_num_increment'=>'페이지 번호 증가',
    'admin/cj/one_per_line'=>'줄당 하나',
    'admin/cj/url_config'=>'URL 구성',
    'admin/cj/url_must_contain'=>'URL에 포함되어야 하는 항목',
    'admin/cj/url_not_contain'=>'URL에 포함되어서는 안 되는 항목',
    'admin/cj/collect_interval'=>'수집 간격',
    'admin/cj/wildcard_prompt'=>'<p>1. 일치 규칙의 경우 시작 및 종료 문자를 설정하고 특정 내용에는 "[내용]"을 와일드카드로 사용하십시오.</p>
                                    <p>2. 일치 규칙은 고정 콘텐츠일 수도 있으며 "[내용]" 와일드카드가 나타나지 않으면 고정 콘텐츠로 간주됩니다.</p>
                                    <p>3. 필터 옵션 형식은 "필터링할 내용[|]바꿀 값"이며 필터링할 내용은 정규식을 지원하며 줄당 하나입니다.</p>',



    'admin/cj/title_rule'=>'제목 규칙',
    'admin/cj/match_rule'=>'일치 규칙',
    'admin/cj/filter_rule'=>'필터 규칙',
    'admin/cj/type_rule'=>'카테고리 규칙',
    'admin/cj/content_rule'=>'콘텐츠 규칙',
    'admin/cj/page_mode'=>'페이지 매김 모드',
    'admin/cj/list_all_mode'=>'전체 목록 모드',
    'admin/cj/next_page_mode'=>'이전/다음 페이지 모드',
    'admin/cj/next_page_rule'=>'다음 페이지 규칙',
    'admin/cj/next_page_tip'=>'다음 페이지 하이퍼링크 중간의 코드를 입력하십시오. 예: <a href="http://www.xxx.com/page_1.html">다음 페이지</a>의 경우 "다음 페이지 규칙"은 "다음 페이지"입니다.',
    'admin/cj/add_group'=>'그룹 추가',

    'admin/cj/content_page'=>'콘텐츠 페이지 매김',
    'admin/cj/no_page'=>'페이지 매김 없음',
    'admin/cj/original_page'=>'원본 텍스트로 페이지 매김',
    'admin/cj/import_sort'=>'가져오기 순서',
    'admin/cj/same_to_site'=>'대상 사이트와 동일',
    'admin/cj/opposite_to_site'=>'대상 사이트와 반대',


    'admin/collect/title'=>'수집 리소스 관리',
    'admin/collect/load_break'=>'중단점 위치를 로드하는 중입니다. 잠시 기다려 주십시오...',
    'admin/collect/view_all_resource'=>'모든 리소스 보기',
    'admin/collect/cj_select'=>'선택한 항목 수집',
    'admin/collect/cj_today'=>'오늘 수집',
    'admin/collect/cj_all'=>'전체 수집',
    'admin/collect/clear_bind'=>'바인딩 지우기',
    'admin/collect/name'=>'리소스 이름',
    'admin/collect/api_url'=>'인터페이스 주소',
    'admin/collect/attach_param'=>'추가 매개변수',
    'admin/collect/attach_param_tip'=>'팁 정보: 일반적으로 &로 시작하며 예를 들어 이전 버전의 XML 형식에서 다운로드 주소를 수집하려면 &ct=1을 추가해야 합니다.',
    'admin/collect/api_type'=>'인터페이스 유형',
    'admin/collect/data_type'=>'리소스 유형',
    'admin/collect/data_opt'=>'데이터 작업',
    'admin/collect/data_opt_tip'=>'팁 정보: 특정 리소스를 보조 리소스로 사용하고 데이터를 추가하지 않으려면 업데이트만 선택하십시오.',
    'admin/collect/add_update'=>'추가 + 업데이트',
    'admin/collect/add'=>'추가',
    'admin/collect/update'=>'업데이트',
    'admin/collect/url_filter'=>'주소 필터링',
    'admin/collect/no_filter'=>'필터링 없음',
    'admin/collect/filter_code'=>'필터 코드',
    'admin/collect/filter_code_tip'=>'여러 그룹의 주소가 있는 리소스에서 화이트리스트를 활성화하면 지정된 코드의 주소만 데이터베이스에 입력됩니다. 예: youku,iqiyi',
    'admin/collect/filter_year'=>'연도 필터링',
    'admin/collect/filter_year_tip'=>'입력 후 지정된 연도의 영화만 데이터베이스에 입력됩니다. 여러 연도는 영어 반각 쉼표로 구분합니다. 예: 2022,2023',
    'admin/collect/test_ok'=>'테스트 유형 성공, 인터페이스 유형',

    'admin/comment/title'=>'댓글 관리',

    'admin/gbook/title'=>'방명록 관리',


    'admin/group/title'=>'회원 그룹 관리',
    'admin/group/reg_group_del_err'=>'가입 기본 회원 그룹은 삭제할 수 없습니다!',
    'admin/group/help_tip'=>'팁 정보: <br>
        1. 방문자, 일반 회원은 시스템에 내장된 회원 그룹에 속하며 삭제 및 비활성화할 수 없습니다. <br>2. 각 회원 그룹의 권한을 별도로 설정하십시오. 권한은 아래로 상속되지 않습니다.',
    'admin/group/pack_day'=>'일일 패키지',
    'admin/group/pack_week'=>'주간 패키지',
    'admin/group/pack_month'=>'월간 패키지',
    'admin/group/pack_year'=>'연간 패키지',
    'admin/group/popedom'=>'관련 권한',
    'admin/group/popedom_tip'=>'팁: <br>
                    1. 목록 페이지, 콘텐츠 페이지, 재생 페이지, 다운로드 페이지의 4가지 권한은 페이지에 들어갈 수 있는지 여부를 제어하며 권한이 없으면 팁 정보가 직접 반환됩니다.<br>
                    2. 미리보기 권한: 재생 페이지에 액세스할 권한이 없거나 권한이 있지만 포인트를 구매해야 하는 데이터의 경우 미리보기 권한을 활성화하면 페이지에 들어갈 수 있습니다.',
    'admin/group/popedom_list'=>'목록 페이지',
    'admin/group/popedom_detail'=>'콘텐츠 페이지',
    'admin/group/popedom_play'=>'재생 페이지',
    'admin/group/popedom_down'=>'다운로드 페이지',
    'admin/group/popedom_trysee'=>'미리보기',

    'admin/annex/title'=>'첨부 파일 관리',
    'admin/annex/check'=>'잘못된 파일 검사',
    'admin/annex/check_complete'=>'잘못된 파일 정리 완료',
    'admin/annex/info_tip'=>'총 %s개의 데이터, %s회 검사, 회당 %s개, 현재 %s회',

    'admin/annex/init_tip'=>'<strong>첨부 파일 데이터 초기화 1.0 버전</strong><br>
                            1. 카테고리 테이블, 동영상, 기사, 웹사이트, 배우, 역할, 회원 등의 테이블을 검색합니다.<br>
                            2. 로컬 이미지 주소 콘텐츠를 첨부 파일 테이블에 삽입합니다.<br>
                            3. 업그레이드된 버전은 한 번 실행하는 것이 좋습니다.',
    'admin/annex/init_data'=>'데이터 초기화',
    'admin/annex/dir_model'=>'폴더 모드',
    'admin/annex/check_ok'=>'첨부 파일 데이터 초기화 완료',
    'admin/annex/check_tip1'=>'%s 테이블을 검사하는 중... 총 %s개, %s회 검사, 회당 %s개, 현재 %s회',
    'admin/annex/check_jump'=>'%s 테이블 검사 완료, 잠시 후 계속...',


    'admin/images/title'=>'이미지 관리',
    'admin/images/sync_complete'=>'동기화 작업 완료!',
    'admin/images/sync_tip'=>'총 %s개의 데이터를 처리해야 하며 페이지당 %s개, 총 %s페이지, 현재 %s페이지 데이터를 처리 중입니다.',
    'admin/images/sync_range'=>'동기화 범위',
    'admin/images/sync_option'=>'동기화 옵션',
    'admin/images/date'=>'데이터 날짜',
    'admin/images/opt/tip1'=>'동기화 필드-자세히 보기 이미지를 동기화할 때 동기화 옵션 매개변수가 작동하지 않습니다!',
    'admin/images/opt/tip2'=>'페이지당 동기화할 항목 수, 너무 크게 설정하지 않는 것이 좋습니다.',
    'admin/images/opt/pic'=>'기본 이미지',
    'admin/images/pic_content'=>'자세히 보기 이미지',


    'admin/database/title'=>'데이터베이스 관리',
    'admin/database/select_export_table'=>'백업할 데이터 테이블을 선택하십시오!',
    'admin/database/lock_check'=>'백업 작업이 실행 중인 것으로 감지되었습니다. 나중에 다시 시도하십시오!',
    'admin/database/backup_err'=>'백업 오류!',
    'admin/database/backup_ok'=>'백업 완료!',
    'admin/database/select_file'=>'복원할 백업 파일을 선택하십시오!',
    'admin/database/import_ok'=>'데이터 복원 완료!',
    'admin/database/import_err'=>'데이터 복원 오류!',
    'admin/database/file_damage'=>'백업 파일이 손상되었을 수 있습니다. 확인하십시오!',
    'admin/database/select_optimize_table'=>'최적화할 데이터 테이블을 선택하십시오!',
    'admin/database/optimize_ok'=>'데이터 테이블 최적화 완료!',
    'admin/database/optimize_err'=>'데이터 테이블 최적화 실패!',
    'admin/database/select_repair_table'=>'복구할 데이터 테이블을 선택하십시오!',
    'admin/database/repair_ok'=>'데이터 테이블 복구 완료!',
    'admin/database/repair_err'=>'데이터 테이블 복구 실패!',
    'admin/database/select_del_file'=>'삭제할 백업 파일을 선택하십시오!',
    'admin/database/backup_db'=>'데이터베이스 백업',
    'admin/database/import_db'=>'데이터베이스 복원',
    'admin/database/optimize_db'=>'데이터베이스 최적화',
    'admin/database/repair_db'=>'데이터베이스 복구',
    'admin/database/table'=>'테이블 이름',
    'admin/database/count'=>'데이터 양',
    'admin/database/size'=>'크기',
    'admin/database/redundancy'=>'중복',
    'admin/database/optimize'=>'최적화',
    'admin/database/repair'=>'복구',
    'admin/database/backup_name'=>'백업 이름',
    'admin/database/backup_num'=>'백업 볼륨 수',
    'admin/database/backup_zip'=>'백업 압축',
    'admin/database/backup_size'=>'백업 크기',
    'admin/database/backup_time'=>'백업 시간',
    'admin/database/import'=>'복원',
    'admin/database/import_confirm'=>'이 백업을 복원하시겠습니까? 이 작업은 복구할 수 없습니다.',
    'admin/database/batch_replace'=>'일괄 바꾸기',
    'admin/database/select_table'=>'데이터 테이블 선택',
    'admin/database/select_col'=>'필드 선택',
    'admin/database/field'=>'바꿀 필드',
    'admin/database/findstr'=>'바꿀 내용',
    'admin/database/tostr'=>'바꿀 내용',
    'admin/database/where'=>'바꾸기 조건',

    'admin/database/sql'=>'SQL 문 실행',
    'admin/database/sql_tip'=>'일반적인 문 비교: <br>
                        1. 데이터 쿼리
                        SELECT * FROM {pre}vod 모든 데이터 쿼리 <br>
                        SELECT * FROM {pre}vod WHERE vod_id=1000 지정된 ID 데이터 쿼리
                        <br>
                        2. 데이터 삭제
                        DELETE FROM {pre}vod 모든 데이터 삭제 <br>
                        DELETE FROM {pre}vod WHERE vod_id=1000 지정된 데이터 삭제 <br>
                        DELETE FROM {pre}vod WHERE vod_actor LIKE \'%유덕화%\' vod_actor "유덕화" 데이터
                        <br>
                        3. 데이터 수정
                        UPDATE {pre}vod SET vod_hits=1 모든 vod_hits 필드의 값을 "1"로 수정 <br>
                        UPDATE {pre}vod SET vod_hits=1 WHERE vod_id=1000 지정된 데이터의 vod_hits 필드 값을 "1"로 수정
                        <br>
                        4. 이미지 주소 바꾸기
                        UPDATE {pre}vod SET vod_pic=REPLACE(vod_pic, \'원래 문자열\', \'다른 문자열로 바꾸기\')
                        <br>
                        5. 데이터 ID 지우고 1부터 다시 시작
                        TRUNCATE {pre}vod',

    'admin/safety/data_inspect'=>'악성코드 검사',
    'admin/safety/data_inspect_tip'=>'<strong>악성코드 검사 3.0 버전</strong><br>
                            1. 카테고리 테이블, 동영상 테이블, 기사 테이블, 회원 테이블 등의 테이블 구조를 검사합니다.<br>
                            2. script, iframe 등 특수 문자열을 검사합니다.<br>
                            3. 악성코드를 자동으로 지웁니다.<br>
                            4. 100% 제거를 보장할 수 없으며 문제가 있는 경우 phpmyadmin 또는 기타 데이터베이스 관리 도구에서 직접 제거하십시오.<br>
                            5. 문제가 있는 데이터가 나타나지 않을 때까지 여러 번 정리하는 것이 좋습니다.',
    'admin/safety/data_clear_ok'=>'정리 완료, 누락된 데이터가 없는지 다시 실행하십시오.',
    'admin/safety/data_check_tip1'=>'%s 테이블 검사 시작...',
    'admin/safety/data_check_tip2'=>'총 %s개의 위험한 데이터가 감지되었습니다...',

    'admin/safety/exec'=>'실행 확인',
    'admin/safety/file_inspect'=>'파일 보안 검사',
    'admin/safety/file_inspect_tip'=>'<strong>보안 검사 3.0 버전</strong><br>
                            1. 웹사이트의 모든 파일을 비교하여 검사합니다.<br>
                            2. 원본 프로그램 패키지에 포함된 파일은 md5를 비교하여 나열됩니다.<br>
                            3. 원본 프로그램 패키지에 없는 새로 추가된 파일이 나열됩니다.<br>
                            4. 100% 정확성을 보장할 수 없으며 문제가 있는 경우 공식 웹사이트 github에 보고하십시오.<br>
                            5. 여러 번 검사하고 나열된 각 파일을 자세히 검사하는 것이 좋습니다.',
    'admin/safety/file_msg1'=>'공식 파일 데이터를 가져오지 못했습니다. 다시 시도하십시오.',
    'admin/safety/file_msg2'=>'로컬 파일 목록을 가져오지 못했습니다. 다시 시도하십시오.',
    'admin/safety/file_msg3'=>'새로 추가된 파일',
    'admin/safety/file_msg4'=>'다른 파일',

    'admin/link/title'=>'링크 관리',
    'admin/link/text_link'=>'텍스트 링크',
    'admin/link/pic_link'=>'이미지 링크',


    'admin/make/title'=>'정적 생성 관리',
    'admin/make/view_model_static_err'=>'찾아보기 모드가 정적이 아니므로 생성할 수 없습니다.',
    'admin/make/typepage_make_complete'=>'카테고리 페이지 생성 완료',
    'admin/make/typepage_make_complete_later_make_index'=>'카테고리 페이지 생성 완료, 잠시 후 홈페이지 생성 계속',
    'admin/make/list_make_complate_later'=>'목록 페이지 생성 완료, 잠시 후 계속',
    'admin/make/type_tip'=>'【%s】 목록 페이지를 생성하는 중이며 총 %s페이지, %s회 생성, 현재 %s회',
    'admin/make/type_timming_tip'=>'예약 작업 완료, 이번에는 각 카테고리에 %s개의 목록 페이지를 생성하여 웹사이트가 멈추는 것을 방지합니다!',
    'admin/make/topicpage_make_complete'=>'특집 목록 페이지 생성 완료',
    'admin/make/topic_index_tip'=>'특집 목록 페이지를 생성하는 중이며 총 %s페이지, %s회 생성, 현재 %s회',
    'admin/make/topic_tip'=>'특집 콘텐츠 페이지를 생성하는 중이며 총 %s개',
    'admin/make/topic_make_complete'=>'특집 콘텐츠 페이지 생성 완료',
    'admin/make/info_make_complete'=>'콘텐츠 페이지 생성 완료',
    'admin/make/info_make_complete_later_make_type'=>'콘텐츠 페이지 생성 완료, 잠시 후 카테고리 페이지 생성 계속',
    'admin/make/info_make_complete_later'=>'콘텐츠 페이지 생성 완료, 잠시 후 계속',
    'admin/make/info_tip'=>'【%s】 콘텐츠 페이지를 생성하는 중이며 총 %s개, %s회 생성, 회당 %s개, 현재 %s회',
    'admin/make/label_tip'=>'사용자 지정 페이지를 생성하는 중이며 총 %s개 페이지',
    'admin/make/label_complete'=>'사용자 지정 페이지 생성 완료',
    'admin/make/select_type'=>'카테고리 선택',
    'admin/make/all_type'=>'모든 카테고리',
    'admin/make/today_type'=>'오늘의 카테고리',
    'admin/make/select_info'=>'콘텐츠 선택',
    'admin/make/all_info'=>'모든 콘텐츠',
    'admin/make/today_info'=>'오늘의 콘텐츠',
    'admin/make/no_make_info'=>'생성되지 않은 콘텐츠',
    'admin/make/one_today'=>'오늘 한 번에',
    'admin/make/topic_list'=>'특집 목록',
    'admin/make/select_topic'=>'특집 선택',
    'admin/make/all_topic'=>'모든 특집',
    'admin/make/topic_index'=>'특집 홈페이지',
    'admin/make/label_page'=>'사용자 지정 페이지',
    'admin/make/rss'=>'RSS 구독 파일',
    'admin/make/google'=>'Google 사이트맵',
    'admin/make/baidu'=>'Baidu 사이트맵',
    'admin/make/so'=>'360 사이트맵',
    'admin/make/sogou'=>'Sogou 사이트맵',
    'admin/make/bing'=>'Bing 사이트맵',
    'admin/make/sm'=>'Shenma 사이트맵',
    'admin/make/make_page_num'=>'생성할 페이지 수',


    'admin/order/title'=>'주문 관리',
    'admin/order/order_no'=>'주문 번호',
    'admin/order/order_money'=>'주문 금액',
    'admin/order/order_status'=>'주문 상태',
    'admin/order/order_time'=>'주문 시간',
    'admin/order/pay_type'=>'결제 유형',
    'admin/order/pay_time'=>'결제 시간',




    'admin/plog/title'=>'포인트 로그 관리',
    'admin/plog/log_time'=>'로그 시간',
    'admin/plog/points_recharge'=>'포인트 충전',
    'admin/plog/reg_promote'=>'가입 프로모션',
    'admin/plog/visit_promote'=>'방문 프로모션',
    'admin/plog/three_distribution'=>'3단계 배포',
    'admin/plog/points_upgrade'=>'포인트 업그레이드',
    'admin/plog/points_buy'=>'포인트 소비',
    'admin/plog/points_withdrawal'=>'포인트 출금',


    'admin/role/title'=>'역할 관리',
    'admin/topic/title'=>'특집 관리',
    'admin/topic/vod_include'=>'동영상 포함',
    'admin/topic/art_include'=>'기사 포함',
    'admin/topic/tpl_empty'=>'특집 템플릿을 입력하십시오.',
    'admin/topic/count'=>'포함된 데이터 양',


    'admin/type/title'=>'카테고리 관리',
    'admin/type/type_tpl'=>'카테고리 페이지 템플릿',
    'admin/type/show_tpl'=>'필터 페이지 템플릿',
    'admin/type/detail_tpl'=>'콘텐츠 페이지 템플릿',
    'admin/type/play_tpl'=>'재생 페이지 템플릿',
    'admin/type/down_tpl'=>'다운로드 페이지 템플릿',
    'admin/type/tip'=>'팁 정보: <br>
            1. 새 카테고리를 추가한 후 사용자-회원 그룹에서 각 그룹에 대한 권한을 설정하십시오. 그렇지 않으면 권한 없이 액세스할 수 있다는 메시지가 표시됩니다.',
    'admin/type/parent_type'=>'상위 카테고리',
    'admin/type/top_type'=>'최상위 카테고리',
    'admin/type/logo'=>'카테고리 아이콘',
    'admin/type/pic'=>'카테고리 표지',
    'admin/type/tpl_empty'=>'카테고리 페이지 템플릿을 입력하십시오.',
    'admin/type/extend_version'=>'확장 버전',
    'admin/type/extend_state'=>'확장 리소스',
    'admin/type/extend_director'=>'확장 감독',
    'admin/type/extend_star'=>'확장 배우',


    'admin/ulog/title'=>'로그 관리',

    'admin/update/step1_a'=>'온라인 업그레이드 중 첫 번째 단계 [파일 업그레이드], 잠시 기다려 주십시오...',
    'admin/update/step1_b'=>'업그레이드 파일 패키지를 다운로드하는 중...',
    'admin/update/download_err'=>'업그레이드 패키지 다운로드 실패, 다시 시도하십시오...',
    'admin/update/download_ok'=>'업그레이드 패키지 다운로드 완료...',
    'admin/update/upgrade_package_processed'=>'업그레이드 패키지 파일을 처리하는 중...',
    'admin/update/upgrade_err'=>'업그레이드 실패, 시스템 디렉토리 및 파일 권한을 확인하십시오!...',
    'admin/update/step2_a'=>'온라인 업그레이드 중 두 번째 단계 [데이터 업그레이드], 잠시 기다려 주십시오...',
    'admin/update/upgrade_sql'=>'데이터베이스 업그레이드 스크립트 파일이 감지되었습니다. 처리 중...',
    'admin/update/no_sql'=>'데이터베이스 업그레이드 스크립트가 감지되지 않았습니다. 잠시 후 데이터 캐시 업데이트 부분으로 이동합니다...',
    'admin/update/step3_a'=>'온라인 업그레이드 중 세 번째 단계 [캐시 업데이트], 잠시 기다려 주십시오...',
    'admin/update/update_cache'=>'데이터 캐시 파일을 업데이트하는 중...',
    'admin/update/upgrade_complete'=>'축하합니다. 시스템 업그레이드가 완료되었습니다...',

    'admin/upload/test_write_ok'=>'쓰기 테스트 성공',
    'admin/upload/test_write_ok'=>'쓰기 실패, 임시 파일 디렉토리 권한을 확인하십시오.',
    'admin/upload/not_find_extend'=>'타사 확장 업로드 라이브러리를 찾을 수 없습니다.',
    'admin/upload/no_input_file'=>'업로드된 파일을 찾을 수 없습니다(이유: 양식 이름이 잘못되었을 수 있습니다. 기본 양식 이름은 "file" 또는 "imgdata"입니다)!',
    'admin/upload/forbidden_ext'=>'시스템에서 허용하지 않는 업로드 형식입니다!',
    'admin/upload/upload_success'=>'파일 업로드 성공!',
    'admin/upload/upload_faild'=>'파일 업로드 실패!',
    'admin/upload/make_thumb_faild'=>'파일 업로드 실패!',
    'admin/upload/upload_safe'=>'파일에 위험한 내용이 포함되어 있습니다!',

    'admin/urlsend/title'=>'URL 푸시 관리',
    'admin/urlsend/no_data'=>'데이터를 가져오지 못했습니다.',
    'admin/urlsend/tip'=>'총 %s개의 데이터를 푸시해야 하며 %s페이지로 나누어 푸시하고 현재 %s페이지입니다.',
    'admin/urlsend/complete'=>'데이터 푸시 완료',
    'admin/urlsend/tip2'=>'중단점은 캐시에 기록되며 캐시를 업데이트하면 중단점이 사라집니다.<br>
            푸시를 시작하기 전에 위의 필요한 구성 항목을 입력하십시오.<br>
            현재 사이트 구성 도메인:',
    'admin/urlsend/send_genre'=>'푸시 유형',
    'admin/urlsend/page_send_num'=>'페이지당 푸시 수',
    'admin/urlsend/start_page'=>'시작 페이지 번호',
    'admin/urlsend/in_break_point_exec'=>'중단점에서 계속 실행',
    'admin/urlsend/send_range'=>'푸시 범위',
    'admin/urlsend/add_update'=>'추가 + 업데이트',
    'admin/urlsend/add'=>'추가',

    'admin/user/title'=>'사용자 관리',
    'admin/user/comment_record'=>'댓글 기록',
    'admin/user/order_record'=>'주문 기록',
    'admin/user/visit_record'=>'방문 기록',
    'admin/user/point_record'=>'포인트 기록',
    'admin/user/withdrawals_record'=>'출금 기록',
    'admin/user/three_distribution'=>'3단계 배포',
    'admin/user/time_end'=>'패키지 시간 만료',
    'admin/user/find_question'=>'비밀번호 찾기 질문',
    'admin/user/find_answer'=>'비밀번호 찾기 답변',
    'admin/user/access_empty'=>'계정을 입력하십시오.',
    'admin/user/pass_empty'=>'비밀번호를 입력하십시오.',

    'admin/user/reward/select_level'=>'레벨 선택',
    'admin/user/reward/one_distribution'=>'1단계 배포',
    'admin/user/reward/two_distribution'=>'2단계 배포',
    'admin/user/reward/three_distribution'=>'3단계 배포',
    'admin/user/reward/distribution_level'=>'3단계 배포',
    'admin/user/reward/one_people_num'=>'1단계 배포 총 인원',
    'admin/user/reward/two_people_num'=>'2단계 배포 총 인원',
    'admin/user/reward/three_people_num'=>'3단계 배포 총 인원',
    'admin/user/reward/total_commission_points'=>'총 수수료 포인트',

    'admin/visit/title'=>'방문 기록 관리',


    'api/auth_err'=>'도메인이 권한을 부여받지 못했습니다.',
    'api/close_err'=>'인터페이스 닫힘 오류',
    'api/pass_err'=>'불법 사용 오류',
    'api/pass_safe_err'=>'보안을 위해 데이터베이스 비밀번호는 16자 이상이어야 합니다.',
    'api/require_name'=>'이름은 필수입니다. 오류',
    'api/require_type'=>'카테고리 이름과 카테고리 ID 중 하나 이상을 입력해야 합니다. 오류',
    'api/require_sex'=>'성별은 필수입니다. 오류',
    'api/require_actor_name'=>'배우 이름은 필수입니다. 오류',
    'api/require_role_name'=>'역할 이름은 필수입니다. 오류',
    'api/require_rel_vod'=>'관련 데이터 이름 vod_name 또는 Douban 번호 douban_id 중 하나 이상을 입력해야 합니다. 오류',
    'api/require_rel_name'=>'관련 데이터 이름 rel_name 또는 Douban 번호 douban_id 중 하나 이상을 입력해야 합니다. 오류',
    'api/require_mid'=>'모듈 ID는 필수입니다. 오류',
    'api/require_comment_name'=>'댓글 닉네임은 필수입니다. 오류',
    'api/require_comment_name'=>'댓글 내용은 필수입니다. 오류',
    'api/never'=>'없음',
    'api/task_tip_exec'=>'작업: %s, 상태: %s, 마지막 실행 시간: %s---실행',
    'api/task_tip_jump'=>'작업: %s, 상태: %s, 마지막 실행 시간: %s---건너뛰기',


    'install/title'=>'Apple CMS-V10 시스템 설치',
    'install/header'=>'Apple CMS-V10 시스템을 사용하여 웹사이트를 구축해 주셔서 감사합니다.',
    'install/lang'=>'언어 팩 [langs]',
    'install/select_lang'=>'언어 팩을 선택하십시오 [select lang]',
    'install/lang_tip'=>'필요에 따라 백엔드 언어 팩을 선택하십시오.',

    'install/user_agreement_title'=>'Apple CMS 사용자 계약 모든 사용자에게 적용',
    'install/user_agreement'=>' (Apple CMS)를 사용하기 전에 다음 조항을 주의 깊게 읽으십시오. 여기에는 저자의 책임을 면제하거나 제한하는 면책 조항과 사용자의 권리 제한이 포함됩니다. 설치 및 사용 행위는 이 "사용자 라이선스 계약"에 동의하고 이 "사용자 라이선스 계약"의 모든 조항을 준수하는 데 동의하는 것으로 간주됩니다. <br /><br />
                1. 설치 및 사용: <br />
                (Apple CMS)는 무료로 오픈 소스로 제공되며 무제한으로 복사하여 설치할 수 있습니다. 불법 활동을 수행하지 않고 소재 국가의 관련 정책 및 규정을 위반하지 않는다는 전제 하에 이 소프트웨어를 사용해야 합니다. <br /><br />
                2. 면책 조항: <br />
                이 소프트웨어는 이 소프트웨어의 적합성, 지적 재산권 비침해 또는 특정 목적에 대한 적합성에 대한 보증을 포함하여 어떠한 형태의 명시적 또는 묵시적 보증도 제공하지 않습니다. <br />
                어떠한 경우에도 저자는 이 소프트웨어의 사용 또는 사용 불가로 인해 발생하는 손해에 대해 책임을 지지 않습니다. 저자는 이 소프트웨어에 포함된 자료, 텍스트, 그래픽, 링크 또는 기타 사항의 정확성이나 완전성을 보증하지 않습니다. 저자는 언제든지 이 소프트웨어를 변경할 수 있으며 별도의 통지 없이 변경할 수 있습니다. <br />
                사용자가 직접 제작, 다운로드, 사용하는 타사 정보 데이터 및 플러그인으로 인해 발생하는 모든 저작권 문제 또는 분쟁에 대해 이 소프트웨어는 책임을 지지 않습니다.<br /><br />
                3. 계약에 따른 제약 및 제한: <br />
                (Apple CMS) 소스 코드에서 저작권 정보를 제거하는 것은 금지되어 있으며 상업용 라이선스 버전에서는 백엔드 인터페이스 및 프런트엔드 인터페이스의 관련 저작권 정보를 제거할 수 있습니다.</br>
                (Apple CMS) 전체 또는 일부를 기반으로 파생 버전, 수정 버전 또는 타사 버전을 개발하여 재배포하는 것은 금지되어 있습니다.</br></br>
                <strong>저작권 (c) 2020, Apple CMS, 모든 권리 보유</strong>.',

    'install/user_agreement_agree'=>'계약에 동의하고 시스템 설치',
    'install/environment_title'=>'운영 환경 검사',
    'install/environment_name'=>'환경 이름',
    'install/current_config'=>'현재 구성',
    'install/required_config'=>'필요한 구성',

    'install/dir_file'=>'디렉토리/파일',
    'install/required_popedom'=>'필요한 권한',
    'install/current_popedom'=>'현재 권한',

    'install/func_ext'=>'함수/확장',
    'install/type'=>'유형',
    'install/result'=>'결과',
    'install/back_step'=>'이전 단계로 돌아가기',
    'install/next_step'=>'다음 단계로 이동',
    'install/question'=>'자주 묻는 질문 해결 방법',
    'install/database_config'=>'데이터베이스 구성',

    'install/server_address'=>'서버 주소',
    'install/server_address_tip'=>'데이터베이스 서버 주소, 일반적으로 127.0.0.1',
    'install/database_port'=>'데이터베이스 포트',
    'install/database_port_tip'=>'시스템 데이터베이스 포트, 일반적으로 3306',
    'install/database_name'=>'데이터베이스 이름',
    'install/database_name_tip'=>'시스템 데이터베이스 이름, 문자를 포함해야 합니다.',
    'install/database_username'=>'데이터베이스 계정',
    'install/database_username_tip'=>'데이터베이스에 연결할 사용자 이름',
    'install/database_pass'=>'데이터베이스 비밀번호',
    'install/database_pass_tip'=>'데이터베이스에 연결할 비밀번호',
    'install/database_pre'=>'데이터베이스 접두사',
    'install/database_pre_tip'=>'기본값을 사용하는 것이 좋습니다. 데이터베이스 접두사에는 _가 있어야 합니다.',
    'install/overwrite_database'=>'데이터베이스 덮어쓰기',
    'install/overwrite'=>'덮어쓰기',
    'install/not_overwrite'=>'덮어쓰지 않음',
    'install/overwrite_tip'=>'기존 데이터를 유지하려면 덮어쓰지 않음을 선택하십시오.',
    'install/test_connect'=>'데이터베이스 연결 테스트',
    'install/test_connect_tip'=>'[데이터 연결 테스트]를 클릭한 다음 설치하십시오.',
    'install/other_config'=>'기타 설정',
    'install/admin_name'=>'관리자 계정',
    'install/admin_name_tip'=>'관리자 계정은 4자 이상이어야 합니다.',
    'install/admin_pass'=>'관리자 비밀번호',
    'install/admin_pass_tip'=>'비밀번호는 6자 이상이어야 합니다.',
    'install/init_data'=>'데이터 초기화',
    'install/create'=>'생성',
    'install/not_create'=>'생성하지 않음',
    'install/create_tip'=>'기본 카테고리 데이터를 생성할지 여부',
    'install/exec'=>'설치 실행',
    'install/submit_tip'=>'먼저 데이터 연결 테스트를 클릭하고 통과하십시오!',

    'install/environment_failed'=>'환경 검사를 통과하지 못했습니다. 다음 작업을 수행할 수 없습니다!',
    'install/init_err'=>'초기화 실패!',
    'install/write_read_err'=>'읽기/쓰기 권한이 없습니다!',
    'install/not_found'=>'존재하지 않음',
    'install/database_connect_err'=>'데이터베이스 연결 실패, 데이터베이스 구성을 확인하십시오!',
    'install/database_name_haved'=>'이 데이터베이스가 이미 있습니다. 직접 설치할 수 있습니다. 덮어쓰려면 데이터베이스 덮어쓰기를 선택하십시오!',
    'install/database_connect_ok'=>'데이터베이스 연결 성공',
    'install/access_denied'=>'불법 액세스',
    'install/please_test_connect'=>'먼저 데이터베이스 연결 테스트를 클릭하십시오!',
    'install/please_input_admin_name_pass'=>'관리자 계정과 비밀번호를 입력하십시오!',
    'install/sql_err'=>'테이블 구조 SQL 가져오기 실패, install.sql의 문이 올바른지 확인하십시오.',
    'install/init_data_err'=>'초기화 데이터 SQL 가져오기 실패, initdata.sql의 문이 올바른지 확인하십시오.',
    'install/admin_err'=>'관리자 계정 생성 실패',
    'install/is_ok'=>'시스템 설치 성공, Apple CMS를 사용하여 웹사이트를 구축해 주셔서 감사합니다.',
    'install/os'=>'운영 체제',
    'install/php'=>'PHP 버전',
    'install/gd'=>'GD 라이브러리',

    'install/not_limited'=>'제한 없음',
    'install/not_installed'=>'설치되지 않음',
    'install/read_and_write'=>'읽기/쓰기',
    'install/not_writable'=>'쓸 수 없음',
    'install/support'=>'지원',
    'install/not_support'=>'지원',
    'install/class'=>'클래스',
    'install/model'=>'모듈',
    'install/function'=>'함수',
    'install/config'=>'구성',


    'validate/require_name'=>'이름은 필수입니다.',
    'validate/require_type'=>'카테고리는 필수입니다.',
    'validate/require_content'=>'내용은 필수입니다.',
    'validate/require_nick'=>'닉네임은 필수입니다.',
    'validate/require_mid'=>'모델 ID는 필수입니다.',
    'validate/require_rid'=>'관련 ID는 필수입니다.',
    'validate/require_pass'=>'비밀번호는 필수입니다.',
    'validate/require_url'=>'URL은 필수입니다.',
    'validate/require_actor'=>'배우는 필수입니다.',
    'validate/require_user'=>'사용자는 필수입니다.',
    'validate/require_no'=>'카드 번호는 필수입니다.',
    'validate/require_name_min'=>'이름은 6자 이상이어야 합니다.',
    'validate/require_money'=>'금액은 필수입니다.',
    'validate/require_points'=>'포인트는 필수입니다.',
    'validate/require_bank_name'=>'은행 이름은 필수입니다.',
    'validate/require_payee_name'=>'수취인 이름은 필수입니다.',
    'validate/require_bank_no'=>'은행 계좌 번호는 필수입니다.',
    'validate/require_sourcecharset'=>'대상 URL 인코딩 유형은 필수입니다.',
    'validate/require_sourcetype'=>'현재 URL 유형은 필수입니다.',
    'validate/require_urlpage'=>'현재 URL은 필수입니다.',
    'validate/require_order_code'=>'주문 번호는 필수입니다.',
    'validate/require_order_price'=>'가격은 필수입니다.',
    'validate/require_order_points'=>'포인트는 필수입니다.',
    'validate/require_msg_to'=>'수신 주소는 필수입니다.',
    'validate/require_verify'=>'인증 코드는 필수입니다.',
    'validate/require_path'=>'경로는 필수입니다.',
    'validate/require_tpl'=>'템플릿은 필수입니다.',
    'validate/require_ip'=>'IP는 필수입니다.',
    'validate/require_time'=>'시간은 필수입니다.',
];