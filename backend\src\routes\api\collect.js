const express = require('express');
const router = express.Router();
const Video = require('../../models/Video');
const Category = require('../../models/Category');
const logger = require('../../utils/logger');

// 添加CORS支持
router.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');

  // 处理预检请求
  if (req.method === 'OPTIONS') {
    res.sendStatus(200);
    return;
  }

  next();
});

// 采集接口文档
router.get('/', (req, res) => {
  res.json({
    message: '91JSPG.COM 采集API v1.0 - 完全兼容苹果CMS',
    description: '100%兼容苹果CMS格式的采集接口',
    endpoints: {
      'GET /api/collect/vod': '视频采集接口（兼容苹果CMS）',
      'GET /api/collect/art': '文章采集接口（暂未实现）'
    },
    vod_endpoints: {
      'GET /api/collect/vod?ac=list': '获取视频列表',
      'GET /api/collect/vod?ac=detail': '获取视频详情',
      'GET /api/collect/vod?ac=videolist': '获取视频内容（兼容旧版）'
    },
    parameters: {
      ac: '操作类型 (list|detail|videolist)',
      t: '分类ID',
      pg: '页码 (默认1)',
      wd: '搜索关键字',
      h: '几小时内的数据',
      ids: '视频ID，多个用逗号分隔',
      at: '输出格式 (json|xml，默认json)',
      pagesize: '每页数量 (默认20)',
      year: '年份筛选',
      isend: '是否完结 (0|1)',
      from: '播放来源筛选',
      sort_direction: '排序方向 (asc|desc)'
    },
    examples: {
      list: '/api/collect/vod?ac=list&t=1&pg=1',
      detail: '/api/collect/vod?ac=detail&ids=1,2,3',
      search: '/api/collect/vod?ac=list&wd=关键字',
      recent: '/api/collect/vod?ac=detail&h=24',
      xml: '/api/collect/vod?ac=list&at=xml'
    },
    version: '1.0.0',
    status: 'active',
    compatible: 'MacCMS v10 - 100% Compatible'
  });
});

// 视频采集接口 - 完全兼容苹果CMS格式
router.get('/vod', async (req, res) => {
  try {
    // 解析所有苹果CMS支持的参数
    const {
      ac,                    // 操作类型
      t,                     // 分类ID
      pg = 1,               // 页码
      wd,                   // 搜索关键字
      h,                    // 几小时内的数据
      ids,                  // 视频ID，多个用逗号分隔
      at = 'json',          // 输出格式
      pagesize = 20,        // 每页数量（苹果CMS使用pagesize而不是limit）
      year,                 // 年份筛选
      isend,                // 是否完结
      from,                 // 播放来源筛选
      sort_direction = 'desc' // 排序方向
    } = req.query;

    // 记录请求日志
    logger.api('COLLECT_VOD_REQUEST', { ac, t, pg, wd, h, ids, at, pagesize });

    // 根据操作类型处理请求
    switch (ac) {
      case 'list':
        await handleVideoList(req, res, { t, pg, wd, h, at, pagesize, year, isend, from, sort_direction });
        break;
      case 'detail':
      case 'videolist':
        await handleVideoDetail(req, res, { ids, t, pg, wd, h, at, pagesize, year, isend, from, sort_direction });
        break;
      default:
        // 返回苹果CMS格式的错误响应
        res.json({
          code: 0,
          msg: '参数错误：ac参数必须为list、detail或videolist',
          page: 1,
          pagecount: 0,
          limit: '0',
          total: 0,
          list: [],
          class: []
        });
    }
  } catch (error) {
    logger.error('采集接口错误:', error);
    // 返回苹果CMS格式的错误响应
    res.json({
      code: 0,
      msg: '服务器内部错误',
      page: 1,
      pagecount: 0,
      limit: '0',
      total: 0,
      list: [],
      class: []
    });
  }
});

// 处理视频列表请求 - 完全兼容苹果CMS格式
async function handleVideoList(req, res, { t, pg, wd, h, at, pagesize, year, isend, from, sort_direction }) {
  try {
    const page = parseInt(pg) || 1;
    const limit = parseInt(pagesize) || 20;

    const options = {
      page,
      limit,
      status: 'active'
    };

    // 分类筛选
    if (t) {
      options.category = parseInt(t);
    }

    // 搜索筛选
    if (wd) {
      options.search = wd;
    }

    // 时间筛选（几小时内的数据）
    if (h) {
      const hours = parseInt(h);
      const timeAgo = new Date(Date.now() - hours * 60 * 60 * 1000);
      options.timeFilter = timeAgo;
    }

    // 年份筛选
    if (year) {
      options.year = year;
    }

    // 是否完结筛选
    if (isend !== undefined) {
      options.isend = parseInt(isend);
    }

    // 播放来源筛选
    if (from) {
      options.from = from;
    }

    // 排序方向
    options.sortDirection = sort_direction || 'desc';

    const result = await Video.findAll(options);
    const categories = await Category.findAll({ status: 'active' });

    // 转换为完全兼容苹果CMS的格式
    const response = {
      code: 1,
      msg: '数据列表',
      page: result.pagination.page,
      pagecount: result.pagination.totalPages,
      limit: result.pagination.limit.toString(),
      total: result.pagination.total,
      list: result.videos.map(video => ({
        vod_id: parseInt(video.id) || 0,
        vod_name: sanitizeString(video.title) || '',
        type_id: parseInt(video.categoryId) || 1,
        type_name: sanitizeString(video.categoryName) || '其他',
        vod_en: generateSlug(video.title || ''),
        vod_time: formatMacCMSDateTime(video.createdAt),
        vod_remarks: sanitizeString(getVideoRemarks(video)) || '',
        vod_play_from: getPlayFrom(video, from),
        vod_pic: sanitizeString(video.coverUrl) || '',
        vod_area: sanitizeString(video.area) || '大陆',
        vod_lang: sanitizeString(video.language) || '国语',
        vod_year: getVideoYear(video.createdAt),
        vod_serial: getVideoSerial(video),
        vod_status: video.status === 'active' ? 1 : 0,
        vod_hits: parseInt(video.views) || 0,
        vod_score: parseFloat(video.rating) || 0,
        vod_isend: getVideoIsEnd(video)
      })),
      class: categories.map(cat => ({
        type_id: parseInt(cat.id) || 0,
        type_pid: parseInt(cat.parentId) || 0,
        type_name: sanitizeString(cat.name) || '其他'
      }))
    };

    // 根据格式返回数据 - 完全兼容苹果CMS
    if (at === 'xml') {
      res.set('Content-Type', 'application/xml; charset=utf-8');
      res.send(convertToMacCMSXML(response, 'list'));
    } else {
      res.set('Content-Type', 'application/json; charset=utf-8');
      res.set('Cache-Control', 'no-cache');

      // 直接返回苹果CMS格式的JSON，不做额外处理
      res.json(response);
    }

  } catch (error) {
    logger.error('获取视频列表失败:', error);
    res.status(500).json({
      code: 0,
      msg: '获取数据失败',
      error: error.message
    });
  }
}

// 处理视频详情请求 - 完全兼容苹果CMS格式
async function handleVideoDetail(req, res, { ids, t, pg, wd, h, at, pagesize, year, isend, from, sort_direction }) {
  try {
    let videos = [];

    if (ids) {
      // 根据ID获取视频
      const idArray = ids.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));
      for (const id of idArray) {
        const video = await Video.findById(id);
        if (video && video.status === 'active') {
          videos.push(video);
        }
      }
    } else {
      // 根据其他条件获取视频
      const page = parseInt(pg) || 1;
      const limit = parseInt(pagesize) || 20;

      const options = {
        page,
        limit,
        status: 'active'
      };

      if (t) {
        options.category = parseInt(t);
      }

      if (wd) {
        options.search = wd;
      }

      if (h) {
        const hours = parseInt(h);
        const timeAgo = new Date(Date.now() - hours * 60 * 60 * 1000);
        options.timeFilter = timeAgo;
      }

      if (year) {
        options.year = year;
      }

      if (isend !== undefined) {
        options.isend = parseInt(isend);
      }

      if (from) {
        options.from = from;
      }

      options.sortDirection = sort_direction || 'desc';

      const result = await Video.findAll(options);
      videos = result.videos;
    }

    // 转换为完全兼容苹果CMS的详情格式
    const response = {
      code: 1,
      msg: '数据列表',
      page: 1,
      pagecount: 1,
      limit: videos.length.toString(),
      total: videos.length,
      list: videos.map(video => ({
        vod_id: parseInt(video.id) || 0,
        vod_name: sanitizeString(video.title) || '',
        type_id: parseInt(video.categoryId) || 1,
        type_name: sanitizeString(video.categoryName) || '其他',
        vod_en: generateSlug(video.title || ''),
        vod_time: formatMacCMSDateTime(video.createdAt),
        vod_remarks: sanitizeString(getVideoRemarks(video)) || '',
        vod_play_from: getPlayFrom(video, from),
        vod_play_url: getPlayUrl(video, from),
        vod_pic: sanitizeString(video.coverUrl) || '',
        vod_area: sanitizeString(video.area) || '大陆',
        vod_lang: sanitizeString(video.language) || '国语',
        vod_year: getVideoYear(video.createdAt),
        vod_serial: getVideoSerial(video),
        vod_actor: sanitizeString(extractActors(video.tags)) || '',
        vod_director: sanitizeString(extractDirector(video.tags)) || '',
        vod_content: sanitizeString(video.description) || '',
        vod_status: video.status === 'active' ? 1 : 0,
        vod_hits: parseInt(video.views) || 0,
        vod_score: parseFloat(video.rating) || 0,
        vod_duration: sanitizeString(video.duration) || '',
        vod_tag: sanitizeTags(video.tags),
        vod_class: sanitizeString(video.categoryName) || '其他',
        vod_isend: getVideoIsEnd(video),
        vod_douban_id: parseInt(video.doubanId) || 0,
        vod_douban_score: parseFloat(video.doubanScore) || 0,
        // 添加苹果CMS采集器期望的dl字段
        dl: generateDlField(video, from)
      }))
    };

    // 根据格式返回数据 - 完全兼容苹果CMS
    if (at === 'xml') {
      res.set('Content-Type', 'application/xml; charset=utf-8');
      res.send(convertToMacCMSXML(response, 'detail'));
    } else {
      res.set('Content-Type', 'application/json; charset=utf-8');
      res.set('Cache-Control', 'no-cache');

      // 直接返回苹果CMS格式的JSON
      res.json(response);
    }

  } catch (error) {
    logger.error('获取视频详情失败:', error);
    res.status(500).json({
      code: 0,
      msg: '获取数据失败',
      error: error.message
    });
  }
}

// ==================== 苹果CMS兼容辅助函数 ====================

// 生成英文slug
function generateSlug(title) {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9\u4e00-\u9fff]/g, '')
    .substring(0, 20);
}

// 获取视频备注
function getVideoRemarks(video) {
  if (video.status !== 'active') {
    return '待审核';
  }

  // 根据视频质量或其他属性生成备注
  if (video.rating && video.rating >= 9) {
    return '超清';
  } else if (video.rating && video.rating >= 7) {
    return '高清';
  } else {
    return '标清';
  }
}

// 获取播放来源 - 兼容苹果CMS格式
function getPlayFrom(video, from) {
  if (from) {
    return from;
  }

  // 如果视频有多个播放源，用$$$分隔
  if (video.playSources && Array.isArray(video.playSources)) {
    return video.playSources.map(source => source.name || 'default').join('$$$');
  }

  return 'default';
}

// 获取播放地址 - 兼容苹果CMS格式
function getPlayUrl(video, from) {
  if (!video.videoUrl) return '';

  // 如果有多个播放源，按苹果CMS格式返回
  if (video.playSources && Array.isArray(video.playSources)) {
    return video.playSources.map(source => {
      const episodes = source.episodes || [{ name: '正片', url: video.videoUrl }];
      return episodes.map(ep => `${ep.name}$${ep.url}`).join('#');
    }).join('$$$');
  }

  // 单个播放源
  return `正片$${video.videoUrl}`;
}

// 获取视频集数状态
function getVideoSerial(video) {
  if (video.totalEpisodes && video.currentEpisode) {
    return `${video.currentEpisode}/${video.totalEpisodes}`;
  }
  return '1';
}

// 获取是否完结状态
function getVideoIsEnd(video) {
  if (video.isCompleted !== undefined) {
    return video.isCompleted ? 1 : 0;
  }

  if (video.totalEpisodes && video.currentEpisode) {
    return video.currentEpisode >= video.totalEpisodes ? 1 : 0;
  }

  return 1; // 默认完结
}

// 从标签中提取演员信息
function extractActors(tags) {
  if (!tags) return '';

  try {
    const tagArray = Array.isArray(tags) ? tags : JSON.parse(tags || '[]');
    // 查找演员相关标签
    const actors = tagArray.filter(tag =>
      typeof tag === 'string' && (
        tag.includes('主演') ||
        tag.includes('演员') ||
        tag.includes('Actor')
      )
    );
    return actors.join(',');
  } catch (e) {
    return '';
  }
}

// 从标签中提取导演信息
function extractDirector(tags) {
  if (!tags) return '';

  try {
    const tagArray = Array.isArray(tags) ? tags : JSON.parse(tags || '[]');
    // 查找导演相关标签
    const directors = tagArray.filter(tag =>
      typeof tag === 'string' && (
        tag.includes('导演') ||
        tag.includes('Director')
      )
    );
    return directors.join(',');
  } catch (e) {
    return '';
  }
}

// 清理字符串，移除可能导致JSON解析错误的字符
function sanitizeString(str) {
  if (!str) return '';

  return String(str)
    .replace(/[\u0000-\u001F\u007F-\u009F]/g, '') // 移除控制字符
    .replace(/[\u2028\u2029]/g, '') // 移除行分隔符和段分隔符
    .replace(/\\/g, '\\\\') // 转义反斜杠
    .replace(/"/g, '\\"') // 转义双引号
    .trim();
}

// 清理标签数据
function sanitizeTags(tags) {
  if (!tags) return '';

  try {
    if (Array.isArray(tags)) {
      return tags.map(tag => sanitizeString(tag)).join(',');
    } else if (typeof tags === 'string') {
      const parsed = JSON.parse(tags);
      if (Array.isArray(parsed)) {
        return parsed.map(tag => sanitizeString(tag)).join(',');
      }
      return sanitizeString(tags);
    }
    return '';
  } catch (e) {
    return sanitizeString(String(tags || ''));
  }
}

// 获取视频年份
function getVideoYear(createdAt) {
  if (!createdAt) return new Date().getFullYear().toString();

  try {
    const year = new Date(createdAt).getFullYear();
    return isNaN(year) ? new Date().getFullYear().toString() : year.toString();
  } catch (e) {
    return new Date().getFullYear().toString();
  }
}

// 格式化日期时间 - 苹果CMS兼容格式
function formatMacCMSDateTime(date) {
  if (!date) return '';

  try {
    const d = new Date(date);
    if (isNaN(d.getTime())) return '';

    // 苹果CMS期望的格式：YYYY-MM-DD HH:mm:ss
    return d.toISOString().slice(0, 19).replace('T', ' ');
  } catch (e) {
    return '';
  }
}

// 格式化日期时间 - 标准格式
function formatDateTime(date) {
  if (!date) return '';
  const d = new Date(date);
  return d.toISOString().slice(0, 19).replace('T', ' ');
}

// 转换为XML格式（简化版）
function convertToXML(data, type) {
  let xml = '<?xml version="1.0" encoding="utf-8"?>\n';
  xml += `<rss version="2.0">\n`;
  xml += `<channel>\n`;
  xml += `<title>91JSPG.COM</title>\n`;
  xml += `<link>https://91jspg.com</link>\n`;
  xml += `<description>影视采集接口</description>\n`;
  xml += `<lastBuildDate>${formatDateTime(new Date())}</lastBuildDate>\n`;
  xml += `<generator>91JSPG.COM</generator>\n`;

  if (Array.isArray(data.list)) {
    data.list.forEach(item => {
      if (type === 'detail') {
        xml += `<video>\n`;
        xml += `<id>${item.vod_id}</id>\n`;
        xml += `<name><![CDATA[${item.vod_name}]]></name>\n`;
        xml += `<type><![CDATA[${item.type_name}]]></type>\n`;
        xml += `<dt>${item.vod_time}</dt>\n`;
        xml += `<last>${item.vod_time}</last>\n`;
        xml += `<lang><![CDATA[${item.vod_lang || '国语'}]]></lang>\n`;
        xml += `<area><![CDATA[${item.vod_area || '大陆'}]]></area>\n`;
        xml += `<year>${item.vod_year || ''}</year>\n`;
        xml += `<pic><![CDATA[${item.vod_pic || ''}]]></pic>\n`;
        xml += `<actor><![CDATA[${item.vod_actor || ''}]]></actor>\n`;
        xml += `<director><![CDATA[${item.vod_director || ''}]]></director>\n`;
        xml += `<des><![CDATA[${item.vod_content || ''}]]></des>\n`;
        xml += `<dl>\n`;
        xml += `  <dd flag="${item.vod_play_from}"><![CDATA[${item.vod_play_url}]]></dd>\n`;
        xml += `</dl>\n`;
        xml += `</video>\n`;
      } else {
        // list 模式简化版本
        xml += `<video>\n`;
        xml += `<id>${item.vod_id}</id>\n`;
        xml += `<name><![CDATA[${item.vod_name}]]></name>\n`;
        xml += `<type><![CDATA[${item.type_name}]]></type>\n`;
        xml += `<last>${item.vod_time}</last>\n`;
        xml += `<dt>${item.vod_time}</dt>\n`;
        xml += `<remarks><![CDATA[${item.vod_remarks}]]></remarks>\n`;
        xml += `</video>\n`;
      }
    });
  }

  xml += `</channel>\n`;
  xml += `</rss>`;
  return xml;
}

// 转换为苹果CMS兼容的XML格式
function convertToMacCMSXML(data, type) {
  let xml = '<?xml version="1.0" encoding="utf-8"?>\n';
  xml += `<rss version="5.1">\n`;

  // 添加分页信息 - 苹果CMS格式
  xml += `<list page="${data.page}" pagecount="${data.pagecount}" pagesize="${data.limit}" recordcount="${data.total}">\n`;

  if (Array.isArray(data.list)) {
    data.list.forEach(item => {
      xml += `<video>\n`;
      xml += `<last>${item.vod_time}</last>\n`;
      xml += `<id>${item.vod_id}</id>\n`;
      xml += `<tid>${item.type_id}</tid>\n`;
      xml += `<n><![CDATA[${item.vod_name}]]></n>\n`;
      xml += `<type>${item.type_name}</type>\n`;

      if (type === 'detail') {
        // 详情模式 - 包含完整信息
        xml += `<pic>${item.vod_pic || ''}</pic>\n`;
        xml += `<lang>${item.vod_lang || '国语'}</lang>\n`;
        xml += `<area>${item.vod_area || '大陆'}</area>\n`;
        xml += `<year>${item.vod_year || ''}</year>\n`;
        xml += `<state>${item.vod_serial || ''}</state>\n`;
        xml += `<note><![CDATA[${item.vod_remarks || ''}]]></note>\n`;
        xml += `<actor><![CDATA[${item.vod_actor || ''}]]></actor>\n`;
        xml += `<director><![CDATA[${item.vod_director || ''}]]></director>\n`;
        xml += `<dl>\n`;

        // 处理播放地址 - 苹果CMS格式
        const playFroms = (item.vod_play_from || 'default').split('$$$');
        const playUrls = (item.vod_play_url || '').split('$$$');

        playFroms.forEach((from, index) => {
          const url = playUrls[index] || '';
          xml += `<dd flag="${from}"><![CDATA[${url}]]></dd>\n`;
        });

        xml += `</dl>\n`;
        xml += `<des><![CDATA[${item.vod_content || ''}]]></des>\n`;
      } else {
        // 列表模式 - 简化信息
        xml += `<dt>${item.vod_play_from || 'default'}</dt>\n`;
        xml += `<note><![CDATA[${item.vod_remarks || ''}]]></note>\n`;
      }

      xml += `</video>\n`;
    });
  }

  xml += `</list>\n`;

  // 添加分类信息（仅在列表模式）
  if (type !== 'detail' && data.class && Array.isArray(data.class)) {
    xml += `<class>\n`;
    data.class.forEach(cat => {
      xml += `<ty id="${cat.type_id}">${cat.type_name}</ty>\n`;
    });
    xml += `</class>\n`;
  }

  xml += `</rss>`;
  return xml;
}

module.exports = router;
