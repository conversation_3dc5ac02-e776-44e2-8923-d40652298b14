const express = require('express');
const router = express.Router();
const Video = require('../../models/Video');
const Category = require('../../models/Category');
const logger = require('../../utils/logger');

// 添加CORS支持
router.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');

  // 处理预检请求
  if (req.method === 'OPTIONS') {
    res.sendStatus(200);
    return;
  }

  next();
});

// 移除API限流，允许无限制访问
// 采集接口不应该有频率限制

// 采集接口文档
router.get('/', (req, res) => {
  res.json({
    message: '91JSPG.COM 采集API v1.0',
    description: '兼容苹果CMS格式的采集接口',
    endpoints: {
      'GET /api/collect/vod': '视频采集接口',
      'GET /api/collect/art': '文章采集接口（暂未实现）'
    },
    vod_endpoints: {
      'GET /api/collect/vod?ac=list': '获取视频列表',
      'GET /api/collect/vod?ac=detail': '获取视频详情',
      'GET /api/collect/vod?ac=videolist': '获取视频内容（兼容旧版）'
    },
    parameters: {
      ac: '操作类型 (list|detail|videolist)',
      t: '分类ID',
      pg: '页码 (默认1)',
      wd: '搜索关键字',
      h: '几小时内的数据',
      ids: '视频ID，多个用逗号分隔',
      at: '输出格式 (json|xml，默认json)',
      limit: '每页数量 (默认20)',
      type: '类型 (默认1)',
      mid: '模块ID (默认1)',
      opt: '选项 (默认0)',
      filter: '过滤 (默认0)',
      filter_from: '过滤来源',
      param: '额外参数'
    },
    examples: {
      list: '/api/collect/vod?ac=list&t=1&pg=1',
      detail: '/api/collect/vod?ac=detail&ids=1,2,3',
      search: '/api/collect/vod?ac=list&wd=关键字',
      recent: '/api/collect/vod?ac=detail&h=24'
    },
    version: '1.0.0',
    status: 'active',
    compatible: 'MacCMS v10'
  });
});

// 视频采集接口
router.get('/vod', async (req, res) => {
  try {
    const {
      ac,
      t,
      pg = 1,
      wd,
      h,
      ids,
      at = 'json',
      limit = 20,
      type = 1,
      mid = 1,
      opt = 0,
      filter = 0,
      filter_from = '',
      param = ''
    } = req.query;

    // 根据操作类型处理请求
    switch (ac) {
      case 'list':
        await handleVideoList(req, res, { t, pg, wd, h, at, limit, type, mid, opt, filter, filter_from, param });
        break;
      case 'detail':
      case 'videolist':
        await handleVideoDetail(req, res, { ids, t, pg, h, at, limit, type, mid, opt, filter, filter_from, param });
        break;
      default:
        res.status(400).json({
          code: 0,
          msg: '参数错误：ac参数必须为list、detail或videolist',
          error: 'Invalid ac parameter'
        });
    }
  } catch (error) {
    logger.error('采集接口错误:', error);
    res.status(500).json({
      code: 0,
      msg: '服务器内部错误',
      error: error.message
    });
  }
});

// 处理视频列表请求
async function handleVideoList(req, res, { t, pg, wd, h, at, limit: requestLimit }) {
  try {
    const page = parseInt(pg) || 1;
    const limit = parseInt(requestLimit) || 20; // 支持自定义每页数量，默认20条
    
    const options = {
      page,
      limit,
      status: 'active'
    };

    // 分类筛选
    if (t) {
      options.category = parseInt(t);
    }

    // 搜索筛选
    if (wd) {
      options.search = wd;
    }

    // 时间筛选（几小时内的数据）
    if (h) {
      const hours = parseInt(h);
      const timeAgo = new Date(Date.now() - hours * 60 * 60 * 1000);
      options.timeFilter = timeAgo;
    }

    const result = await Video.findAll(options);
    const categories = await Category.findAll({ status: 'active' });

    // 转换为苹果CMS格式
    const response = {
      code: 1,
      msg: '数据列表',
      page: result.pagination.page,
      pagecount: result.pagination.totalPages,
      limit: result.pagination.limit.toString(),
      total: result.pagination.total,
      list: result.videos.map(video => ({
        vod_id: parseInt(video.id) || 0,
        vod_name: sanitizeString(video.title) || '',
        type_id: parseInt(video.categoryId) || 1,
        type_name: sanitizeString(video.categoryName) || '其他',
        vod_en: generateSlug(video.title || ''),
        vod_time: formatDateTime(video.createdAt) || '',
        vod_remarks: sanitizeString(getVideoRemarks(video)) || '',
        vod_play_from: 'default',
        vod_pic: sanitizeString(video.coverUrl) || '',
        vod_area: '大陆',
        vod_lang: '国语',
        vod_year: getVideoYear(video.createdAt),
        vod_serial: '0',
        vod_status: video.status === 'active' ? 1 : 0,
        vod_hits: parseInt(video.views) || 0,
        vod_score: parseFloat(video.rating) || 0
      })),
      class: categories.map(cat => ({
        type_id: parseInt(cat.id) || 0,
        type_pid: 0,
        type_name: sanitizeString(cat.name) || '其他'
      }))
    };

    // 根据格式返回数据
    if (at === 'xml') {
      res.set('Content-Type', 'application/xml; charset=utf-8');
      res.send(convertToXML(response, 'list'));
    } else {
      res.set('Content-Type', 'application/json; charset=utf-8');
      res.set('Cache-Control', 'no-cache');

      // 验证JSON格式并确保兼容性
      try {
        // 创建严格的JSON字符串，确保数字类型正确
        const cleanResponse = {
          code: Number(response.code),
          msg: String(response.msg),
          page: Number(response.page),
          pagecount: Number(response.pagecount),
          limit: String(response.limit),
          total: Number(response.total),
          list: response.list.map(item => ({
            vod_id: Number(item.vod_id),
            vod_name: String(item.vod_name || ''),
            type_id: Number(item.type_id),
            type_name: String(item.type_name || ''),
            vod_en: String(item.vod_en || ''),
            vod_time: String(item.vod_time || ''),
            vod_remarks: String(item.vod_remarks || ''),
            vod_play_from: String(item.vod_play_from || 'default')
          })),
          class: response.class.map(cat => ({
            type_id: Number(cat.type_id),
            type_pid: Number(cat.type_pid),
            type_name: String(cat.type_name || '')
          }))
        };

        const jsonString = JSON.stringify(cleanResponse);
        JSON.parse(jsonString); // 验证JSON是否有效
        res.send(jsonString);
      } catch (jsonError) {
        logger.error('JSON序列化错误:', jsonError);
        res.status(500).json({
          code: 0,
          msg: '数据格式错误',
          error: 'JSON serialization failed'
        });
      }
    }

  } catch (error) {
    logger.error('获取视频列表失败:', error);
    res.status(500).json({
      code: 0,
      msg: '获取数据失败',
      error: error.message
    });
  }
}

// 处理视频详情请求
async function handleVideoDetail(req, res, { ids, t, pg, h, at, limit: requestLimit }) {
  try {
    let videos = [];

    if (ids) {
      // 根据ID获取视频
      const idArray = ids.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));
      for (const id of idArray) {
        const video = await Video.findById(id);
        if (video && video.status === 'active') {
          videos.push(video);
        }
      }
    } else {
      // 根据其他条件获取视频
      const page = parseInt(pg) || 1;
      const limit = parseInt(requestLimit) || 20;
      
      const options = {
        page,
        limit,
        status: 'active'
      };

      if (t) {
        options.category = parseInt(t);
      }

      if (h) {
        const hours = parseInt(h);
        const timeAgo = new Date(Date.now() - hours * 60 * 60 * 1000);
        options.timeFilter = timeAgo;
      }

      const result = await Video.findAll(options);
      videos = result.videos;
    }

    // 转换为苹果CMS详情格式
    const response = {
      code: 1,
      msg: '数据列表',
      page: 1,
      pagecount: 1,
      limit: videos.length.toString(),
      total: videos.length,
      list: videos.map(video => ({
        vod_id: parseInt(video.id) || 0,
        vod_name: sanitizeString(video.title) || '',
        type_id: parseInt(video.categoryId) || 1,
        type_name: sanitizeString(video.categoryName) || '其他',
        vod_en: generateSlug(video.title || ''),
        vod_time: formatDateTime(video.createdAt) || '',
        vod_remarks: sanitizeString(getVideoRemarks(video)) || '',
        vod_play_from: 'default',
        vod_pic: sanitizeString(video.coverUrl) || '',
        vod_area: '大陆',
        vod_lang: '国语',
        vod_year: getVideoYear(video.createdAt),
        vod_serial: '0',
        vod_actor: sanitizeString(extractActors(video.tags)) || '',
        vod_director: '',
        vod_content: sanitizeString(video.description) || '',
        vod_play_url: video.videoUrl ? `正片$${sanitizeString(video.videoUrl)}` : '',
        vod_status: video.status === 'active' ? 1 : 0,
        vod_hits: parseInt(video.views) || 0,
        vod_score: parseFloat(video.rating) || 0,
        vod_duration: sanitizeString(video.duration) || '',
        vod_tag: sanitizeTags(video.tags),
        vod_class: sanitizeString(video.categoryName) || '其他'
      }))
    };

    // 根据格式返回数据
    if (at === 'xml') {
      res.set('Content-Type', 'application/xml; charset=utf-8');
      res.send(convertToXML(response, 'detail'));
    } else {
      res.set('Content-Type', 'application/json; charset=utf-8');
      res.set('Cache-Control', 'no-cache');

      // 验证JSON格式并确保兼容性
      try {
        // 创建严格的JSON字符串，确保数字类型正确
        const cleanResponse = {
          code: Number(response.code),
          msg: String(response.msg),
          page: Number(response.page),
          pagecount: Number(response.pagecount),
          limit: String(response.limit),
          total: Number(response.total),
          list: response.list.map(item => ({
            vod_id: Number(item.vod_id),
            vod_name: String(item.vod_name || ''),
            type_id: Number(item.type_id),
            type_name: String(item.type_name || ''),
            vod_en: String(item.vod_en || ''),
            vod_time: String(item.vod_time || ''),
            vod_remarks: String(item.vod_remarks || ''),
            vod_play_from: String(item.vod_play_from || 'default'),
            vod_pic: String(item.vod_pic || ''),
            vod_area: String(item.vod_area || ''),
            vod_lang: String(item.vod_lang || ''),
            vod_year: String(item.vod_year || ''),
            vod_serial: String(item.vod_serial || ''),
            vod_actor: String(item.vod_actor || ''),
            vod_director: String(item.vod_director || ''),
            vod_content: String(item.vod_content || ''),
            vod_play_url: String(item.vod_play_url || ''),
            vod_status: Number(item.vod_status),
            vod_hits: Number(item.vod_hits),
            vod_score: Number(item.vod_score),
            vod_duration: String(item.vod_duration || ''),
            vod_tag: String(item.vod_tag || ''),
            vod_class: String(item.vod_class || '')
          }))
        };

        const jsonString = JSON.stringify(cleanResponse);
        JSON.parse(jsonString); // 验证JSON是否有效
        res.send(jsonString);
      } catch (jsonError) {
        logger.error('JSON序列化错误:', jsonError);
        res.status(500).json({
          code: 0,
          msg: '数据格式错误',
          error: 'JSON serialization failed'
        });
      }
    }

  } catch (error) {
    logger.error('获取视频详情失败:', error);
    res.status(500).json({
      code: 0,
      msg: '获取数据失败',
      error: error.message
    });
  }
}

// 生成英文slug
function generateSlug(title) {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9\u4e00-\u9fff]/g, '')
    .substring(0, 20);
}

// 获取视频备注
function getVideoRemarks(video) {
  if (video.status !== 'active') {
    return '待审核';
  }

  // 根据视频质量或其他属性生成备注
  if (video.rating && video.rating >= 9) {
    return '超清';
  } else if (video.rating && video.rating >= 7) {
    return '高清';
  } else {
    return '标清';
  }
}

// 从标签中提取演员信息
function extractActors(tags) {
  if (!tags) return '';

  try {
    const tagArray = Array.isArray(tags) ? tags : JSON.parse(tags || '[]');
    // 这里可以根据实际需求来提取演员信息
    // 暂时返回空字符串，可以根据标签规则来提取
    return '';
  } catch (e) {
    return '';
  }
}

// 清理字符串，移除可能导致JSON解析错误的字符
function sanitizeString(str) {
  if (!str) return '';

  return String(str)
    .replace(/[\u0000-\u001F\u007F-\u009F]/g, '') // 移除控制字符
    .replace(/[\u2028\u2029]/g, '') // 移除行分隔符和段分隔符
    .replace(/\\/g, '\\\\') // 转义反斜杠
    .replace(/"/g, '\\"') // 转义双引号
    .trim();
}

// 清理标签数据
function sanitizeTags(tags) {
  if (!tags) return '';

  try {
    if (Array.isArray(tags)) {
      return tags.map(tag => sanitizeString(tag)).join(',');
    } else if (typeof tags === 'string') {
      const parsed = JSON.parse(tags);
      if (Array.isArray(parsed)) {
        return parsed.map(tag => sanitizeString(tag)).join(',');
      }
      return sanitizeString(tags);
    }
    return '';
  } catch (e) {
    return sanitizeString(String(tags || ''));
  }
}

// 获取视频年份
function getVideoYear(createdAt) {
  if (!createdAt) return new Date().getFullYear().toString();

  try {
    const year = new Date(createdAt).getFullYear();
    return isNaN(year) ? new Date().getFullYear().toString() : year.toString();
  } catch (e) {
    return new Date().getFullYear().toString();
  }
}

// 格式化日期时间
function formatDateTime(date) {
  if (!date) return '';
  const d = new Date(date);
  return d.toISOString().slice(0, 19).replace('T', ' ');
}

// 转换为XML格式（简化版）
function convertToXML(data, type) {
  let xml = '<?xml version="1.0" encoding="utf-8"?>\n';
  xml += `<rss version="2.0">\n`;
  xml += `<channel>\n`;
  xml += `<title>91JSPG.COM</title>\n`;
  xml += `<link>https://91jspg.com</link>\n`;
  xml += `<description>影视采集接口</description>\n`;
  xml += `<lastBuildDate>${formatDateTime(new Date())}</lastBuildDate>\n`;
  xml += `<generator>91JSPG.COM</generator>\n`;

  if (Array.isArray(data.list)) {
    data.list.forEach(item => {
      if (type === 'detail') {
        xml += `<video>\n`;
        xml += `<id>${item.vod_id}</id>\n`;
        xml += `<name><![CDATA[${item.vod_name}]]></name>\n`;
        xml += `<type><![CDATA[${item.type_name}]]></type>\n`;
        xml += `<dt>${item.vod_time}</dt>\n`;
        xml += `<last>${item.vod_time}</last>\n`;
        xml += `<lang><![CDATA[${item.vod_lang || '国语'}]]></lang>\n`;
        xml += `<area><![CDATA[${item.vod_area || '大陆'}]]></area>\n`;
        xml += `<year>${item.vod_year || ''}</year>\n`;
        xml += `<pic><![CDATA[${item.vod_pic || ''}]]></pic>\n`;
        xml += `<actor><![CDATA[${item.vod_actor || ''}]]></actor>\n`;
        xml += `<director><![CDATA[${item.vod_director || ''}]]></director>\n`;
        xml += `<des><![CDATA[${item.vod_content || ''}]]></des>\n`;
        xml += `<dl>\n`;
        xml += `  <dd flag="${item.vod_play_from}"><![CDATA[${item.vod_play_url}]]></dd>\n`;
        xml += `</dl>\n`;
        xml += `</video>\n`;
      } else {
        // list 模式简化版本
        xml += `<video>\n`;
        xml += `<id>${item.vod_id}</id>\n`;
        xml += `<name><![CDATA[${item.vod_name}]]></name>\n`;
        xml += `<type><![CDATA[${item.type_name}]]></type>\n`;
        xml += `<last>${item.vod_time}</last>\n`;
        xml += `<dt>${item.vod_time}</dt>\n`;
        xml += `<remarks><![CDATA[${item.vod_remarks}]]></remarks>\n`;
        xml += `</video>\n`;
      }
    });
  }

  xml += `</channel>\n`;
  xml += `</rss>`;
  return xml;
}

module.exports = router;
