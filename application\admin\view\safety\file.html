{include file="../../../application/admin/view/public/head" /}

<div class="page-container">
    <form class="layui-form layui-form-pane" method="get" action="">
        <input name="ck" value="1" type="hidden">
        <div class="layui-tab">
            <ul class="layui-tab-title">
                <li class="layui-this">{:lang('admin/safety/file_inspect')}</li>
            </ul>
            <div class="layui-tab-content">
                <div class="layui-tab-item layui-show">
                    <div class="layui-input-block" >
                        <blockquote class="layui-elem-quote layui-quote-nm">
                            {:lang('admin/safety/file_inspect_tip')}
                        </blockquote>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-form-item center">
            <div class="layui-input-block">
                <input type="checkbox" lay-skin="primary" name="ft[]" value="1" title="{:lang('admin/safety/file_msg3')}" checked>
                <input type="checkbox" lay-skin="primary" name="ft[]" value="2" title="{:lang('admin/safety/file_msg4')}" checked>
                <button type="submit" class="layui-btn" >{:lang('admin/safety/exec')}</button>
            </div>
        </div>
    </form>
</div>

{include file="../../../application/admin/view/public/foot" /}
<script type="text/javascript">
    $(function(){
       $('.layui-btn').click(function(){
           layer.msg("{:lang('wait_submit')}");
       });
    });
</script>

</body>
</html>