{include file="../../../application/admin/view/public/head" /}

<div class="page-container">
        <form class="layui-form layui-form-pane" action="">
            <input type="hidden" name="__token__" value="{$Request.token}" />
            <div class="layui-tab" lay-filter="tb1">
                <ul class="layui-tab-title">
                    <li class="layui-this" lay-id="configseo_1">{:lang('admin/system/configseo/vod_index')}SEO</li>
                    <li lay-id="configseo_2">{:lang('admin/system/configseo/art_index')}SEO</li>
                    <li lay-id="configseo_3">{:lang('admin/system/configseo/actor_index')}SEO</li>
                    <li lay-id="configseo_4">{:lang('admin/system/configseo/role_index')}SEO</li>
                    <li lay-id="configseo_5">{:lang('admin/system/configseo/plot_index')}SEO</li>
                    <li lay-id="configseo_6">{:lang('admin/system/configseo/website_index')}SEO</li>
                </ul>
                <div class="layui-tab-content">
                    <div class="layui-tab-item layui-show">
                        <blockquote class="layui-elem-quote layui-quote-nm">
                            {:lang('admin/system/configseo/tip_des')}：<br>
                            {:lang('admin/system/configseo/vod_index')} vod/index
                        </blockquote>
                <div class="layui-form-item">
                    <label class="layui-form-label">
                        {:lang('admin/system/configseo/tit')}：</label>
                    <div class="layui-input-block">
                        <input type="text" name="seo[vod][name]" placeholder="{:lang('admin/system/configseo/tit')}title" value="{$config['seo']['vod']['name']}" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">
                        {:lang('admin/system/configseo/key')}：</label>
                    <div class="layui-input-block">
                        <input type="text" name="seo[vod][key]" placeholder="{:lang('admin/system/configseo/key')}keywords" value="{$config['seo']['vod']['key']}" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">
                        {:lang('admin/system/configseo/des')}：</label>
                    <div class="layui-input-block">
                        <input type="text" name="seo[vod][des]" placeholder="{:lang('admin/system/configseo/des')}description" value="{$config['seo']['vod']['des']}" class="layui-input">
                    </div>
                </div>

            </div>
                    <div class="layui-tab-item">
                        <blockquote class="layui-elem-quote layui-quote-nm">
                            {:lang('admin/system/configseo/tip_des')}：<br>
                            {:lang('admin/system/configseo/art_index')} art/index
                        </blockquote>
                        <div class="layui-form-item">
                            <label class="layui-form-label">
                                {:lang('admin/system/configseo/tit')}：</label>
                            <div class="layui-input-block">
                                <input type="text" name="seo[art][name]" placeholder="{:lang('admin/system/configseo/tit')}title" value="{$config['seo']['art']['name']}" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">
                                {:lang('admin/system/configseo/key')}：</label>
                            <div class="layui-input-block">
                                <input type="text" name="seo[art][key]" placeholder="{:lang('admin/system/configseo/key')}keywords" value="{$config['seo']['art']['key']}" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">
                                {:lang('admin/system/configseo/des')}：</label>
                            <div class="layui-input-block">
                                <input type="text" name="seo[art][des]" placeholder="{:lang('admin/system/configseo/des')}description" value="{$config['seo']['art']['des']}" class="layui-input">
                            </div>
                        </div>

                    </div>


                    <div class="layui-tab-item">
                        <blockquote class="layui-elem-quote layui-quote-nm">
                            {:lang('admin/system/configseo/tip_des')}：<br>
                            {:lang('admin/system/configseo/actor_index')} actor/index
                        </blockquote>
                        <div class="layui-form-item">
                            <label class="layui-form-label">
                                {:lang('admin/system/configseo/tit')}：</label>
                            <div class="layui-input-block">
                                <input type="text" name="seo[actor][name]" placeholder="{:lang('admin/system/configseo/tit')}title" value="{$config['seo']['actor']['name']}" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">
                                {:lang('admin/system/configseo/key')}：</label>
                            <div class="layui-input-block">
                                <input type="text" name="seo[actor][key]" placeholder="{:lang('admin/system/configseo/key')}keywords" value="{$config['seo']['actor']['key']}" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">
                                {:lang('admin/system/configseo/des')}：</label>
                            <div class="layui-input-block">
                                <input type="text" name="seo[actor][des]" placeholder="{:lang('admin/system/configseo/des')}description" value="{$config['seo']['actor']['des']}" class="layui-input">
                            </div>
                        </div>

                    </div>

                    <div class="layui-tab-item">
                        <blockquote class="layui-elem-quote layui-quote-nm">
                            {:lang('admin/system/configseo/tip_des')}：<br>
                            {:lang('admin/system/configseo/role_index')} role/index
                        </blockquote>
                        <div class="layui-form-item">
                            <label class="layui-form-label">
                                {:lang('admin/system/configseo/tit')}：</label>
                            <div class="layui-input-block">
                                <input type="text" name="seo[role][name]" placeholder="{:lang('admin/system/configseo/tit')}title" value="{$config['seo']['role']['name']}" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">
                                {:lang('admin/system/configseo/key')}：</label>
                            <div class="layui-input-block">
                                <input type="text" name="seo[role][key]" placeholder="{:lang('admin/system/configseo/key')}keywords" value="{$config['seo']['role']['key']}" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">
                                {:lang('admin/system/configseo/des')}：</label>
                            <div class="layui-input-block">
                                <input type="text" name="seo[role][des]" placeholder="{:lang('admin/system/configseo/des')}description" value="{$config['seo']['role']['des']}" class="layui-input">
                            </div>
                        </div>

                    </div>

                    <div class="layui-tab-item">
                        <blockquote class="layui-elem-quote layui-quote-nm">
                            {:lang('admin/system/configseo/tip_des')}：<br>
                            {:lang('admin/system/configseo/plot_index')} plot/index
                        </blockquote>
                        <div class="layui-form-item">
                            <label class="layui-form-label">
                                {:lang('admin/system/configseo/tit')}：</label>
                            <div class="layui-input-block">
                                <input type="text" name="seo[plot][name]" placeholder="{:lang('admin/system/configseo/tit')}title" value="{$config['seo']['plot']['name']}" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">
                                {:lang('admin/system/configseo/key')}：</label>
                            <div class="layui-input-block">
                                <input type="text" name="seo[plot][key]" placeholder="{:lang('admin/system/configseo/key')}keywords" value="{$config['seo']['plot']['key']}" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">
                                {:lang('admin/system/configseo/des')}：</label>
                            <div class="layui-input-block">
                                <input type="text" name="seo[plot][des]" placeholder="{:lang('admin/system/configseo/des')}description" value="{$config['seo']['plot']['des']}" class="layui-input">
                            </div>
                        </div>

                    </div>


                    <div class="layui-tab-item">
                        <blockquote class="layui-elem-quote layui-quote-nm">
                            {:lang('admin/system/configseo/tip_des')}：<br>
                            {:lang('admin/system/configseo/website_index')} website/index
                        </blockquote>
                        <div class="layui-form-item">
                            <label class="layui-form-label">
                                {:lang('admin/system/configseo/tit')}：</label>
                            <div class="layui-input-block">
                                <input type="text" name="seo[website][name]" placeholder="{:lang('admin/system/configseo/tit')}title" value="{$config['seo']['website']['name']}" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">
                                {:lang('admin/system/configseo/key')}：</label>
                            <div class="layui-input-block">
                                <input type="text" name="seo[website][key]" placeholder="{:lang('admin/system/configseo/key')}keywords" value="{$config['seo']['website']['key']}" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">
                                {:lang('admin/system/configseo/des')}：</label>
                            <div class="layui-input-block">
                                <input type="text" name="seo[website][des]" placeholder="{:lang('admin/system/configseo/des')}description" value="{$config['seo']['website']['des']}" class="layui-input">
                            </div>
                        </div>

                    </div>

                </div>
        </div>
            <div class="layui-form-item center">
                <div class="layui-input-block">
                    <button type="submit" class="layui-btn" lay-submit="" lay-filter="formSubmit">{:lang('btn_save')}</button>
                    <button class="layui-btn layui-btn-warm" type="reset">{:lang('btn_reset')}</button>
                </div>
            </div>
    </form>
</div>

{include file="../../../application/admin/view/public/foot" /}
<script type="text/javascript" src="__STATIC__/js/jquery.cookie.js"></script>
<script type="text/javascript">
    layui.use(['element', 'form', 'layer'], function() {
        var element = layui.element
            ,form = layui.form
            , layer = layui.layer;


        element.on('tab(tb1)', function(){
            $.cookie('configapi_tab', this.getAttribute('lay-id'));
        });

        if( $.cookie('configapi_tab') !=null ) {
            element.tabChange('tb1', $.cookie('configapi_tab'));
        }

    });
</script>

</body>
</html>