{include file="../../../application/admin/view/public/head" /}
<div class="page-container p10">

    <div class="my-toolbar-box">
        {if condition="$param.select neq 1"}
        <div class="center mb10">
            <form class="layui-form " method="post" action="{:url('data')}">
                <input type="hidden" value="{$param.select|mac_filter_xss}" name="select">
                <input type="hidden" value="{$param.input|mac_filter_xss}" name="input">
                <div class="layui-input-inline w150">
                    <select name="status">
                        <option value="">{:lang('select_status')}</option>
                        <option value="0" {if condition="$param['status'] eq '0'"}selected {/if}>{:lang('reviewed_not')}</option>
                        <option value="1" {if condition="$param['status'] eq '1'"}selected {/if}>{:lang('reviewed')}</option>
                    </select>
                </div>
                <div class="layui-input-inline w150">
                    <select name="level">
                        <option value="">{:lang('select_level')}</option>
                        <option value="9" {if condition="$param['level'] eq '9'"}selected {/if}>{:lang('level')}9-{:lang('slide')}</option>
                        <option value="1" {if condition="$param['level'] eq '1'"}selected {/if}>{:lang('level')}1</option>
                        <option value="2" {if condition="$param['level'] eq '2'"}selected {/if}>{:lang('level')}2</option>
                        <option value="3" {if condition="$param['level'] eq '3'"}selected {/if}>{:lang('level')}3</option>
                        <option value="4" {if condition="$param['level'] eq '4'"}selected {/if}>{:lang('level')}4</option>
                        <option value="5" {if condition="$param['level'] eq '5'"}selected {/if}>{:lang('level')}5</option>
                        <option value="6" {if condition="$param['level'] eq '6'"}selected {/if}>{:lang('level')}6</option>
                        <option value="7" {if condition="$param['level'] eq '7'"}selected {/if}>{:lang('level')}7</option>
                        <option value="8" {if condition="$param['level'] eq '8'"}selected {/if}>{:lang('level')}8</option>
                    </select>
                </div>
                <div class="layui-input-inline w150">
                    <select name="pic">
                        <option value="">{:lang('select_pic')}</option>
                        <option value="1" {if condition="$param['pic'] eq '1'"}selected{/if}>{:lang('pic_empty')}</option>
                        <option value="2" {if condition="$param['pic'] eq '2'"}selected{/if}>{:lang('pic_remote')}</option>
                        <option value="3" {if condition="$param['pic'] eq '3'"}selected{/if}>{:lang('pic_sync_err')}</option>
                    </select>
                </div>
                <div class="layui-input-inline w150">
                    <select name="order">
                        <option value="">{:lang('select_sort')}</option>
                        <option value="role_time" {if condition="$param['order'] eq 'role_time'"}selected{/if}>{:lang('update_time')}</option>
                        <option value="role_id" {if condition="$param['order'] eq 'role_id'"}selected{/if}>{:lang('id')}</option>
                        <option value="role_hits" {if condition="$param['order'] eq 'role_hits'"}selected{/if}>{:lang('hits')}</option>
                        <option value="role_hits_month" {if condition="$param['order'] eq 'role_hits_month'"}selected{/if}>{:lang('hits_month')}</option>
                        <option value="role_hits_week" {if condition="$param['order'] eq 'role_hits_week'"}selected{/if}>{:lang('hits_week')}</option>
                        <option value="role_hits_day" {if condition="$param['order'] eq 'role_hits_day'"}selected{/if}>{:lang('hits_day')}</option>
                    </select>
                </div>

                <div class="layui-input-inline">
                    <input type="text" autocomplete="off" placeholder="{:lang('wd')}" class="layui-input" name="wd" value="{$param['wd']|mac_filter_xss}">
                </div>
                <button class="layui-btn mgl-20 j-search" >{:lang('btn_search')}</button>
            </form>
        </div>
        {/if}

        <div class="layui-btn-group">
            {if condition="$param.select eq 1 && $param.rid neq ''"}
            <a data-href="{:url('info')}?tab={$param.tab}&rid={$param.rid}" data-full="1" class="layui-btn layui-btn-primary j-iframe"><i class="layui-icon">&#xe654;</i>{:lang('add')}</a>
            {/if}
            <a data-href="{:url('del')}" class="layui-btn layui-btn-primary j-page-btns confirm"><i class="layui-icon">&#xe640;</i>{:lang('del')}</a>
            <a data-href="{:url('index/select')}?tab=role&col=role_level&tpl=select_level&url=role/field" data-width="270" data-height="100" data-checkbox="1" class="layui-btn layui-btn-primary j-select"><i class="layui-icon">&#xe620;</i>{:lang('level')}</a>
            <a data-href="{:url('index/select')}?tab=role&col=role_hits&tpl=select_hits&url=role/field" data-width="470" data-height="100" data-checkbox="1" class="layui-btn layui-btn-primary j-select"><i class="layui-icon">&#xe620;</i>{:lang('hits')}</a>
            <a data-href="{:url('index/select')}?tab=role&col=role_status&tpl=select_status&url=role/field" data-width="470" data-height="100" data-checkbox="1" class="layui-btn layui-btn-primary j-select"><i class="layui-icon">&#xe620;</i>{:lang('status')}</a>
            <a class="layui-btn layui-btn-primary j-iframe" data-href="{:url('images/opt?tab=role')}" href="javascript:;" title="{:lang('pic_sync')}"><i class="layui-icon">&#xe620;</i>{:lang('pic_sync')}</a>
        </div>

    </div>


    <form class="layui-form " method="post" id="pageListForm">
        <table class="layui-table" lay-size="sm">
            <thead>
            <tr>
                <th width="25"><input type="checkbox" lay-skin="primary" lay-filter="allChoose"></th>
                <th width="50">{:lang('id')}</th>
                <th >{:lang('vod_name')}</th>
                <th width="150">{:lang('role_name')}</th>
                <th width="150">{:lang('actor_name')}</th>
                <th width="40">{:lang('sort')}</th>
                <th width="40">{:lang('hits')}</th>
                <th width="40">{:lang('level')}</th>
                <th width="120">{:lang('update_time')}</th>
                <th width="80">{:lang('opt')}</th>
            </tr>
            </thead>

            {volist name="list" id="vo"}
            <tr>
                <td><input type="checkbox" name="ids[]" value="{$vo.role_id}" class="layui-checkbox checkbox-ids" lay-skin="primary"></td>
                <td>{$vo.role_id}</td>
                <td><a target="_blank" class="layui-badge-rim " href="{:mac_url_vod_detail($vo.data)}">[{$vo.data.vod_name|htmlspecialchars}]</a></td>
                <td> <a target="_blank" class="layui-badge-rim " href="{:mac_url_role_detail($vo)}">{$vo.role_name|htmlspecialchars}</a> {if condition="$vo.role_status eq 0"} <span class="layui-badge">{:lang('reviewed_not')}</span>{/if} {if condition="$vo.role_lock eq 1"} <span class="layui-badge">{:lang('lock')}</span>{/if}</td>
                <td>{$vo.role_actor|htmlspecialchars}</td>
                <td>{$vo.role_sort}</td>
                <td>{$vo.role_hits}</td>
                <td><a data-href="{:url('index/select')}?tab=role&col=role_level&tpl=select_level&url=role/field&ids={$vo.role_id}" data-width="270" data-height="100" class=" j-select"><span class="layui-badge layui-bg-orange">{$vo.role_level}</span></a></td>
                <td>{$vo.role_time|mac_day='color'}</td>
                <td>
                    <a class="layui-badge-rim j-iframe" data-full="1" data-href="{:url('info?id='.$vo['role_id'])}" href="javascript:;" title="{:lang('edit')}">{:lang('edit')}</a>
                    <a class="layui-badge-rim j-tr-del" data-href="{:url('del?ids='.$vo['role_id'])}" href="javascript:;" title="{:lang('del')}">{:lang('del')}</a>
                </td>
            </tr>
            {/volist}
            </tbody>
        </table>
        <div id="pages" class="center"></div>
    </form>
</div>




{include file="../../../application/admin/view/public/foot" /}

<script type="text/javascript">
    var curUrl="{:url('role/data',$param)}";
    layui.use(['laypage', 'layer','form'], function() {
        var laypage = layui.laypage
                , layer = layui.layer,
                form = layui.form;

        laypage.render({
            elem: 'pages'
            ,count: {$total}
            ,limit: {$limit}
            ,curr: {$page}
            ,layout: ['count', 'prev', 'page', 'next', 'limit', 'skip']
            ,jump: function(obj,first){
                if(!first){
                    location.href = curUrl.replace('%7Bpage%7D',obj.curr).replace('%7Blimit%7D',obj.limit);
                }
            }
        });


    });
</script>
</body>
</html>