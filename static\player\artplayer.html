<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=no" id="viewport">
<meta name="referrer" content="never">
<meta name="apple-mobile-web-app-capable" content="yes">
<meta name="apple-touch-fullscreen" content="yes">
<meta name="apple-mobile-web-app-status-bar-style" content="black">
<meta name="full-screen" content="yes">
<meta name="browsermode" content="application">
<meta name="x5-fullscreen" content="true">
<meta name="x5-page-mode" content="app">
<title>ArtPlayer播放器</title>
<script type="text/javascript" src="./artplayer/jquery.js"></script>
<script type="text/javascript" src="./artplayer/artplayer.js?v=5.1.7"></script>
<script type="text/javascript" src="./artplayer/artplayer.legacy.js?v=5.1.7"></script>
<script type="text/javascript" src="./artplayer/crypto-js.min.js"></script>
<script type="text/javascript" src="./artplayer/hls.min.js"></script>
<style>
    body,html { font: 24px "Microsoft YaHei", Arial, Lucida Grande, Tahoma, sans-serif; width: 100%; height: 100%; padding: 0; margin: 0; overflow-x: hidden; overflow-y: hidden; background-color: black; } .artplayer-app{width:100%;height:100%}
    .artplayer-notice-list{position:absolute;bottom:80px;left:20px;z-index:100}.artplayer-notice-list .artplayer-notice{border-radius:2px;background:rgba(28,28,28,0.9);transition:all 0.3s ease-in-out;overflow:hidden;color:#fff;display:table;pointer-events:none;animation:showNotice 0.3s ease 1 forwards}.artplayer-notice-list .remove-notice{animation:removeNotice 0.3s ease 1 forwards}@keyframes showNotice{from{padding:0;font-size:0;margin-top:0}to{padding:7px 20px;font-size:14px;margin-top:5px}}@keyframes removeNotice{0%{padding:7px 20px;font-size:14px;margin-top:5px}20%{font-size:12px}21%{font-size:0;padding:7px 10px}100%{padding:0;margin-top:0;font-size:0}}.main{width:100%;position:absolute;z-index:1;text-align:center;left:50%;top:50%;transform:translate(-50%,-50%)}.loading{height:60px;width:60px;display:inline-block;vertical-align:middle;animation:Loading 0.6s steps(8,end) infinite;background:#000 url(./artplayer/load3.svg) no-repeat;background-size:100%;border-radius:50px;border:10px solid #000;box-sizing:border-box}@keyframes Loading{0%{-webkit-transform:rotate3d(0,0,1,0deg);transform:rotate3d(0,0,1,0deg)}100%{-webkit-transform:rotate3d(0,0,1,360deg);transform:rotate3d(0,0,1,360deg)}}.tips{color:#fff;margin-top:5px;font-size:16px;font-weight:200;display:flex;flex-direction:row;flex-wrap:nowrap;align-content:center;justify-content:center;align-items:center}.tips span{color:rgb(255 0 123);font-weight:600;margin:5px}
  </style>
</head>
<body>
    <div class="main">
      <div class="loading"></div>
      <div class="tips">
        资源正在加载中
        <span class="clock">5</span>
      </div>
    </div>
    <script>
      var t = 5
      var time = document.getElementsByClassName('clock')[0]
      function fun() {
        t--
        time.innerHTML = t
        if (t <= 0) {
          clearInterval(inter)
        }
      }
      var inter = setInterval('fun()', 1000)
    </script>    
<script type="text/javascript" src="./artplayer/player.js?v=1.0.0"></script>
</body>
</html>