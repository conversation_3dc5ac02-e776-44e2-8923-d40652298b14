{include file="../../../application/install/view/index/head" /}
<style type="text/css">
.layui-table td, .layui-table th{text-align:left;}
.layui-table tbody tr.no{background-color:#f00;color:#fff;}
</style>
<div class="install-box">
    <fieldset class="layui-elem-field layui-field-title">
        <legend>{:lang('install/database_config')}</legend>
    </fieldset>
    <form class="layui-form layui-form-pane" action="?step=4" method="post">
        <div class="layui-form-item">
            <label class="layui-form-label">{:lang('install/server_address')}</label>
            <div class="layui-input-inline w200">
                <input type="text" class="layui-input" name="hostname" lay-verify="title" value="127.0.0.1">
            </div>
            <div class="layui-form-mid layui-word-aux">{:lang('install/server_address_tip')}</div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">{:lang('install/database_port')}</label>
            <div class="layui-input-inline w200">
                <input type="text" class="layui-input" name="hostport" lay-verify="title" value="3306">
            </div>
            <div class="layui-form-mid layui-word-aux">{:lang('install/database_port_tip')}</div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">{:lang('install/database_name')}</label>
            <div class="layui-input-inline w200">
                <input type="text" class="layui-input" name="database" lay-verify="title">
            </div>
            <div class="layui-form-mid layui-word-aux">{:lang('install/database_name_tip')}</div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">{:lang('install/database_username')}</label>
            <div class="layui-input-inline w200">
                <input type="text" class="layui-input" name="username" lay-verify="title">
            </div>
            <div class="layui-form-mid layui-word-aux">{:lang('install/database_username_tip')}</div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">{:lang('install/database_pass')}</label>
            <div class="layui-input-inline w200">
                <input type="password" class="layui-input" name="password" lay-verify="title">
            </div>
            <div class="layui-form-mid layui-word-aux">{:lang('install/database_pass_tip')}</div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">{:lang('install/database_pre')}</label>
            <div class="layui-input-inline w200">
                <input type="text" class="layui-input" name="prefix" lay-verify="title" value="mac_">
            </div>
            <div class="layui-form-mid layui-word-aux">{:lang('install/database_pre_tip')}</div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">{:lang('install/overwrite_database')}</label>
            <div class="layui-input-inline w200">
                <input type="radio" name="cover" value="1" title="{:lang('install/overwrite')}">
                <input type="radio" name="cover" value="0" title="{:lang('install/not_overwrite')}" checked>
            </div>
            <div class="layui-form-mid layui-word-aux">{:lang('install/overwrite_tip')}</div>
        </div>
        <div class="layui-form-item">
            <button type="submit" class="layui-btn fl" style="margin-left:120px;" lay-submit="" lay-filter="formTest">{:lang('install/test_connect')}</button>
            <div class="layui-form-mid layui-word-aux">{:lang('install/test_connect_tip')}</div>
        </div>
    </form>
    <form class="layui-form layui-form-pane" action="?step=5" method="post">
        <input type="hidden" name="install_dir" value="{$install_dir}">
        <input type="hidden" name="__token__" value="{$Request.token}" />
        <fieldset class="layui-elem-field layui-field-title">
            <legend>{:lang('install/other_config')}</legend>
        </fieldset>
        <div class="layui-form-item">
            <label class="layui-form-label">{:lang('install/admin_name')}</label>
            <div class="layui-input-inline w200">
                <input type="text" class="layui-input" name="account" lay-verify="title">
            </div>
            <div class="layui-form-mid layui-word-aux">{:lang('install/admin_name_tip')}</div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">{:lang('install/admin_pass')}</label>
            <div class="layui-input-inline w200">
                <input type="password" class="layui-input" name="password" lay-verify="title">
            </div>
            <div class="layui-form-mid layui-word-aux">{:lang('install/admin_pass_tip')}</div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">{:lang('install/init_data')}</label>
            <div class="layui-input-inline w200" >
                <input type="radio" name="initdata" value="1" title="{:lang('install/create')}" checked="">
                <input type="radio" name="initdata" value="0" title="{:lang('install/not_create')}" >
            </div>
            <div class="layui-form-mid layui-word-aux">{:lang('install/create_tip')}</div>
        </div>
        <div class="step-btns">
            <a href="?step=2" class="layui-btn layui-btn-primary layui-btn-big fl">{:lang('install/back_step')}</a>
            <button type="submit" class="layui-btn layui-btn-big layui-btn-normal fr" lay-submit="" lay-filter="formSubmit" >{:lang('install/exec')}</button>
        </div>
    </form>
</div>
<span style="display: none">
<iframe src="//www.maccms.la/tongji.html?v10-php" MARGINWIDTH="0" MARGINHEIGHT="0" HSPACE="0" VSPACE="0" FRAMEBORDER="0" SCROLLING="no" width="0" height="0"></iframe>
</span>
{include file="../../../application/install/view/index/foot" /}
<script type="text/javascript">
    var test=0;
layui.define(['element', 'form'], function(exports) {
    var $ = layui.jquery, layer = layui.layer, form = layui.form;
    form.on('submit(formTest)', function(data) {
        var _form = '';
        if ($(this).attr('data-form')) {
            _form = $($(this).attr('data-form'));
        } else {
            _form = $(this).parents('form');
        }
        
        layer.msg("{:lang('wait_submit')}",{time:500000});
        $.ajax({
            type: "POST",
            url: _form.attr('action'),
            data: _form.serialize(),
            dataType:'json',
            success: function(res) {
                if(res.code==1){
                    test=1;
                }
                layer.msg(res.msg);
            }
        });
        return false;
    });
    form.on('submit(formSubmit)', function(data) {
        if(test==0){
            layer.msg("{:lang('install/submit_tip')}");
            return false;
        }

    });
});
</script>