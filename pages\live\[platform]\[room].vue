<template>
  <div class="bg-gray-900 min-h-screen">
    <!-- 加载状态 - 移动端优化 -->
    <div v-if="loading" class="flex items-center justify-center min-h-screen px-4">
      <div class="text-center">
        <div class="relative w-16 h-16 sm:w-24 sm:h-24 mx-auto">
          <!-- 外圈脉冲 -->
          <div class="absolute inset-0 w-16 h-16 sm:w-24 sm:h-24 bg-orange-500/20 rounded-full animate-ping"></div>
          <!-- 中圈脉冲 -->
          <div class="absolute inset-1 sm:inset-2 w-14 h-14 sm:w-20 sm:h-20 bg-orange-500/30 rounded-full animate-pulse"></div>
          <!-- 内圈旋转 -->
          <div class="absolute inset-3 sm:inset-4 w-10 h-10 sm:w-16 sm:h-16 border-2 sm:border-4 border-orange-500/40 border-t-orange-500 rounded-full animate-spin"></div>
          <!-- 中心圆 -->
          <div class="absolute inset-0 flex items-center justify-center">
            <div class="w-4 h-4 sm:w-6 sm:h-6 bg-gradient-to-r from-orange-400 to-red-500 rounded-full shadow-lg animate-pulse"></div>
          </div>
          <!-- 装饰点 -->
          <div class="absolute inset-0 animate-spin" style="animation-duration: 3s;">
            <div class="absolute top-0 left-1/2 w-1.5 h-1.5 sm:w-2 sm:h-2 bg-orange-400 rounded-full transform -translate-x-1/2 -translate-y-1"></div>
            <div class="absolute bottom-0 left-1/2 w-1.5 h-1.5 sm:w-2 sm:h-2 bg-red-400 rounded-full transform -translate-x-1/2 translate-y-1"></div>
            <div class="absolute left-0 top-1/2 w-1.5 h-1.5 sm:w-2 sm:h-2 bg-yellow-400 rounded-full transform -translate-y-1/2 -translate-x-1"></div>
            <div class="absolute right-0 top-1/2 w-1.5 h-1.5 sm:w-2 sm:h-2 bg-pink-400 rounded-full transform -translate-y-1/2 translate-x-1"></div>
          </div>
        </div>
        <p class="text-white text-lg sm:text-xl font-medium mt-4 sm:mt-8">加载直播间中...</p>
        <p class="text-gray-400 text-sm mt-2">正在连接直播流</p>
      </div>
    </div>

    <!-- 错误状态 - 移动端优化 -->
    <div v-else-if="error" class="flex items-center justify-center min-h-screen px-4">
      <div class="text-center max-w-md mx-auto p-4 sm:p-8">
        <div class="w-16 h-16 sm:w-20 sm:h-20 bg-red-500/20 rounded-full flex items-center justify-center mb-4 mx-auto">
          <svg class="w-8 h-8 sm:w-10 sm:h-10 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        </div>
        <h2 class="text-lg sm:text-xl font-bold text-white mb-2">加载失败</h2>
        <p class="text-gray-400 mb-4 sm:mb-6 text-sm sm:text-base">{{ error }}</p>
        <div class="space-y-3">
          <button @click="fetchRoomData" class="w-full px-4 sm:px-6 py-2 sm:py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors text-sm sm:text-base">
            重新加载
          </button>
          <NuxtLink :to="`/live/${platform}`" class="block w-full px-4 sm:px-6 py-2 sm:py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors text-center text-sm sm:text-base">
            返回平台页面
          </NuxtLink>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div v-else>
      <!-- 主内容区 - 移动端优化 -->
      <main class="w-full px-2 sm:px-4 lg:px-8 py-3 sm:py-6">
        <div class="w-full lg:w-[70%] mx-auto">

          <!-- 面包屑导航 - 移动端优化 -->
          <nav class="mb-4 sm:mb-6">
            <div class="flex items-center space-x-1 sm:space-x-2 text-xs sm:text-sm overflow-x-auto">
              <NuxtLink to="/live" class="text-gray-400 hover:text-orange-400 transition-colors flex items-center flex-shrink-0">
                <svg class="w-3 h-3 sm:w-4 sm:h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                </svg>
                <span class="hidden sm:inline">直播平台</span>
                <span class="sm:hidden">直播</span>
              </NuxtLink>
              <span class="text-gray-600 flex-shrink-0">/</span>
              <NuxtLink :to="`/live/${platform}`" class="text-gray-400 hover:text-orange-400 transition-colors flex-shrink-0 truncate max-w-20 sm:max-w-none">
                {{ platformInfo?.name || platform }}
              </NuxtLink>
              <span class="text-gray-600 flex-shrink-0">/</span>
              <span class="text-white font-medium truncate">{{ roomData?.title || '直播间' }}</span>
            </div>
          </nav>



          <!-- 播放器区域 - 移动端优化 -->
          <div class="mb-4 sm:mb-8">
              <!-- 播放器容器 -->
              <div class="relative bg-black rounded-lg sm:rounded-2xl overflow-hidden shadow-2xl mb-4 sm:mb-8">
                <div class="aspect-video relative">
                  <!-- FLV 播放器 -->
                  <FlvPlayer v-if="roomData?.proxyStreamUrl || roomData?.streamUrl"
                    ref="videoPlayer"
                    :src="roomData.proxyStreamUrl || roomData.streamUrl" :poster="roomData?.thumbnail" :autoplay="true"
                    :muted="true" :player-id="`room-${platform}-${room}`" @ready="onPlayerReady" @error="onPlayerError"
                    @play="onPlayerPlay" @pause="onPlayerPause" />

                  <!-- 播放器占位符 - 移动端优化 -->
                  <div v-else class="w-full h-full bg-gradient-to-br from-gray-800 to-gray-900 flex items-center justify-center">
                    <div class="text-center px-4">
                      <div class="relative w-20 h-20 sm:w-32 sm:h-32 mx-auto mb-4 sm:mb-8">
                        <!-- 外圈脉冲 -->
                        <div class="absolute inset-0 w-20 h-20 sm:w-32 sm:h-32 bg-orange-500/10 rounded-full animate-ping"></div>
                        <!-- 中圈脉冲 -->
                        <div class="absolute inset-2 sm:inset-3 w-16 h-16 sm:w-26 sm:h-26 bg-orange-500/20 rounded-full animate-pulse"></div>
                        <!-- 内圈旋转 -->
                        <div class="absolute inset-4 sm:inset-6 w-12 h-12 sm:w-20 sm:h-20 border-2 sm:border-4 border-orange-500/30 border-t-orange-500 rounded-full animate-spin"></div>
                        <!-- 播放图标背景 -->
                        <div class="absolute inset-0 flex items-center justify-center">
                          <div class="w-10 h-10 sm:w-16 sm:h-16 bg-gradient-to-r from-orange-500/20 to-red-500/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                            <svg class="w-6 h-6 sm:w-10 sm:h-10 text-orange-400 animate-pulse" fill="currentColor" viewBox="0 0 24 24">
                              <path d="M8 5v14l11-7z" />
                            </svg>
                          </div>
                        </div>
                        <!-- 装饰环 -->
                        <div class="absolute inset-0 animate-spin" style="animation-duration: 4s; animation-direction: reverse;">
                          <div class="absolute top-1 sm:top-2 left-1/2 w-2 h-2 sm:w-3 sm:h-3 bg-gradient-to-r from-orange-400 to-red-400 rounded-full transform -translate-x-1/2 -translate-y-1/2"></div>
                          <div class="absolute bottom-1 sm:bottom-2 left-1/2 w-2 h-2 sm:w-3 sm:h-3 bg-gradient-to-r from-red-400 to-pink-400 rounded-full transform -translate-x-1/2 translate-y-1/2"></div>
                          <div class="absolute left-1 sm:left-2 top-1/2 w-2 h-2 sm:w-3 sm:h-3 bg-gradient-to-r from-yellow-400 to-orange-400 rounded-full transform -translate-y-1/2 -translate-x-1/2"></div>
                          <div class="absolute right-1 sm:right-2 top-1/2 w-2 h-2 sm:w-3 sm:h-3 bg-gradient-to-r from-pink-400 to-purple-400 rounded-full transform -translate-y-1/2 translate-x-1/2"></div>
                        </div>
                      </div>
                      <p class="text-white text-lg sm:text-xl font-bold mb-2">正在连接直播流</p>
                      <p class="text-gray-400 text-sm sm:text-base">请稍候，播放器正在初始化...</p>
                    </div>
                  </div>
                </div>

                <!-- 播放器装饰边框 - 移动端优化 -->
                <div
                  class="absolute inset-0 rounded-lg sm:rounded-2xl border-2 border-gradient-to-r from-orange-500/20 to-red-500/20 pointer-events-none">
                </div>
              </div>

          </div>

          <!-- 直播数据卡片 - 移动端优化 -->
          <div class="mb-4 sm:mb-8">
            <div class="bg-gradient-to-br from-gray-800/80 to-gray-900/80 backdrop-blur-sm rounded-xl sm:rounded-2xl p-4 sm:p-6 border border-gray-700/50">
              <h2 class="text-lg sm:text-xl font-bold text-white mb-4 sm:mb-6 flex items-center">
                <svg class="w-5 h-5 sm:w-6 sm:h-6 mr-2 text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
                直播数据
              </h2>

              <!-- 统计网格 - 移动端一排显示 -->
              <div class="grid grid-cols-3 md:grid-cols-3 gap-2 sm:gap-6">
                <!-- 观看人数卡片 -->
                <div class="bg-gradient-to-br from-orange-500/10 to-red-500/10 rounded-lg sm:rounded-xl p-4 sm:p-6 border border-orange-500/20 hover:border-orange-500/40 transition-all duration-300">
                  <div class="flex items-center justify-between mb-2 sm:mb-3">
                    <span class="text-orange-400 text-xs sm:text-sm font-medium">观看人数</span>
                    <svg class="w-4 h-4 sm:w-5 sm:h-5 text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                  </div>
                  <div class="text-2xl sm:text-3xl font-bold text-white">{{ formatViews(roomData?.viewerCount || 0) }}</div>
                  <div class="text-orange-300/70 text-xs sm:text-sm mt-1">实时观看</div>
                </div>

                <!-- 评分卡片 -->
                <div class="bg-gradient-to-br from-green-500/10 to-emerald-500/10 rounded-lg sm:rounded-xl p-4 sm:p-6 border border-green-500/20 hover:border-green-500/40 transition-all duration-300">
                  <div class="flex items-center justify-between mb-2 sm:mb-3">
                    <span class="text-green-400 text-xs sm:text-sm font-medium">评分</span>
                    <svg class="w-4 h-4 sm:w-5 sm:h-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                    </svg>
                  </div>
                  <div class="text-2xl sm:text-3xl font-bold text-white">{{ roomData?.rating || '9.2' }}</div>
                  <div class="text-green-300/70 text-xs sm:text-sm mt-1">用户评分</div>
                </div>

                <!-- 画质卡片 -->
                <div class="bg-gradient-to-br from-purple-500/10 to-pink-500/10 rounded-lg sm:rounded-xl p-4 sm:p-6 border border-purple-500/20 hover:border-purple-500/40 transition-all duration-300">
                  <div class="flex items-center justify-between mb-2 sm:mb-3">
                    <span class="text-purple-400 text-xs sm:text-sm font-medium">画质</span>
                    <svg class="w-4 h-4 sm:w-5 sm:h-5 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <div class="text-2xl sm:text-3xl font-bold text-white">高清</div>
                  <div class="text-purple-300/70 text-xs sm:text-sm mt-1">视频质量</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 推荐房间 - 移动端优化 -->
          <div class="mb-4 sm:mb-8">
            <div class="bg-gradient-to-br from-gray-800/80 to-gray-900/80 backdrop-blur-sm rounded-xl sm:rounded-2xl p-4 sm:p-6 border border-gray-700/50">
              <h2 class="text-lg sm:text-2xl font-bold text-white mb-4 sm:mb-6 flex items-center">
                <svg class="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
                推荐房间
              </h2>

              <!-- 推荐房间网格 - 移动端一排2个 -->
              <div class="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-6">
                <!-- 推荐房间卡片 - 移动端优化 -->
                <div v-for="recommendedRoom in recommendedRooms" :key="recommendedRoom.id"
                  @click="goToRoom(recommendedRoom.id)"
                  class="group cursor-pointer bg-gradient-to-br from-gray-700/30 to-gray-600/30 hover:from-orange-500/10 hover:to-red-500/10 rounded-lg sm:rounded-xl p-3 sm:p-4 transition-all duration-300 hover:scale-[1.02] border border-gray-700/50 hover:border-orange-500/30">

                  <!-- 缩略图 -->
                  <div class="relative mb-2 sm:mb-3">
                    <div class="w-full aspect-video bg-gray-600 rounded-md sm:rounded-lg overflow-hidden">
                      <img v-if="recommendedRoom.thumbnail" :src="recommendedRoom.thumbnail"
                        :alt="recommendedRoom.title" class="w-full h-full object-cover"
                        @error="$event.target.style.display = 'none'">
                      <div v-else class="w-full h-full flex items-center justify-center">
                        <svg class="w-6 h-6 sm:w-8 sm:h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                        </svg>
                      </div>
                    </div>
                    <!-- 直播标识 -->
                    <div class="absolute top-1 sm:top-2 left-1 sm:left-2">
                      <span class="px-1.5 sm:px-2 py-0.5 sm:py-1 bg-red-500 text-white text-xs font-medium rounded-full flex items-center">
                        <span class="w-1 h-1 sm:w-1.5 sm:h-1.5 bg-white rounded-full mr-1 animate-pulse"></span>
                        <span class="hidden sm:inline">直播中</span>
                        <span class="sm:hidden">●</span>
                      </span>
                    </div>
                  </div>

                  <!-- 房间信息 - 移动端优化 -->
                  <div>
                    <h3 class="text-white font-medium text-xs sm:text-sm line-clamp-2 group-hover:text-orange-400 transition-colors mb-1 sm:mb-2">
                      {{ recommendedRoom.title }}
                    </h3>
                    <p class="text-gray-400 text-xs mb-1 sm:mb-2 truncate">{{ recommendedRoom.streamerName }}</p>
                    <div class="flex items-center justify-between">
                      <span class="text-orange-400 text-xs font-medium bg-orange-500/10 px-1.5 sm:px-2 py-0.5 sm:py-1 rounded-full">
                        👥 {{ formatViews(recommendedRoom.viewerCount || 0) }}
                      </span>
                      <svg class="w-3 h-3 sm:w-4 sm:h-4 text-gray-400 group-hover:text-orange-400 transition-colors" fill="none"
                        stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 查看更多按钮 - 移动端优化 -->
              <div class="mt-4 sm:mt-8 text-center">
                <NuxtLink
                  :to="`/live/${platform}`"
                  class="inline-flex items-center px-4 sm:px-6 py-2 sm:py-3 bg-gradient-to-r from-orange-500 to-red-500 text-white rounded-lg sm:rounded-xl hover:from-orange-600 hover:to-red-600 transition-all duration-300 font-medium hover:scale-105 text-sm sm:text-base"
                >
                  <svg class="w-4 h-4 sm:w-5 sm:h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                  查看更多房间
                </NuxtLink>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>
</template>

<script setup>
// 导入必要的组合式API
import { onBeforeRouteLeave } from 'vue-router'

// 获取路由信息
const route = useRoute()
const platform = computed(() => route.params.platform)
const room = computed(() => route.params.room)

// API 工具
const { apiUser } = useApi()

// 网络状态监控
const { isOnline, fetchWithRetry, diagnoseNetworkIssue } = useNetworkStatus()

// 响应式数据
const loading = ref(true)
const error = ref(null)
const roomData = ref(null)
const platformInfo = ref(null)
const recommendedRooms = ref([])
const videoPlayer = ref(null)

// 防止并发请求的标志
const isLoadingData = ref(false)
const requestAbortController = ref(null)

// 格式化观看次数
const formatViews = (views) => {
  if (views >= 1000000) {
    return (views / 1000000).toFixed(1) + 'M'
  } else if (views >= 1000) {
    return (views / 1000).toFixed(1) + 'K'
  }
  return views.toString()
}

// 获取代理流URL
const getProxyStreamUrl = async (originalUrl) => {
  try {
    console.log('🔄 获取代理流URL:', originalUrl)

    // 如果是HTTPS环境且原始URL是HTTP，使用代理
    if (originalUrl.startsWith('http://')) {
      const proxyUrl = `/api/proxy/direct?url=${encodeURIComponent(originalUrl)}`
      console.log('✅ 使用代理URL:', proxyUrl)
      return proxyUrl
    }

    // 如果已经是HTTPS，直接返回
    return originalUrl
  } catch (error) {
    console.error('❌ 获取代理流URL失败:', error)
    return originalUrl
  }
}

// 获取推荐房间
const fetchRecommendedRooms = async () => {
  try {
    console.log('🔄 获取推荐房间...')
    const response = await apiUser(`/api/live/${platform.value}/rooms`, {
      signal: requestAbortController.value?.signal
    })

    if (response.success) {
      // 设置平台信息
      platformInfo.value = {
        name: response.platformName,
        id: platform.value
      }

      // 设置推荐房间（排除当前房间）
      recommendedRooms.value = response.data
        .filter(r => r.id !== room.value)
        .slice(0, 8)

      console.log('✅ 推荐房间获取成功:', recommendedRooms.value.length)
    }
  } catch (error) {
    console.error('❌ 获取推荐房间失败:', error)
    // 推荐房间获取失败不影响主要功能
  }
}

// 获取房间数据
const fetchRoomData = async () => {
  // 防止并发请求
  if (isLoadingData.value) {
    console.log('⏳ 数据正在加载中，跳过重复请求')
    return
  }

  // 取消之前的请求
  if (requestAbortController.value) {
    requestAbortController.value.abort()
  }

  try {
    isLoadingData.value = true
    loading.value = true
    error.value = null

    // 创建新的AbortController
    requestAbortController.value = new AbortController()

    console.log('🚀 获取房间数据:', {
      platform: platform.value,
      room: room.value
    })

    // 尝试从 useState 获取传递的房间数据
    const currentRoomData = useState('currentRoomData', () => null)

    if (currentRoomData.value?.roomData && currentRoomData.value.roomData.id === room.value) {
      console.log('📦 使用 useState 传递的房间数据')
      roomData.value = currentRoomData.value.roomData

      // 设置平台信息
      if (currentRoomData.value.platformData) {
        platformInfo.value = currentRoomData.value.platformData
      }

      // 处理视频流代理
      if (roomData.value.streamUrl) {
        roomData.value.proxyStreamUrl = await getProxyStreamUrl(roomData.value.streamUrl)
      }

      // 获取推荐房间
      await fetchRecommendedRooms()

      // 清除 useState 数据，避免影响下次访问
      currentRoomData.value = null

      loading.value = false
      return
    }

    // 如果没有路由数据，调用API获取
    console.log('📡 调用API获取房间数据')
    const response = await fetchWithRetry(() =>
      apiUser(`/api/live/${platform.value}/rooms`, {
        signal: requestAbortController.value.signal
      })
    )

    if (!response.success) {
      throw new Error(response.message || '获取房间数据失败')
    }

    // 查找当前房间
    const currentRoom = response.data.find(r => r.id === room.value)

    console.log('🔍 查找房间:', {
      targetRoomId: room.value,
      foundRoom: !!currentRoom,
      allRoomIds: response.data.map(r => r.id).slice(0, 5)
    })

    if (!currentRoom) {
      throw new Error(`房间 ${room.value} 不存在或已下线`)
    }

    // 设置房间数据
    roomData.value = currentRoom

    // 处理视频流代理
    if (roomData.value.streamUrl) {
      roomData.value.proxyStreamUrl = await getProxyStreamUrl(roomData.value.streamUrl)
    }

    // 设置平台信息
    platformInfo.value = {
      name: response.platformName,
      id: platform.value
    }

    // 设置推荐房间（排除当前房间）
    recommendedRooms.value = response.data
      .filter(r => r.id !== room.value)
      .slice(0, 8)

    console.log('✅ 房间数据加载成功:', {
      roomTitle: roomData.value?.title,
      hasProxyUrl: !!roomData.value?.proxyStreamUrl,
      recommendedCount: recommendedRooms.value.length
    })

  } catch (err) {
    console.error('❌ 获取房间数据失败:', err)

    // 处理不同类型的网络错误
    if (err.name === 'AbortError') {
      console.log('📡 请求被取消')
      return // 请求被取消，不显示错误
    }

    // 使用网络诊断功能
    const diagnosis = diagnoseNetworkIssue(err)
    console.log('🔍 网络问题诊断:', diagnosis)

    error.value = `${diagnosis.message} - ${diagnosis.suggestion}`
  } finally {
    isLoadingData.value = false
    loading.value = false
    requestAbortController.value = null
  }
}

// 跳转到其他房间
const goToRoom = async (roomId) => {
  const targetUrl = `/live/${platform.value}/${roomId}`
  console.log('🚀 跳转到房间:', targetUrl)

  try {
    await navigateTo(targetUrl)
  } catch (error) {
    console.error('❌ 跳转失败:', error)
    alert(`跳转失败: ${error.message}`)
  }
}

// 刷新房间
const refreshRoom = () => {
  fetchRoomData()
}

// 播放器事件处理
const onPlayerReady = () => {
  console.log('🎬 播放器就绪')
}

const onPlayerError = (error) => {
  console.error('❌ 播放器错误:', error)
}

const onPlayerPlay = () => {
  console.log('▶️ 开始播放')
}

const onPlayerPause = () => {
  console.log('⏸️ 暂停播放')
}

// 停止播放器
const stopPlayer = () => {
  try {
    if (videoPlayer.value) {
      console.log('🛑 停止播放器')

      // 调用组件的销毁方法（这会触发播放器管理器的清理）
      if (typeof videoPlayer.value.destroy === 'function') {
        videoPlayer.value.destroy()
      }

      // 清空播放器引用
      videoPlayer.value = null
    }

    console.log('✅ 播放器停止完成')

  } catch (error) {
    console.error('❌ 停止播放器时出错:', error)
  }
}

// 强制断开服务端代理连接
const forceDisconnectProxyStream = async (streamUrl) => {
  try {
    if (!streamUrl) return

    console.log('🌐 强制断开服务端代理连接:', streamUrl)

    const response = await apiUser('/api/proxy/disconnect', {
      method: 'POST',
      query: { url: streamUrl }
    })

    if (response.success) {
      console.log('✅ 服务端代理连接已断开:', response.data)
    } else {
      console.warn('⚠️ 断开服务端连接失败:', response.message)
    }
  } catch (error) {
    console.warn('断开服务端代理连接时出错:', error)
  }
}

// 强制断开网络连接
const forceDisconnectNetworkStreams = async () => {
  try {
    console.log('🌐 强制断开网络流连接')

    // 如果有当前播放的流，断开服务端代理连接
    if (roomData.value?.streamUrl) {
      await forceDisconnectProxyStream(roomData.value.streamUrl)
    }

    console.log('✅ 网络流连接已强制断开')
  } catch (error) {
    console.warn('强制断开网络连接时出错:', error)
  }
}





// 页面元数据
useHead({
  title: computed(() => {
    if (roomData.value) {
      return `${roomData.value.title} - ${platformInfo.value?.name || platform.value} | 直播观看`
    }
    return `直播间 - ${platform.value} | 直播观看`
  }),
  meta: [
    {
      name: 'description',
      content: computed(() => {
        if (roomData.value) {
          return `观看 ${roomData.value.title} 的精彩直播内容，主播：${roomData.value.streamerName}`
        }
        return '观看精彩直播内容'
      })
    }
  ]
})

// 监听路由参数变化
watch([platform, room], async (newValues, oldValues) => {
  console.log('📍 路由参数变化:', {
    platform: newValues[0],
    room: newValues[1],
    oldPlatform: oldValues?.[0],
    oldRoom: oldValues?.[1]
  })

  // 避免初始化时的重复调用
  if (oldValues && (newValues[0] !== oldValues[0] || newValues[1] !== oldValues[1])) {
    await fetchRoomData()
  }
}, { immediate: true })

// 页面加载时的调试日志将在下面的onMounted中处理

// 页面卸载时清理资源
onUnmounted(async () => {
  console.log('🧹 房间页面卸载，清理资源')

  // 强制断开网络连接
  await forceDisconnectNetworkStreams()

  // 停止播放器
  stopPlayer()

  // 取消正在进行的请求
  if (requestAbortController.value) {
    requestAbortController.value.abort()
    requestAbortController.value = null
  }

  // 重置状态
  isLoadingData.value = false
  loading.value = false
})

// 路由离开守卫 - 确保离开页面时停止播放器
onBeforeRouteLeave(async (to, from, next) => {
  console.log('🚪 准备离开房间页面，停止播放器', { from: from.path, to: to.path })

  // 强制断开网络连接
  await forceDisconnectNetworkStreams()

  // 停止播放器
  stopPlayer()

  next()
})

// 页面可见性变化处理
const handleVisibilityChange = () => {
  if (document.hidden) {
    console.log('📱 页面隐藏，暂停播放器')
    if (videoPlayer.value && typeof videoPlayer.value.pause === 'function') {
      videoPlayer.value.pause()
    }
  } else {
    console.log('📱 页面显示，可以恢复播放')
    // 注意：这里不自动恢复播放，让用户手动控制
  }
}

// 窗口关闭/刷新时停止播放器
const handleBeforeUnload = () => {
  console.log('🚪 窗口即将关闭/刷新，停止播放器')
  stopPlayer()
}

// 监听页面可见性变化和窗口关闭
onMounted(() => {
  console.log('🎬 房间页面加载:', {
    path: route.path,
    platform: platform.value,
    room: room.value,
    params: route.params
  })

  // 添加页面可见性监听
  document.addEventListener('visibilitychange', handleVisibilityChange)

  // 添加窗口关闭/刷新监听
  window.addEventListener('beforeunload', handleBeforeUnload)
})

// 清理事件监听
onUnmounted(() => {
  document.removeEventListener('visibilitychange', handleVisibilityChange)
  window.removeEventListener('beforeunload', handleBeforeUnload)
})
</script>

<style scoped>
/* 文本截断样式 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 视频播放器样式 */
video {
  background-color: #000;
}

video::-webkit-media-controls-panel {
  background-color: rgba(0, 0, 0, 0.8);
}

/* 自定义滚动条 */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: rgba(75, 85, 99, 0.3);
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.7);
}

/* 加载动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes enhanced-pulse {
  0%, 100% {
    opacity: 0.4;
    transform: scale(0.95);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
}

@keyframes smooth-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes enhanced-ping {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-enhanced-pulse {
  animation: enhanced-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-smooth-spin {
  animation: smooth-spin 2s linear infinite;
}

.animate-enhanced-ping {
  animation: enhanced-ping 1.5s cubic-bezier(0, 0, 0.2, 1) infinite;
}

/* 响应式调整 */
@media (max-width: 1024px) {
  .sticky {
    position: relative;
    top: auto;
  }
}
</style>
