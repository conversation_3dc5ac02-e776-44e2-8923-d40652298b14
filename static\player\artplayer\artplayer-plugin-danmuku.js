
/*!
 * artplayer-plugin-danmuku.js v5.1.4
 * Github: https://github.com/zhw2590582/ArtPlayer
 * (c) 2017-2024 <PERSON>
 * Released under the MIT License.
 */
!function(e,t,i,a,n){var o="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{},s="function"==typeof o[a]&&o[a],l=s.cache||{},r="undefined"!=typeof module&&"function"==typeof module.require&&module.require.bind(module);function d(t,i){if(!l[t]){if(!e[t]){var n="function"==typeof o[a]&&o[a];if(!i&&n)return n(t,!0);if(s)return s(t,!0);if(r&&"string"==typeof t)return r(t);var p=Error("Cannot find module '"+t+"'");throw p.code="MODULE_NOT_FOUND",p}h.resolve=function(i){var a=e[t][1][i];return null!=a?a:i},h.cache={};var u=l[t]=new d.Module(t);e[t][0].call(u.exports,h,u,u.exports,this)}return l[t].exports;function h(e){var t=h.resolve(e);return!1===t?{}:d(t)}}d.isParcelRequire=!0,d.Module=function(e){this.id=e,this.bundle=d,this.exports={}},d.modules=e,d.cache=l,d.parent=s,d.register=function(t,i){e[t]=[function(e,t){t.exports=i},{}]},Object.defineProperty(d,"root",{get:function(){return o[a]}}),o[a]=d;for(var p=0;p<t.length;p++)d(t[p]);if(i){var u=d(i);"object"==typeof exports&&"undefined"!=typeof module?module.exports=u:"function"==typeof define&&define.amd&&define(function(){return u})}}({bgm6t:[function(e,t,i){var a=e("@parcel/transformer-js/src/esmodule-helpers.js");a.defineInteropFlag(i),a.export(i,"default",()=>p);var n=e("./danmuku"),o=a.interopDefault(n),s=e("./setting"),l=a.interopDefault(s),r=e("./heatmap"),d=a.interopDefault(r);function p(e){return t=>{let i=new o.default(t,e),a=new l.default(t,i);return i.option.heatmap&&(0,d.default)(t,i,i.option.heatmap),{name:"artplayerPluginDanmuku",emit:i.emit.bind(i),load:i.load.bind(i),config:i.config.bind(i),hide:i.hide.bind(i),show:i.show.bind(i),reset:i.reset.bind(i),mount:a.mount.bind(a),get option(){return i.option},get isHide(){return i.isHide},get isStop(){return i.isStop}}}}p.icons=l.default.icons,"undefined"!=typeof window&&(window.artplayerPluginDanmuku=p)},{"./danmuku":"4ns48","./setting":"lO8OT","./heatmap":"8AxLD","@parcel/transformer-js/src/esmodule-helpers.js":"9pCYc"}],"4ns48":[function(e,t,i){var a=e("@parcel/transformer-js/src/esmodule-helpers.js");a.defineInteropFlag(i);var n=e("./bilibili"),o=e("bundle-text:./worker"),s=a.interopDefault(o);class l{constructor(e,t){let{constructor:i,template:a}=e;this.utils=i.utils,this.validator=i.validator,this.$danmuku=a.$danmuku,this.$player=a.$player,this.art=e,this.queue=[],this.$refs=[],this.isStop=!1,this.isHide=!1,this.timer=null,this.index=0,this.option=l.option,this.states={wait:[],ready:[],emit:[],stop:[]},this.config(t),this.worker=new Worker(URL.createObjectURL(new Blob([s.default]))),this.start=this.start.bind(this),this.stop=this.stop.bind(this),this.reset=this.reset.bind(this),this.resize=this.resize.bind(this),this.destroy=this.destroy.bind(this),e.on("video:play",this.start),e.on("video:playing",this.start),e.on("video:pause",this.stop),e.on("video:waiting",this.stop),e.on("destroy",this.destroy),e.on("resize",this.resize),this.load()}static get option(){return{danmuku:[],speed:5,margin:[10,"25%"],opacity:1,color:"#FFFFFF",mode:0,modes:[0,1,2],fontSize:25,antiOverlap:!0,synchronousPlayback:!1,mount:void 0,heatmap:!1,width:512,points:[],filter:()=>!0,beforeEmit:()=>!0,beforeVisible:()=>!0,visible:!0,emitter:!0,maxLength:200,lockTime:5,theme:"dark",OPACITY:{},FONT_SIZE:{},MARGIN:{},SPEED:{},COLOR:[]}}static get scheme(){return{danmuku:"array|function|string",speed:"number",margin:"array",opacity:"number",color:"string",mode:"number",modes:"array",fontSize:"number|string",antiOverlap:"boolean",synchronousPlayback:"boolean",mount:"?htmldivelement|string",heatmap:"object|boolean",width:"number",points:"array",filter:"function",beforeEmit:"function",beforeVisible:"function",visible:"boolean",emitter:"boolean",maxLength:"number",lockTime:"number",theme:"string",OPACITY:"object",FONT_SIZE:"object",MARGIN:"object",SPEED:"object",COLOR:"array"}}static get cssText(){return` user-select: none; position: absolute; white-space: pre; pointer-events: none; perspective: 500px; display: inline-block; will-change: transform; font-weight: normal; line-height: 1.125; visibility: hidden; font-family: SimHei, "Microsoft JhengHei", Arial, Helvetica, sans-serif; text-shadow: rgb(0, 0, 0) 1px 0px 1px, rgb(0, 0, 0) 0px 1px 1px, rgb(0, 0, 0) 0px -1px 1px, rgb(0, 0, 0) -1px 0px 1px; `}get isRotate(){return this.art.plugins?.autoOrientation?.state}get marginTop(){let{clamp:e}=this.utils,t=this.option.margin[0],{clientHeight:i}=this.$player;return"number"==typeof t?e(t,0,i):"string"==typeof t&&t.endsWith("%")?e(i*(parseFloat(t)/100),0,i):l.option.margin[0]}get marginBottom(){let{clamp:e}=this.utils,t=this.option.margin[1],{clientHeight:i}=this.$player;return"number"==typeof t?e(t,0,i):"string"==typeof t&&t.endsWith("%")?e(i*(parseFloat(t)/100),0,i):l.option.margin[1]}get fontSize(){let{clamp:e}=this.utils,{clientHeight:t}=this.$player,i=this.option.fontSize;return"number"==typeof i?Math.round(e(i,12,t)):"string"==typeof i&&i.endsWith("%")?Math.round(e(t*(parseFloat(i)/100),12,t)):l.option.fontSize}get $ref(){let e=this.$refs.pop()||document.createElement("div");return e.style.cssText=l.cssText,e.dataset.mode="",e.className="",e}get readys(){let{currentTime:e}=this.art,t=[];return this.filter("ready",e=>t.push(e)),this.filter("wait",i=>{e+.1>=i.time&&i.time>=e-.1&&t.push(i)}),t}get visibles(){let e=[],{clientWidth:t}=this.$player,i=this.getLeft(this.$player);return this.filter("emit",a=>{let n=a.$ref.offsetTop,o=this.getLeft(a.$ref)-i,s=a.$ref.clientHeight,l=a.$ref.clientWidth,r=o+l,d=r/a.$restTime,p={};p.top=n,p.left=o,p.height=s,p.width=l,p.right=t-r,p.speed=d,p.distance=r,p.time=a.$restTime,p.mode=a.mode,e.push(p)}),e}get speed(){return this.option.synchronousPlayback&&this.art.playbackRate?this.option.speed/Number(this.art.playbackRate):this.option.speed}async load(e){let{errorHandle:t}=this.utils,i=[],a=e||this.option.danmuku;try{"function"==typeof a?i=await a():a instanceof Promise?i=await a:"string"==typeof a?i=await (0,n.bilibiliDanmuParseFromUrl)(a):Array.isArray(a)&&(i=a),t(Array.isArray(i),"Danmuku need return an array as result"),void 0===e&&(this.reset(),this.queue=[],this.states={wait:[],ready:[],emit:[],stop:[]},this.$refs=[],this.$danmuku.innerText="");for(let e=0;e<i.length;e++){let t=i[e];await this.emit(t)}this.art.emit("artplayerPluginDanmuku:loaded",this.queue)}catch(e){throw this.art.emit("artplayerPluginDanmuku:error",e),e}return this}async emit(e){let{clamp:t}=this.utils;if(this.validator(e,{text:"string",mode:"?number",color:"?string",time:"?number",border:"?boolean",style:"?object"}),!e.text.trim()||(e.time?e.time=t(e.time,0,1/0):e.time=this.art.currentTime+.5,void 0===e.mode&&(e.mode=this.option.mode),void 0===e.style&&(e.style={}),void 0===e.color&&(e.color=this.option.color),![0,1,2].includes(e.mode)||!this.option.filter(e)))return this;let i={...e,$state:"wait",$id:this.index++,$ref:null,$restTime:0,$lastStartTime:0};return this.setState(i,"wait"),this.queue.push(i),this}config(e){let{clamp:t}=this.utils,{$controlsCenter:i}=this.art.template;return Object.keys(e).some(t=>JSON.stringify(this.option[t])!==JSON.stringify(e[t]))&&(this.option=Object.assign({},l.option,this.option,e),this.validator(this.option,l.scheme),this.option.mode=t(this.option.mode,0,2),this.option.speed=t(this.option.speed,1,10),this.option.opacity=t(this.option.opacity,0,1),this.option.lockTime=t(this.option.lockTime,1,60),this.option.maxLength=t(this.option.maxLength,1,1e3),this.option.mount=this.option.mount||i,e.fontSize&&this.reset(),this.option.visible?this.show():this.hide(),this.art.emit("artplayerPluginDanmuku:config",this.option)),this}getLeft(e){let t=e.getBoundingClientRect();return this.isRotate?t.top:t.left}postMessage(e={}){return new Promise(t=>{e.id=Date.now(),this.worker.postMessage(e),this.worker.onmessage=i=>{let{data:a}=i;a.id===e.id&&t(a)}})}filter(e,t){let i=this.states[e]||[];for(let e=0;e<i.length;e++)t(i[e]);return i}setState(e,t){this.states[e.$state]=this.states[e.$state].filter(t=>t!==e),e.$state=t,e.$ref&&(e.$ref.dataset.state=t),this.states[t].push(e)}makeWait(e){this.setState(e,"wait"),e.$ref&&(e.$ref.style.cssText=l.cssText,e.$ref.style.visibility="hidden",e.$ref.style.marginLeft="0px",e.$ref.style.transform="translateX(0px)",e.$ref.style.transition="transform 0s linear 0s",this.$refs.push(e.$ref),e.$ref=null)}update(){let{setStyles:e}=this.utils;return this.timer=window.requestAnimationFrame(async()=>{if(this.art.playing&&!this.isHide){this.filter("emit",e=>{let t=(Date.now()-e.$lastStartTime)/1e3;e.$restTime-=t,e.$lastStartTime=Date.now(),e.$restTime<=0&&this.makeWait(e)});let t=this.readys;for(let i=0;i<t.length;i++){let a=t[i];if(await this.option.beforeVisible(a)){let{clientWidth:t,clientHeight:i}=this.$player;a.$ref=this.$ref,a.$ref.innerText=a.text,this.$danmuku.appendChild(a.$ref),a.$ref.style.opacity=this.option.opacity,a.$ref.style.fontSize=`${this.fontSize}px`,a.$ref.style.color=a.color,a.$ref.style.border=a.border?`1px solid ${a.color}`:null,a.$ref.style.backgroundColor=a.border?"rgb(0 0 0 / 50%)":null,e(a.$ref,a.style),a.$lastStartTime=Date.now(),a.$restTime=this.speed;let n=t+a.$ref.clientWidth,{result:o}=await this.postMessage({type:"getDanmuTop",target:{mode:a.mode,height:a.$ref.clientHeight,speed:n/a.$restTime},visibles:this.visibles,antiOverlap:this.option.antiOverlap,clientWidth:t,clientHeight:i,marginBottom:this.marginBottom,marginTop:this.marginTop});if(a.$ref){if(this.isStop||void 0===o)this.setState(a,"ready"),this.$refs.push(a.$ref),a.$ref=null;else{switch(this.setState(a,"emit"),a.$ref.style.top=`${o}px`,a.$ref.style.visibility="visible",a.$ref.dataset.mode=a.mode,a.mode){case 0:a.$ref.style.left=`${t}px`,a.$ref.style.marginLeft="0px",a.$ref.style.transform=`translateX(${-n}px)`,a.$ref.style.transition=`transform ${a.$restTime}s linear 0s`;break;case 1:case 2:a.$ref.style.left="50%",a.$ref.style.marginLeft=`-${a.$ref.clientWidth/2}px`}this.art.emit("artplayerPluginDanmuku:visible",a)}}}}}this.isStop||this.update()}),this}resize(){let{clientWidth:e}=this.$player;this.filter("stop",t=>{0===t.mode&&(t.$ref.style.left=`${e}px`)}),this.filter("emit",t=>{if(t.$lastStartTime=Date.now(),0===t.mode){let i=e+t.$ref.clientWidth;t.$ref.style.left=`${e}px`,t.$ref.style.transform=`translateX(${-i}px)`,t.$ref.style.transition=`transform ${t.$restTime}s linear 0s`}})}continue(){let{clientWidth:e}=this.$player;return this.filter("stop",t=>{if(this.setState(t,"emit"),t.$lastStartTime=Date.now(),0===t.mode){let i=e+t.$ref.clientWidth;t.$ref.style.transform=`translateX(${-i}px)`,t.$ref.style.transition=`transform ${t.$restTime}s linear 0s`}}),this}suspend(){let{clientWidth:e}=this.$player;return this.filter("emit",t=>{if(this.setState(t,"stop"),0===t.mode){let i=e-(this.getLeft(t.$ref)-this.getLeft(this.$player));t.$ref.style.transform=`translateX(${-i}px)`,t.$ref.style.transition="transform 0s linear 0s"}}),this}stop(){return this.isStop=!0,this.suspend(),window.cancelAnimationFrame(this.timer),this.art.emit("artplayerPluginDanmuku:stop"),this}start(){return this.isStop=!1,this.continue(),this.update(),this.art.emit("artplayerPluginDanmuku:start"),this}reset(){return this.queue.forEach(e=>this.makeWait(e)),this.art.emit("artplayerPluginDanmuku:reset"),this}show(){return this.isHide=!1,this.$danmuku.style.opacity=1,this.option.visible=!0,this.art.emit("artplayerPluginDanmuku:show"),this}hide(){return this.isHide=!0,this.$danmuku.style.opacity=0,this.option.visible=!1,this.art.emit("artplayerPluginDanmuku:hide"),this}destroy(){this.stop(),this.worker.terminate(),this.art.off("video:play",this.start),this.art.off("video:playing",this.start),this.art.off("video:pause",this.stop),this.art.off("video:waiting",this.stop),this.art.off("resize",this.reset),this.art.off("destroy",this.destroy),this.art.emit("artplayerPluginDanmuku:destroy")}}i.default=l},{"./bilibili":"f83sx","bundle-text:./worker":"lfIAi","@parcel/transformer-js/src/esmodule-helpers.js":"9pCYc"}],f83sx:[function(e,t,i){var a=e("@parcel/transformer-js/src/esmodule-helpers.js");function n(e){switch(e){case 1:case 2:case 3:default:return 0;case 4:return 2;case 5:return 1}}function o(e){if("string"!=typeof e)return[];let t=new RegExp(/<d (?:.*? )??p="(?<p>.+?)"(?: .*?)?>(?<text>.+?)<\/d>/gs);return Array.from(e.matchAll(t)).map(e=>{let t=e.groups.p.split(",");return t.length>=8?{text:e.groups.text.trim().replaceAll("&quot;",'"').replaceAll("&apos;","'").replaceAll("&lt;","<").replaceAll("&gt;",">").replaceAll("&amp;","&"),time:Number(t[0]),mode:n(Number(t[1])),fontSize:Number(t[2]),color:`#${Number(t[3]).toString(16)}`,timestamp:Number(t[4]),pool:Number(t[5]),userID:t[6],rowID:Number(t[7])}:null}).filter(Boolean)}function s({data:e}){let{xml:t,id:i}=e;if(!i||!t)return;let a=o(t);self.postMessage({danmus:a,id:i})}function l(e){return new Promise(async t=>{let i=await fetch(e),a=await i.text();try{let e=function(){let e=new Blob([` ${n.toString()} ${o.toString()} onmessage = ${s.toString()} `],{type:"application/javascript"});return new Worker(URL.createObjectURL(e))}();e.onmessage=i=>{let{danmus:a,id:n}=i.data;n&&a&&(t(a),e.terminate())},e.postMessage({xml:a,id:Date.now()})}catch(e){t(o(a))}})}a.defineInteropFlag(i),a.export(i,"bilibiliDanmuParseFromUrl",()=>l)},{"@parcel/transformer-js/src/esmodule-helpers.js":"9pCYc"}],"9pCYc":[function(e,t,i){i.interopDefault=function(e){return e&&e.__esModule?e:{default:e}},i.defineInteropFlag=function(e){Object.defineProperty(e,"__esModule",{value:!0})},i.exportAll=function(e,t){return Object.keys(e).forEach(function(i){"default"===i||"__esModule"===i||Object.prototype.hasOwnProperty.call(t,i)||Object.defineProperty(t,i,{enumerable:!0,get:function(){return e[i]}})}),t},i.export=function(e,t,i){Object.defineProperty(e,t,{enumerable:!0,get:i})}},{}],lfIAi:[function(e,t,i){t.exports='!function(e,t,n,o,i){var r="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{},f="function"==typeof r[o]&&r[o],l=f.cache||{},d="undefined"!=typeof module&&"function"==typeof module.require&&module.require.bind(module);function u(t,n){if(!l[t]){if(!e[t]){var i="function"==typeof r[o]&&r[o];if(!n&&i)return i(t,!0);if(f)return f(t,!0);if(d&&"string"==typeof t)return d(t);var h=Error("Cannot find module \'"+t+"\'");throw h.code="MODULE_NOT_FOUND",h}s.resolve=function(n){var o=e[t][1][n];return null!=o?o:n},s.cache={};var p=l[t]=new u.Module(t);e[t][0].call(p.exports,s,p,p.exports,this)}return l[t].exports;function s(e){var t=s.resolve(e);return!1===t?{}:u(t)}}u.isParcelRequire=!0,u.Module=function(e){this.id=e,this.bundle=u,this.exports={}},u.modules=e,u.cache=l,u.parent=f,u.register=function(t,n){e[t]=[function(e,t){t.exports=n},{}]},Object.defineProperty(u,"root",{get:function(){return r[o]}}),r[o]=u;for(var h=0;h<t.length;h++)u(t[h]);if(n){var p=u(n);"object"==typeof exports&&"undefined"!=typeof module?module.exports=p:"function"==typeof define&&define.amd&&define(function(){return p})}}({"59OZS":[function(e,t,n){function o({target:e,visibles:t,clientWidth:n,clientHeight:o,marginBottom:i,marginTop:r,antiOverlap:f}){let l=o-i,d=t.filter(t=>t.mode===e.mode&&t.top<=l).sort((e,t)=>e.top-t.top);if(0===d.length)return 2===e.mode?l-e.height:r;if(d.unshift({type:"top",top:0,left:0,right:0,height:r,width:n,speed:0,distance:n}),d.push({type:"bottom",top:l,left:0,right:0,height:i,width:n,speed:0,distance:n}),2===e.mode)for(let t=d.length-2;t>=0;t-=1){let n=d[t],o=d[t+1],i=n.top+n.height;if(o.top-i>=e.height)return o.top-e.height}else for(let t=1;t<d.length;t+=1){let n=d[t],o=d[t-1],i=o.top+o.height;if(n.top-i>=e.height)return i}let u=[];for(let e=1;e<d.length-1;e+=1){let t=d[e];if(u.length){let e=u[u.length-1];e[0].top===t.top?e.push(t):u.push([t])}else u.push([t])}if(f)switch(e.mode){case 0:{let t=u.find(t=>t.every(t=>!(n<t.distance)&&(e.speed<t.speed||t.right/(e.speed-t.speed)>t.time)));return t&&t[0]?t[0].top:void 0}case 1:case 2:return}else{switch(e.mode){case 0:u.sort((e,t)=>{let n=Math.min(...t.map(e=>e.right)),o=Math.min(...e.map(e=>e.right));return n*t.length-o*e.length});break;case 1:case 2:u.sort((e,t)=>{let n=Math.max(...t.map(e=>e.width));return Math.max(...e.map(e=>e.width))*e.length-n*t.length})}return u[0][0].top}}onmessage=e=>{let{data:t}=e;if(!t.id||!t.type)return;let n=(0,({getDanmuTop:o})[t.type])(t);self.postMessage({result:n,id:t.id})}},{}]},["59OZS"],"59OZS","parcelRequire4dc0");'},{}],lO8OT:[function(e,t,i){var a=e("@parcel/transformer-js/src/esmodule-helpers.js");a.defineInteropFlag(i);var n=e("bundle-text:./style.less"),o=a.interopDefault(n),s=e("bundle-text:./img/on.svg"),l=a.interopDefault(s),r=e("bundle-text:./img/off.svg"),d=a.interopDefault(r),p=e("bundle-text:./img/config.svg"),u=a.interopDefault(p),h=e("bundle-text:./img/style.svg"),m=a.interopDefault(h),c=e("bundle-text:./img/mode_0_off.svg"),f=a.interopDefault(c),g=e("bundle-text:./img/mode_0_on.svg"),y=a.interopDefault(g),v=e("bundle-text:./img/mode_1_off.svg"),x=a.interopDefault(v),k=e("bundle-text:./img/mode_1_on.svg"),b=a.interopDefault(k),$=e("bundle-text:./img/mode_2_off.svg"),w=a.interopDefault($),M=e("bundle-text:./img/mode_2_on.svg"),E=a.interopDefault(M),S=e("bundle-text:./img/check_on.svg"),D=a.interopDefault(S),z=e("bundle-text:./img/check_off.svg"),A=a.interopDefault(z);if(i.default=class{constructor(e,t){this.art=e,this.danmuku=t,this.utils=e.constructor.utils;let{setStyle:i}=this.utils,{$controlsCenter:a}=e.template;i(a,"display","flex"),this.template={$controlsCenter:a,$mount:a,$danmuku:null,$toggle:null,$config:null,$configPanel:null,$configModes:null,$style:null,$stylePanel:null,$styleModes:null,$colors:null,$opacitySlider:null,$opacityValue:null,$marginSlider:null,$marginValue:null,$fontSizeSlider:null,$fontSizeValue:null,$speedSlider:null,$speedValue:null,$input:null,$send:null},this.slider={opacity:null,margin:null,fontSize:null,speed:null},this.emitting=!1,this.isLock=!1,this.timer=null,this.createTemplate(),this.createSliders(),this.createEvents(),this.mount(this.option.mount),e.on("resize",()=>this.resize()),e.on("fullscreen",e=>this.onFullscreen(e)),e.on("fullscreenWeb",e=>this.onFullscreen(e)),e.proxy(this.template.$config,"mouseenter",()=>{this.onMouseEnter({$control:this.template.$config,$panel:this.template.$configPanel})}),e.proxy(this.template.$style,"mouseenter",()=>{this.onMouseEnter({$control:this.template.$style,$panel:this.template.$stylePanel})})}static get icons(){return{$on:l.default,$off:d.default,$config:u.default,$style:m.default,$mode_0_off:f.default,$mode_0_on:y.default,$mode_1_off:x.default,$mode_1_on:b.default,$mode_2_off:w.default,$mode_2_on:E.default,$check_on:D.default,$check_off:A.default}}get option(){return this.danmuku.option}get outside(){return this.template.$mount!==this.template.$controlsCenter}get TEMPLATE(){let{option:e}=this;return`<div class="apd-toggle">${l.default}${d.default}</div><div class="apd-config">${u.default}<div class="apd-config-panel"><div class="apd-config-panel-inner"><div class="apd-config-mode">\u{6309}\u{7C7B}\u{578B}\u{5C4F}\u{853D}<div class="apd-modes"><div data-mode="0" class="apd-mode">${f.default}${y.default}<div>\u{6EDA}\u{52A8}</div></div><div data-mode="1" class="apd-mode">${x.default}${b.default}<div>\u{9876}\u{90E8}</div></div><div data-mode="2" class="apd-mode">${w.default}${E.default}<div>\u{5E95}\u{90E8}</div></div></div></div><div class="apd-config-other"><div class="apd-other apd-anti-overlap">${D.default}${A.default} \u{9632}\u{6B62}\u{5F39}\u{5E55}\u{91CD}\u{53E0}</div><div class="apd-other apd-sync-video">${D.default}${A.default} \u{540C}\u{6B65}\u{89C6}\u{9891}\u{901F}\u{5EA6}</div></div><div class="apd-config-slider apd-config-opacity">\u{4E0D}\u{900F}\u{660E}\u{5EA6}<div class="apd-slider"></div><div class="apd-value">\u{672A}\u{77E5}</div></div><div class="apd-config-slider apd-config-margin">\u{663E}\u{793A}\u{533A}\u{57DF}<div class="apd-slider"></div><div class="apd-value">\u{672A}\u{77E5}</div></div><div class="apd-config-slider apd-config-fontSize">\u{5F39}\u{5E55}\u{5B57}\u{53F7}<div class="apd-slider"></div><div class="apd-value">\u{672A}\u{77E5}</div></div><div class="apd-config-slider apd-config-speed">\u{5F39}\u{5E55}\u{901F}\u{5EA6}<div class="apd-slider"></div><div class="apd-value">\u{672A}\u{77E5}</div></div></div></div></div><div class="apd-emitter"><div class="apd-style">${m.default}<div class="apd-style-panel"><div class="apd-style-panel-inner"><div class="apd-style-mode">\u{6A21}\u{5F0F}<div class="apd-modes"><div data-mode="0" class="apd-mode">${y.default}<div>\u{6EDA}\u{52A8}</div></div><div data-mode="1" class="apd-mode">${b.default}<div>\u{9876}\u{90E8}</div></div><div data-mode="2" class="apd-mode">${E.default}<div>\u{5E95}\u{90E8}</div></div></div></div><div class="apd-style-color">\u{989C}\u{8272}<div class="apd-colors">${this.COLOR.map(e=>`<div data-color="${e}" class="apd-color" style="background-color: ${e}"></div>`).join("")}</div></div></div></div></div><input class="apd-input" placeholder="\u{53D1}\u{4E2A}\u{53CB}\u{5584}\u{7684}\u{5F39}\u{5E55}\u{89C1}\u{8BC1}\u{5F53}\u{4E0B}" autocomplete="off" maxLength="${e.maxLength}" /><div class="apd-send">\u{53D1}\u{9001}</div></div>`}get OPACITY(){return{min:0,max:100,steps:[],...this.option.OPACITY}}get FONT_SIZE(){return{min:12,max:120,steps:[],...this.option.FONT_SIZE}}get MARGIN(){return{min:0,max:3,steps:[{name:"1/4",value:[10,"75%"]},{name:"半屏",value:[10,"50%"]},{name:"3/4",value:[10,"25%"]},{name:"满屏",value:[10,10]}],...this.option.MARGIN}}get SPEED(){return{min:0,max:4,steps:[{name:"极慢",value:10},{name:"较慢",value:7.5,hide:!0},{name:"适中",value:5},{name:"较快",value:2.5,hide:!0},{name:"极快",value:1}],...this.option.SPEED}}get COLOR(){return this.option.COLOR.length?this.option.COLOR:["#FE0302","#FF7204","#FFAA02","#FFD302","#FFFF00","#A0EE00","#00CD00","#019899","#4266BE","#89D5FF","#CC0273","#222222","#9B9B9B","#FFFFFF"]}query(e){let{query:t}=this.utils,{$danmuku:i}=this.template;return t(e,i)}append(e,t){let{append:i}=this.utils;[...e.children].some(e=>e===t)||i(e,t)}setData(e,t){let{$player:i}=this.art.template,{$mount:a}=this.template;i.dataset[e]=t,this.outside&&(a.dataset[e]=t)}createTemplate(){let{createElement:e,tooltip:t}=this.utils,i=e("div");i.className="artplayer-plugin-danmuku",i.innerHTML=this.TEMPLATE,this.template.$danmuku=i,this.template.$toggle=this.query(".apd-toggle"),this.template.$config=this.query(".apd-config"),this.template.$configPanel=this.query(".apd-config-panel"),this.template.$configModes=this.query(".apd-config-mode .apd-modes"),this.template.$style=this.query(".apd-style"),this.template.$stylePanel=this.query(".apd-style-panel"),this.template.$styleModes=this.query(".apd-style-mode .apd-modes"),this.template.$colors=this.query(".apd-colors"),this.template.$antiOverlap=this.query(".apd-anti-overlap"),this.template.$syncVideo=this.query(".apd-sync-video"),this.template.$opacitySlider=this.query(".apd-config-opacity .apd-slider"),this.template.$opacityValue=this.query(".apd-config-opacity .apd-value"),this.template.$marginSlider=this.query(".apd-config-margin .apd-slider"),this.template.$marginValue=this.query(".apd-config-margin .apd-value"),this.template.$fontSizeSlider=this.query(".apd-config-fontSize .apd-slider"),this.template.$fontSizeValue=this.query(".apd-config-fontSize .apd-value"),this.template.$speedSlider=this.query(".apd-config-speed .apd-slider"),this.template.$speedValue=this.query(".apd-config-speed .apd-value"),this.template.$input=this.query(".apd-input"),this.template.$send=this.query(".apd-send");let{$toggle:a}=this.template;this.art.on("artplayerPluginDanmuku:show",()=>{t(a,"关闭弹幕")}),this.art.on("artplayerPluginDanmuku:hide",()=>{t(a,"打开弹幕")})}createEvents(){let{$toggle:e,$configModes:t,$styleModes:i,$colors:a,$antiOverlap:n,$syncVideo:o,$send:s,$input:l}=this.template;this.art.proxy(e,"click",()=>{this.danmuku.config({visible:!this.option.visible}),this.reset()}),this.art.proxy(t,"click",e=>{let t=e.target.closest(".apd-mode");if(!t)return;let i=Number(t.dataset.mode);this.option.modes.includes(i)?this.danmuku.config({modes:this.option.modes.filter(e=>e!==i)}):this.danmuku.config({modes:[...this.option.modes,i]}),this.reset()}),this.art.proxy(n,"click",()=>{this.danmuku.config({antiOverlap:!this.option.antiOverlap}),this.reset()}),this.art.proxy(o,"click",()=>{this.danmuku.config({synchronousPlayback:!this.option.synchronousPlayback}),this.reset()}),this.art.proxy(i,"click",e=>{let t=e.target.closest(".apd-mode");if(!t)return;let i=Number(t.dataset.mode);this.danmuku.config({mode:i}),this.reset()}),this.art.proxy(a,"click",e=>{let t=e.target.closest(".apd-color");t&&(this.danmuku.config({color:t.dataset.color}),this.reset())}),this.art.proxy(s,"click",()=>this.emit()),this.art.proxy(l,"keypress",e=>{"Enter"===e.key&&(e.preventDefault(),this.emit())})}createSliders(){this.slider.opacity=this.createSlider({...this.OPACITY,container:this.template.$opacitySlider,findIndex:()=>Math.round(100*this.option.opacity),onChange:e=>{let{$opacityValue:t}=this.template;t.textContent=`${e}%`,this.danmuku.config({opacity:e/100})}}),this.slider.margin=this.createSlider({...this.MARGIN,container:this.template.$marginSlider,findIndex:()=>this.MARGIN.steps.findIndex(e=>e.value[0]===this.option.margin[0]&&e.value[1]===this.option.margin[1]),onChange:e=>{let t=this.MARGIN.steps[e];if(!t)return;let{$marginValue:i}=this.template;i.textContent=t.name,this.danmuku.config({margin:t.value})}}),this.slider.fontSize=this.createSlider({...this.FONT_SIZE,container:this.template.$fontSizeSlider,findIndex:()=>this.danmuku.fontSize,onChange:e=>{let{$fontSizeValue:t}=this.template;t.textContent=`${e}px`,e!==this.danmuku.fontSize&&this.danmuku.config({fontSize:e})}}),this.slider.speed=this.createSlider({...this.SPEED,container:this.template.$speedSlider,findIndex:()=>this.SPEED.steps.findIndex(e=>e.value===this.option.speed),onChange:e=>{let t=this.SPEED.steps[e];if(!t)return;let{$speedValue:i}=this.template;i.textContent=t.name,this.danmuku.config({speed:t.value})}})}createSlider({min:e,max:t,container:i,findIndex:a,onChange:n,steps:o=[]}){let{query:s,clamp:l}=this.utils;i.innerHTML=`<div class="apd-slider-line"><div class="apd-slider-points">${o.map(()=>'<div class="apd-slider-point"></div>').join("")}</div><div class="apd-slider-progress"></div></div><div class="apd-slider-dot"></div><div class="apd-slider-steps">${o.map(e=>e.hide?"":`<div class="apd-slider-step">${e.name}</div>`).join("")}</div>`;let r=s(".apd-slider-dot",i),d=s(".apd-slider-progress",i),p=!1;function u(i=a()){if(i<e||i>t)return;let s=(i-e)/(t-e);r.style.left=`${100*s}%`,0===o.length&&(d.style.width=r.style.left),n(i)}function h(a){let{left:n,width:o}=i.getBoundingClientRect();u(Math.round(l(a.clientX-n,0,o)/o*(t-e)+e))}return this.art.proxy(i,"click",e=>{h(e)}),this.art.proxy(i,"mousedown",e=>{p=0===e.button}),this.art.on("document:mousemove",e=>{p&&h(e)}),this.art.on("document:mouseup",e=>{p&&(p=!1,h(e))}),{reset:u}}onFullscreen(e){let{$danmuku:t,$controlsCenter:i,$mount:a}=this.template;this.outside?e?this.append(i,t):this.append(a,t):this.append(i,t)}onMouseEnter({$control:e,$panel:t}){let{$player:i}=this.art.template,a=e.getBoundingClientRect(),n=t.getBoundingClientRect(),o=i.getBoundingClientRect(),s=n.width/2-a.width/2,l=o.left-(a.left-s),r=a.right+s-o.right;l>0?t.style.left=`${-s+l}px`:r>0?t.style.left=`${-s-r}px`:t.style.left=`${-s}px`}async emit(){let{$input:e}=this.template,t=e.value.trim();if(!t.length||this.isLock||this.emitting)return;let i={text:t,mode:this.option.mode,color:this.option.color,time:this.art.currentTime};try{this.emitting=!0;let t=await this.option.beforeEmit(i);if(this.emitting=!1,!0!==t)return;i.border=!0,delete i.time,this.danmuku.emit(i),e.value="",this.lock()}catch(e){this.emitting=!1}}lock(){let{addClass:e}=this.utils,{$send:t}=this.template;this.isLock=!0;let i=this.option.lockTime;t.innerText=i,e(t,"apd-lock");let a=()=>{this.timer=setTimeout(()=>{0===i?this.unlock():(i-=1,t.innerText=i,a())},1e3)};a()}unlock(){let{removeClass:e}=this.utils,{$send:t}=this.template;clearTimeout(this.timer),this.isLock=!1,t.innerText="发送",e(t,"apd-lock")}resize(){if(this.outside||this.art.fullscreen||this.art.fullscreenWeb)return;let{$player:e,$controlsCenter:t}=this.art.template,{$danmuku:i}=this.template;this.art.width<this.option.width?this.append(e,i):this.append(t,i)}reset(){let{inverseClass:e,tooltip:t}=this.utils,{$toggle:i,$colors:a}=this.template;this.slider.opacity.reset(),this.slider.margin.reset(),this.slider.fontSize.reset(),this.slider.speed.reset(),this.setData("danmukuVisible",this.option.visible),this.setData("danmukuMode",this.option.mode),this.setData("danmukuColor",this.option.color),this.setData("danmukuMode0",this.option.modes.includes(0)),this.setData("danmukuMode1",this.option.modes.includes(1)),this.setData("danmukuMode2",this.option.modes.includes(2)),this.setData("danmukuAntiOverlap",this.option.antiOverlap),this.setData("danmukuSyncVideo",this.option.synchronousPlayback),this.setData("danmukuTheme",this.option.theme),this.setData("danmukuEmitter",this.option.emitter);let n=Array.from(a.children).find(e=>e.dataset.color===this.option.color.toUpperCase());n&&e(n,"apd-active"),t(i,this.option.visible?"关闭弹幕":"打开弹幕"),this.resize()}mount(e){let{errorHandle:t}=this.utils,i="string"==typeof e?document.querySelector(e):e;t(i,`Can not find the mount point: ${e}`),this.append(i,this.template.$danmuku),this.template.$mount=i,this.reset()}},"undefined"!=typeof document){let e="artplayer-plugin-danmuku",t=document.getElementById(e);if(t)t.textContent=o.default;else{let t=document.createElement("style");t.id=e,t.textContent=o.default,document.head.appendChild(t)}}},{"bundle-text:./style.less":"hViDo","bundle-text:./img/on.svg":"9pjcf","bundle-text:./img/off.svg":"b2dkP","bundle-text:./img/config.svg":"l8tyy","bundle-text:./img/style.svg":"5iZC3","bundle-text:./img/mode_0_off.svg":"i0Vut","bundle-text:./img/mode_0_on.svg":"hOSvZ","bundle-text:./img/mode_1_off.svg":"bOXC3","bundle-text:./img/mode_1_on.svg":"lKuh0","bundle-text:./img/mode_2_off.svg":"eB8W6","bundle-text:./img/mode_2_on.svg":"bpe2E","bundle-text:./img/check_on.svg":"kL9zy","bundle-text:./img/check_off.svg":"22xpM","@parcel/transformer-js/src/esmodule-helpers.js":"9pCYc"}],hViDo:[function(e,t,i){t.exports='.artplayer-plugin-danmuku{z-index:99;color:#fff;flex-shrink:0;justify-content:center;align-items:center;gap:10px;width:100%;height:32px;font-size:12px;font-weight:300;display:flex;position:relative}.artplayer-plugin-danmuku .apd-icon{cursor:pointer;opacity:.75;fill:#fff;transition:all .2s}.artplayer-plugin-danmuku .apd-icon:hover{opacity:1}.artplayer-plugin-danmuku .apd-config{display:flex;position:relative}.artplayer-plugin-danmuku .apd-config .apd-config-panel{opacity:0;pointer-events:none;width:320px;padding:10px;position:absolute;bottom:24px;left:0}.artplayer-plugin-danmuku .apd-config .apd-config-panel .apd-config-panel-inner{background-color:#000000d9;border-radius:3px;width:100%;padding:10px}.artplayer-plugin-danmuku .apd-config:hover .apd-config-panel{opacity:100;pointer-events:all}.artplayer-plugin-danmuku .apd-config-mode,.artplayer-plugin-danmuku .apd-config-slider,.artplayer-plugin-danmuku .apd-config-other,.artplayer-plugin-danmuku .apd-style-mode{margin-bottom:15px}.artplayer-plugin-danmuku .apd-modes{align-items:center;gap:20px;margin-top:5px;display:flex}.artplayer-plugin-danmuku .apd-modes .apd-mode{cursor:pointer;text-align:center}.artplayer-plugin-danmuku .apd-modes .apd-mode:hover{color:#00a1d6}.artplayer-plugin-danmuku .apd-config-slider{align-items:center;gap:12px;display:flex}.artplayer-plugin-danmuku .apd-config-slider .apd-value{text-align:right;width:32px}.artplayer-plugin-danmuku .apd-slider{cursor:pointer;flex:1;justify-content:center;align-items:center;height:20px;display:flex;position:relative}.artplayer-plugin-danmuku .apd-slider .apd-slider-line{background-color:#ffffff40;border-radius:3px;width:100%;height:2px;position:relative;overflow:hidden}.artplayer-plugin-danmuku .apd-slider .apd-slider-points{justify-content:space-between;align-items:center;display:flex;position:absolute;inset:0}.artplayer-plugin-danmuku .apd-slider .apd-slider-points .apd-slider-point{background-color:#ffffff80;border-radius:50%;width:2px;height:2px}.artplayer-plugin-danmuku .apd-slider .apd-slider-progress{background-color:#00a1d6;width:0%;height:100%}.artplayer-plugin-danmuku .apd-slider .apd-slider-dot{background-color:#00a1d6;border-radius:50%;width:12px;height:12px;position:absolute;left:0%;transform:translate(-6px)}.artplayer-plugin-danmuku .apd-slider .apd-slider-steps{color:#777;justify-content:space-between;align-items:center;width:calc(100% + 32px);display:flex;position:absolute;bottom:-12px}.artplayer-plugin-danmuku .apd-slider .apd-slider-steps .apd-slider-step{text-align:center;flex-shrink:0;width:36px;scale:.95}.artplayer-plugin-danmuku .apd-config-other{align-items:center;gap:20px;display:flex}.artplayer-plugin-danmuku .apd-config-other .apd-check-off,.artplayer-plugin-danmuku .apd-config-other .apd-check-on{width:16px;height:16px}.artplayer-plugin-danmuku .apd-config-other .apd-other{cursor:pointer;align-items:center;gap:2px;display:flex}.artplayer-plugin-danmuku .apd-config-other .apd-other:hover{color:#00a1d6}.artplayer-plugin-danmuku .apd-emitter{background-color:#ffffff40;border-radius:5px;flex:1;align-items:center;height:100%;display:flex}.artplayer-plugin-danmuku .apd-style{justify-content:center;align-items:center;display:flex;position:relative}.artplayer-plugin-danmuku .apd-style .apd-style-panel{opacity:0;pointer-events:none;width:200px;padding:10px;position:absolute;bottom:24px;left:0}.artplayer-plugin-danmuku .apd-style .apd-style-panel .apd-style-panel-inner{background-color:#000000d9;border-radius:3px;width:100%;padding:10px}.artplayer-plugin-danmuku .apd-style:hover .apd-style-panel{opacity:100;pointer-events:all}.artplayer-plugin-danmuku .apd-colors{flex-wrap:wrap;gap:8px;margin-top:5px;display:flex}.artplayer-plugin-danmuku .apd-colors .apd-color{cursor:pointer;border-radius:2px;width:16px;height:16px}.artplayer-plugin-danmuku .apd-colors .apd-color.apd-active{border:1px solid #000;box-shadow:0 0 0 1px #fff}.artplayer-plugin-danmuku .apd-input{color:#fff;background-color:#0000;border:none;outline:none;flex:1;width:auto;min-width:0;height:100%;line-height:1}.artplayer-plugin-danmuku .apd-input::placeholder{color:#ffffff80}.artplayer-plugin-danmuku .apd-send{cursor:pointer;text-shadow:none;background-color:#00a1d6;border-top-right-radius:5px;border-bottom-right-radius:5px;flex-shrink:0;justify-content:center;align-items:center;width:60px;height:100%;display:flex}.artplayer-plugin-danmuku .apd-send.apd-lock{cursor:not-allowed;color:#666;background-color:#e7e7e7}.art-controls-center .apd-emitter{flex:none;width:260px}.art-fullscreen .artplayer-plugin-danmuku,.art-fullscreen-web .artplayer-plugin-danmuku{gap:16px;height:38px}.art-fullscreen .artplayer-plugin-danmuku .apd-config-icon,.art-fullscreen-web .artplayer-plugin-danmuku .apd-config-icon,.art-fullscreen .artplayer-plugin-danmuku .apd-toggle-off,.art-fullscreen-web .artplayer-plugin-danmuku .apd-toggle-off,.art-fullscreen .artplayer-plugin-danmuku .apd-toggle-on,.art-fullscreen-web .artplayer-plugin-danmuku .apd-toggle-on{width:28px;height:28px}.art-fullscreen .artplayer-plugin-danmuku .apd-emitter,.art-fullscreen-web .artplayer-plugin-danmuku .apd-emitter{flex:none;width:400px}.art-video-player>.artplayer-plugin-danmuku{padding:0 10px;position:absolute;bottom:-40px;left:0;right:0}.art-video-player:has(>.artplayer-plugin-danmuku){margin-bottom:40px}[data-danmuku-emitter=false] .apd-emitter{display:none!important}[data-danmuku-emitter=false] .art-controls-center .artplayer-plugin-danmuku{justify-content:flex-end;gap:18px}[data-danmuku-emitter=false].art-fullscreen .art-controls-center .artplayer-plugin-danmuku,[data-danmuku-emitter=false].art-fullscreen-web .art-controls-center .artplayer-plugin-danmuku{gap:24px}[data-danmuku-theme=light]>.artplayer-plugin-danmuku .apd-icon{fill:#333}[data-danmuku-theme=light]>.artplayer-plugin-danmuku .apd-emitter{background-color:#f1f2f3}[data-danmuku-theme=light]>.artplayer-plugin-danmuku .apd-input{color:#000}[data-danmuku-theme=light]>.artplayer-plugin-danmuku .apd-input::placeholder{color:#0000004d}[data-danmuku-visible=false] .apd-toggle-off{display:block}[data-danmuku-visible=false] .apd-toggle-on,[data-danmuku-visible=true] .apd-toggle-off{display:none}[data-danmuku-visible=true] .apd-toggle-on{display:block}[data-danmuku-anti-overlap=false] .apd-anti-overlap .apd-check-on{display:none}[data-danmuku-anti-overlap=false] .apd-anti-overlap .apd-check-off,[data-danmuku-anti-overlap=true] .apd-anti-overlap .apd-check-on{display:block}[data-danmuku-anti-overlap=true] .apd-anti-overlap .apd-check-off,[data-danmuku-sync-video=false] .apd-sync-video .apd-check-on{display:none}[data-danmuku-sync-video=false] .apd-sync-video .apd-check-off,[data-danmuku-sync-video=true] .apd-sync-video .apd-check-on{display:block}[data-danmuku-sync-video=true] .apd-sync-video .apd-check-off{display:none}[data-danmuku-mode0=false] .apd-config-mode .apd-mode-0-off{display:block}[data-danmuku-mode0=false] .apd-config-mode .apd-mode-0-on{display:none}[data-danmuku-mode0=false] .art-danmuku [data-mode="0"]{opacity:0!important}[data-danmuku-mode0=true] .apd-config-mode .apd-mode-0-off{display:none}[data-danmuku-mode0=true] .apd-config-mode .apd-mode-0-on{display:block}[data-danmuku-mode="0"] .apd-style-mode [data-mode="0"]{color:#00a1d6}[data-danmuku-mode="0"] .apd-style-mode [data-mode="0"] path{fill:#00a1d6}[data-danmuku-mode1=false] .apd-config-mode .apd-mode-1-off{display:block}[data-danmuku-mode1=false] .apd-config-mode .apd-mode-1-on{display:none}[data-danmuku-mode1=false] .art-danmuku [data-mode="1"]{opacity:0!important}[data-danmuku-mode1=true] .apd-config-mode .apd-mode-1-off{display:none}[data-danmuku-mode1=true] .apd-config-mode .apd-mode-1-on{display:block}[data-danmuku-mode="1"] .apd-style-mode [data-mode="1"]{color:#00a1d6}[data-danmuku-mode="1"] .apd-style-mode [data-mode="1"] path{fill:#00a1d6}[data-danmuku-mode2=false] .apd-config-mode .apd-mode-2-off{display:block}[data-danmuku-mode2=false] .apd-config-mode .apd-mode-2-on{display:none}[data-danmuku-mode2=false] .art-danmuku [data-mode="2"]{opacity:0!important}[data-danmuku-mode2=true] .apd-config-mode .apd-mode-2-off{display:none}[data-danmuku-mode2=true] .apd-config-mode .apd-mode-2-on{display:block}[data-danmuku-mode="2"] .apd-style-mode [data-mode="2"]{color:#00a1d6}[data-danmuku-mode="2"] .apd-style-mode [data-mode="2"] path{fill:#00a1d6}'},{}],"9pjcf":[function(e,t,i){t.exports='<svg class="apd-icon apd-toggle-on" xmlns="http://www.w3.org/2000/svg" data-pointer="none" viewBox="0 0 24 24" width="24" height="24"><path fill-rule="evenodd" d="M11.989 4.828c-.47 0-.975.004-1.515.012l-1.71-2.566a1.008 1.008 0 0 0-1.678 1.118l.999 1.5c-.681.018-1.403.04-2.164.068a4.013 4.013 0 0 0-3.83 3.44c-.165 1.15-.245 2.545-.245 4.185 0 1.965.115 3.67.35 5.116a4.012 4.012 0 0 0 3.763 3.363l.906.046c1.205.063 1.808.095 3.607.095a.988.988 0 0 0 0-1.975c-1.758 0-2.339-.03-3.501-.092l-.915-.047a2.037 2.037 0 0 1-1.91-1.708c-.216-1.324-.325-2.924-.325-4.798 0-1.563.076-2.864.225-3.904.14-.977.96-1.713 1.945-1.747 2.444-.087 4.465-.13 6.063-.131 1.598 0 3.62.044 ***********.034 1.71.81 1.855 1.814.075.524.113 1.962.141 3.065v.002c.01.342.017.65.025.88a.987.987 0 1 0 1.974-.068c-.008-.226-.016-.523-.025-.856v-.027c-.03-1.118-.073-2.663-.16-3.276-.273-1.906-1.783-3.438-3.74-3.507-.9-.032-1.743-.058-2.531-.078l1.05-1.46a1.008 1.008 0 0 0-1.638-1.177l-1.862 2.59c-.38-.004-.744-.007-1.088-.007h-.13Zm.521 4.775h-1.32v4.631h2.222v.847h-2.618v1.078h2.618l.003.678c.36.026.714.163 1.01.407h.11v-1.085h2.694v-1.078h-2.695v-.847H16.8v-4.63h-1.276a8.59 8.59 0 0 0 .748-1.42L15.183 7.8a14.232 14.232 0 0 1-.814 1.804h-1.518l.693-.308a8.862 8.862 0 0 0-.814-1.408l-1.045.352c.297.396.572.847.825 1.364Zm-4.18 3.564.154-1.485h1.98V8.294h-3.2v.98H9.33v1.43H7.472l-.308 3.453h2.277c0 1.166-.044 1.925-.12 2.277-.078.352-.386.528-.936.528-.308 0-.616-.022-.902-.055l.297 1.067.062.005c.285.02.551.04.818.04 1.001-.067 1.562-.419 1.694-1.057.11-.638.176-1.903.176-3.795h-2.2Zm7.458.11v-.858h-1.254v.858h1.254Zm-2.376-.858v.858h-1.199v-.858h1.2Zm-1.199-.946h1.2v-.902h-1.2v.902Zm2.321 0v-.902h1.254v.902h-1.254Z" clip-rule="evenodd"/><path fill="#00AEEC" fill-rule="evenodd" d="M22.846 14.627a1 1 0 0 0-1.412.075l-5.091 5.703-2.216-2.275-.097-.086-.008-.005a1 1 0 0 0-1.322 1.493l2.963 *************.007.005a1 1 0 0 0 1.354-.124l5.81-6.505.08-.102.005-.008a1 1 0 0 0-.166-1.295Z" clip-rule="evenodd"/></svg>'},{}],b2dkP:[function(e,t,i){t.exports='<svg class="apd-icon apd-toggle-off" xmlns="http://www.w3.org/2000/svg" data-pointer="none" viewBox="0 0 24 24" width="24" height="24"><path fill-rule="evenodd" d="m8.085 4.891-.999-1.499a1.008 1.008 0 0 1 1.679-1.118l1.709 2.566c.54-.008 1.045-.012 1.515-.012h.13c.345 0 .707.003 1.088.007l1.862-2.59a1.008 1.008 0 0 1 1.637 1.177l-1.049 1.46c.788.02 1.631.046 2.53.078 1.958.069 3.468 1.6 3.74 3.507.088.613.13 2.158.16 3.276l.001.027c.01.333.017.63.025.856a.987.987 0 0 1-1.974.069c-.008-.23-.016-.539-.025-.881v-.002c-.028-1.103-.066-2.541-.142-3.065-.143-1.004-.895-1.78-1.854-1.813a179.14 179.14 0 0 0-6.064-.131c-1.598 0-3.619.044-6.063.13a2.037 2.037 0 0 0-1.945 1.748c-.15 1.04-.225 2.341-.225 3.904 0 1.874.11 3.474.325 4.798.154.949.95 1.66 1.91 1.708a97.58 97.58 0 0 0 5.416.139.988.988 0 0 1 0 1.975 99.78 99.78 0 0 1-5.513-.141A4.012 4.012 0 0 1 2.197 17.7c-.236-1.446-.351-3.151-.351-5.116 0-1.64.08-3.035.245-4.184A4.013 4.013 0 0 1 5.92 4.96c.761-.027 1.483-.05 2.164-.069Zm4.436 4.707h-1.32v4.63h2.222v.848h-2.618v1.078h2.431a5.01 5.01 0 0 1 3.575-3.115V9.598h-1.276a8.59 8.59 0 0 0 .748-1.42l-1.089-.384a14.232 14.232 0 0 1-.814 1.804h-1.518l.693-.308a8.862 8.862 0 0 0-.814-1.408l-1.045.352c.297.396.572.847.825 1.364Zm-4.18 3.564.154-1.485h1.98V8.289h-3.2v.979h2.067v1.43H7.483l-.308 3.454h2.277c0 1.166-.044 1.925-.12 2.277-.078.352-.386.528-.936.528-.308 0-.616-.022-.902-.055l.297 1.067.062.004c.285.02.551.04.818.04 1.001-.066 1.562-.418 1.694-1.056.11-.638.176-1.903.176-3.795h-2.2Zm7.458.11v-.858h-1.254v.858H15.8Zm-2.376-.858v.858h-1.199v-.858h1.2Zm-1.199-.946h1.2v-.902h-1.2v.902Zm2.321 0v-.902H15.8v.902h-1.254Zm3.517 10.594a4 4 0 1 0 0-8 4 4 0 0 0 0 8Zm-.002-1.502a2.5 2.5 0 0 1-2.217-3.657l3.326 3.398a2.49 2.49 0 0 1-1.109.259Zm2.5-2.5c0 .42-.103.815-.286 1.162l-3.328-3.401a2.5 2.5 0 0 1 3.614 2.239Z" clip-rule="evenodd"/></svg>'},{}],l8tyy:[function(e,t,i){t.exports='<svg class="apd-icon apd-config-icon" xmlns="http://www.w3.org/2000/svg" data-pointer="none" viewBox="0 0 24 24" width="24" height="24"><path fill-rule="evenodd" d="m15.645 4.881 1.06-1.473a.998.998 0 1 0-1.622-1.166L13.22 4.835a110.67 110.67 0 0 0-1.1-.007h-.131c-.47 0-.975.004-1.515.012L8.783 2.3A.998.998 0 0 0 7.12 3.408l.988 1.484c-.688.019-1.418.042-2.188.069a4.013 4.013 0 0 0-3.83 3.44c-.165 1.15-.245 2.545-.245 4.185 0 1.965.115 3.67.35 5.116a4.012 4.012 0 0 0 3.763 3.363 99.78 99.78 0 0 0 5.513.141.988.988 0 0 0 0-1.975 97.58 97.58 0 0 1-5.416-.139 2.037 2.037 0 0 1-1.91-1.708c-.216-1.324-.325-2.924-.325-4.798 0-1.563.076-2.864.225-3.904.14-.977.96-1.713 1.945-1.747 2.444-.087 4.465-.13 6.063-.131 1.598 0 3.62.044 ***********.034 1.71.81 1.855 1.814.075.524.113 1.962.141 3.065v.002c.*************.014-.038.004-.096.008-.189.011-.081a.987.987 0 1 0 1.974-.069c-.004-.105-.007-.009-.011.09a2.175 2.175 0 0 1-.007.135l-.002.01a.574.574 0 0 1-.005-.091v-.027c-.03-1.118-.073-2.663-.16-3.276-.273-1.906-1.783-3.438-3.74-3.507-.905-.032-1.752-.058-2.543-.079Zm-3.113 4.703h-1.307v4.643h2.2v.04l.651-1.234c.113-.215.281-.389.482-.509v-.11h.235c.137-.049.283-.074.433-.074h1.553V9.584h-1.264a8.5 8.5 0 0 0 .741-1.405l-1.078-.381c-.24.631-.501 1.23-.806 1.786h-1.503l.686-.305a8.613 8.613 0 0 0-.806-1.394l-1.034.348c.294.392.566.839.817 1.35Zm-1.7 5.502h2.16l-.564 1.068h-1.595v-1.068Zm-2.498-1.863.152-1.561h1.96V8.289H7.277v.969h2.048v1.435h-1.84l-.306 3.51h2.254c0 1.155-.043 1.906-.12 2.255-.076.348-.38.523-.925.523-.305 0-.61-.022-.893-.055l.294 1.056.061.005c.282.02.546.039.81.039.991-.065 1.547-.414 1.677-1.046.11-.631.175-1.883.175-3.757H8.334Zm5.09-.8v.85h-1.188v-.85h1.187Zm-1.188-.955h1.187v-.893h-1.187v.893Zm2.322.007v-.893h1.241v.893h-1.241Zm.528 2.757a1.26 1.26 0 0 1 1.087-.627l4.003-.009a1.26 1.26 0 0 1 1.094.63l1.721 2.982c.226.39.225.872-.001 1.263l-1.743 3a1.26 1.26 0 0 1-1.086.628l-4.003.009a1.26 1.26 0 0 1-1.094-.63l-1.722-2.982a1.26 1.26 0 0 1 .002-1.263l1.742-3Zm1.967.858a1.26 1.26 0 0 0-1.08.614l-.903 1.513a1.26 1.26 0 0 0-.002 1.289l.885 1.492c.227.384.64.62 1.086.618l2.192-.005a1.26 1.26 0 0 0 1.08-.615l.904-1.518a1.26 1.26 0 0 0 .001-1.288l-.884-1.489a1.26 1.26 0 0 0-1.086-.616l-2.193.005Zm2.517 2.76a1.4 1.4 0 1 1-2.8 0 1.4 1.4 0 0 1 2.8 0Z" clip-rule="evenodd"/></svg>'},{}],"5iZC3":[function(e,t,i){t.exports='<svg class="apd-icon apd-style-icon" xmlns="http://www.w3.org/2000/svg" xml:space="preserve" data-pointer="none" style="enable-background:new 0 0 22 22" viewBox="0 0 22 22" width="36" height="24"><path d="M17 16H5c-.55 0-1 .45-1 1s.45 1 1 1h12c.55 0 1-.45 1-1s-.45-1-1-1zM6.96 15c.39 0 .74-.24.89-.6l.65-1.6h5l.66 1.6c.*********.89.6.69 0 1.15-.71.88-1.34l-3.88-8.97C11.87 4.27 11.46 4 11 4s-.87.27-1.05.69l-3.88 8.97c-.27.63.2 1.34.89 1.34zM11 5.98 12.87 11H9.13L11 5.98z"/></svg>'},{}],i0Vut:[function(e,t,i){t.exports='<svg class="apd-icon apd-mode-0-off" xmlns="http://www.w3.org/2000/svg" xml:space="preserve" data-pointer="none" style="enable-background:new 0 0 28 28" viewBox="0 0 28 28" width="28" height="28"><path d="M23 15c1.487 0 2.866.464 4 1.255V7a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v14a4 4 0 0 0 4 4h11.674A7 7 0 0 1 23 15zM11 9h6a1 1 0 0 1 0 2h-6a1 1 0 0 1 0-2zm-3 2H6V9h2v2zm4 4h-2v-2h2v2zm2-1a1 1 0 0 1 1-1h1a1 1 0 0 1 0 2h-1a1 1 0 0 1-1-1z" fill="#00AEEC"/><path d="M26.536 18.464a5 5 0 0 0-7.071 0 5 5 0 0 0 0 7.071 5 5 0 1 0 7.071-7.071zm-5.657 5.657a3 3 0 0 1-.586-3.415l4.001 4.001a3 3 0 0 1-3.415-.586zm4.829-.827-4.001-4.001a3.002 3.002 0 0 1 4.001 4.001z" fill="#00AEEC"/></svg>'},{}],hOSvZ:[function(e,t,i){t.exports='<svg class="apd-icon apd-mode-0-on" xmlns="http://www.w3.org/2000/svg" xml:space="preserve" data-pointer="none" style="enable-background:new 0 0 28 28" viewBox="0 0 28 28" width="28" height="28"><path d="M23 3H5a4 4 0 0 0-4 4v14a4 4 0 0 0 4 4h18a4 4 0 0 0 4-4V7a4 4 0 0 0-4-4zM11 9h6a1 1 0 0 1 0 2h-6a1 1 0 0 1 0-2zm-3 2H6V9h2v2zm4 4h-2v-2h2v2zm9 0h-6a1 1 0 0 1 0-2h6a1 1 0 0 1 0 2z" fill="#FFF"/></svg>'},{}],bOXC3:[function(e,t,i){t.exports='<svg class="apd-icon apd-mode-1-off" xmlns="http://www.w3.org/2000/svg" xml:space="preserve" data-pointer="none" style="enable-background:new 0 0 28 28" viewBox="0 0 28 28" width="28" height="28"><path d="M23 15c1.487 0 2.866.464 4 1.255V7a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v14a4 4 0 0 0 4 4h11.674A7 7 0 0 1 23 15zm-4-8h2v2h-2V7zM9 9H7V7h2v2zm4 0h-2V7h2v2zm2-2h2v2h-2V7z" fill="#00AEEC"/><path d="M26.536 18.464a5 5 0 0 0-7.071 0 5 5 0 0 0 0 7.071 5 5 0 1 0 7.071-7.071zm-5.657 5.657a3 3 0 0 1-.586-3.415l4.001 4.001a3 3 0 0 1-3.415-.586zm4.829-.827-4.001-4.001a3.002 3.002 0 0 1 4.001 4.001z" fill="#00AEEC"/></svg>'},{}],lKuh0:[function(e,t,i){t.exports='<svg class="apd-icon apd-mode-1-on" xmlns="http://www.w3.org/2000/svg" xml:space="preserve" data-pointer="none" style="enable-background:new 0 0 28 28" viewBox="0 0 28 28" width="28" height="28"><path d="M23 3H5a4 4 0 0 0-4 4v14a4 4 0 0 0 4 4h18a4 4 0 0 0 4-4V7a4 4 0 0 0-4-4zM9 9H7V7h2v2zm4 0h-2V7h2v2zm4 0h-2V7h2v2zm4 0h-2V7h2v2z" fill="#FFF"/></svg>'},{}],eB8W6:[function(e,t,i){t.exports='<svg class="apd-icon apd-mode-2-off" xmlns="http://www.w3.org/2000/svg" xml:space="preserve" data-pointer="none" style="enable-background:new 0 0 28 28" viewBox="0 0 28 28" width="28" height="28"><path d="M23 15c1.487 0 2.866.464 4 1.255V7a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v14a4 4 0 0 0 4 4h11.674A7 7 0 0 1 23 15zM9 21H7v-2h2v2zm4 0h-2v-2h2v2z" fill="#00AEEC"/><path d="M26.536 18.464a5 5 0 0 0-7.071 0 5 5 0 0 0 0 7.071 5 5 0 1 0 7.071-7.071zm-5.657 5.657a3 3 0 0 1-.586-3.415l4.001 4.001a3 3 0 0 1-3.415-.586zm4.829-.827-4.001-4.001a3.002 3.002 0 0 1 4.001 4.001z" fill="#00AEEC"/></svg>'},{}],bpe2E:[function(e,t,i){t.exports='<svg class="apd-icon apd-mode-2-on" xmlns="http://www.w3.org/2000/svg" xml:space="preserve" data-pointer="none" style="enable-background:new 0 0 28 28" viewBox="0 0 28 28" width="28" height="28"><path d="M23 3H5a4 4 0 0 0-4 4v14a4 4 0 0 0 4 4h18a4 4 0 0 0 4-4V7a4 4 0 0 0-4-4zM9 21H7v-2h2v2zm4 0h-2v-2h2v2zm4 0h-2v-2h2v2zm4 0h-2v-2h2v2z" fill="#FFF"/></svg>'},{}],kL9zy:[function(e,t,i){t.exports='<svg class="apd-icon apd-check-on" xmlns="http://www.w3.org/2000/svg" data-pointer="none" viewBox="0 0 32 32" width="32" height="32"><path d="m13 18.25-1.8-1.8c-.6-.6-1.65-.6-2.25 0s-.6 1.5 0 2.25l2.85 2.85c.318.318.762.468 1.2.448.438.02.882-.13 1.2-.448l8.85-8.85c.6-.6.6-1.65 0-2.25s-1.65-.6-2.25 0l-7.8 7.8zM8 4h16c2.21 0 4 1.79 4 4v16c0 2.21-1.79 4-4 4H8c-2.21 0-4-1.79-4-4V8c0-2.21 1.79-4 4-4z" fill="#00AEEC"/></svg>'},{}],"22xpM":[function(e,t,i){t.exports='<svg class="apd-icon apd-check-off" xmlns="http://www.w3.org/2000/svg" data-pointer="none" viewBox="0 0 32 32" width="32" height="32"><path d="M8 6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2H8zm0-2h16c2.21 0 4 1.79 4 4v16c0 2.21-1.79 4-4 4H8c-2.21 0-4-1.79-4-4V8c0-2.21 1.79-4 4-4z" fill="#FFF"/></svg>'},{}],"8AxLD":[function(e,t,i){var a=e("@parcel/transformer-js/src/esmodule-helpers.js");a.defineInteropFlag(i),a.export(i,"default",()=>s);let n={map:(e,t,i,a,n)=>(e-t)*(n-a)/(i-t)+a,range(e,t,i){let a=Math.round(e/i)*i;return Array.from({length:Math.floor((t-e)/i)},(e,t)=>t*i+a)}},o=(e,t)=>{let i=t[0]-e[0],a=t[1]-e[1];return{length:Math.sqrt(Math.pow(i,2)+Math.pow(a,2)),angle:Math.atan2(a,i)}};function s(e,t,i){let{query:a}=e.constructor.utils;e.controls.add({name:"heatmap",position:"top",html:"",style:{position:"absolute",top:"-100px",left:"0px",right:"0px",height:"100px",width:"100%",pointerEvents:"none"},mounted(s){let l=null,r=null;function d(p=[]){if(l=null,r=null,s.innerHTML="",!e.duration||e.option.isLive)return;let u={w:s.offsetWidth,h:s.offsetHeight},h={xMin:0,xMax:u.w,yMin:0,yMax:128,scale:.25,opacity:.2,minHeight:Math.floor(.05*u.h),sampling:Math.floor(u.w/100),smoothing:.2,flattening:.2};"object"==typeof i&&Object.assign(h,i);let m=[];if(Array.isArray(p)&&p.length)m=[...p];else{let i=e.duration/u.w;for(let e=0;e<=u.w;e+=h.sampling){let a=t.queue.filter(({time:t})=>t>e*i&&t<=(e+h.sampling)*i).length;m.push([e,a])}}if(0===m.length)return;let c=m[m.length-1],f=c[0],g=c[1];f!==u.w&&m.push([u.w,g]);let y=m.map(e=>e[1]),v=(Math.min(...y)+Math.max(...y))/2;for(let e=0;e<m.length;e++){let t=m[e],i=t[1];t[1]=i*(i>v?1+h.scale:1-h.scale)+h.minHeight}let x=(e,t,i,a)=>{let s=o(t||e,i||e),l=n.map(Math.cos(s.angle)*h.flattening,0,1,1,0),r=s.angle*l+(a?Math.PI:0),d=s.length*h.smoothing;return[e[0]+Math.cos(r)*d,e[1]+Math.sin(r)*d]},k=(e,t,i)=>{let a=x(i[t-1],i[t-2],e),n=x(e,i[t-1],i[t+1],!0),o=t===i.length-1?" z":"";return`C ${a[0]},${a[1]} ${n[0]},${n[1]} ${e[0]},${e[1]}${o}`},b=m.map(e=>[n.map(e[0],h.xMin,h.xMax,0,u.w),n.map(e[1],h.yMin,h.yMax,u.h,0)]).reduce((e,t,i,a)=>0===i?`M ${a[a.length-1][0]},${u.h} L ${t[0]},${u.h} L ${t[0]},${t[1]}`:`${e} ${k(t,i,a)}`,"");s.innerHTML=`<svg viewBox="0 0 ${u.w} ${u.h}"><defs><linearGradient id="heatmap-solids" x1="0%" y1="0%" x2="100%" y2="0%"><stop offset="0%" style="stop-color:var(--art-theme);stop-opacity:${h.opacity}" /><stop offset="0%" style="stop-color:var(--art-theme);stop-opacity:${h.opacity}" id="heatmap-start" /><stop offset="0%" style="stop-color:var(--art-progress-color);stop-opacity:1" id="heatmap-stop" /><stop offset="100%" style="stop-color:var(--art-progress-color);stop-opacity:1" /></linearGradient></defs><path fill="url(#heatmap-solids)" d="${b}"></path></svg>`,l=a("#heatmap-start",s),r=a("#heatmap-stop",s),l.setAttribute("offset",`${100*e.played}%`),r.setAttribute("offset",`${100*e.played}%`)}e.on("video:timeupdate",()=>{l&&r&&(l.setAttribute("offset",`${100*e.played}%`),r.setAttribute("offset",`${100*e.played}%`))}),e.on("setBar",(e,t)=>{l&&r&&"played"===e&&(l.setAttribute("offset",`${100*t}%`),r.setAttribute("offset",`${100*t}%`))}),e.on("ready",()=>d()),e.on("resize",()=>d()),e.on("artplayerPluginDanmuku:loaded",()=>d()),e.on("artplayerPluginDanmuku:points",e=>d(e))}})}},{"@parcel/transformer-js/src/esmodule-helpers.js":"9pCYc"}]},["bgm6t"],"bgm6t","parcelRequire4dc0");