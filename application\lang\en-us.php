<?php
/*
*英文
*当前语言包是系统全局语言包；自定义模块语言包请在模块的lang目录下创建比如 application/admin/lang/显示的时候会优先于全局包
*全局key：单词可能在系统内任何地方使用
*模块key：开头  admin/ 、 install/ 、只在模块内使用
*后台菜单key：开头menu/
*内部处理key：开头 model/、controller/、只在模块内使用
*/
return [
    'lang_ver'=>'3021+',
    'hello' => 'Welcome to',
    'maccms_name'=>'AppleCMS-v10',
    'maccms_copyright'=>'© MacCMS All Rights Reserved.',

    'vod'=>'Video',
    'art'=>'Article',
    'topic'=>'Topic',
    'comment'=>'Comment',
    'gbook'=>'Guestbook',
    'user'=>'User',
    'label'=>'Custom Page',
    'actor'=>'Actor',
    'role'=>'Role',
    'plot'=>'Episode Plot',
    'website'=>'Website',
    'domain'=>'Domain',
    'or'=>'Or',
    'all'=>'All',
    'open'=>'Open',
    'close'=>'Close',
    'task'=>'Task',
    'status'=>'Status',
    'status_parse'=>'Parsing Status',
    'test'=>'Test',
    'copy'=>'Copy',
    'run'=>'Run',
    'run_ok'=>'Run Successful',
    'skip'=>'Skip',
    'jump'=>'Jump',
    'jump_over'=>'Jump Over',
    'quantity'=>'Quantity',
    'start'=>'Start',
    'end'=>'End',
    'save'=>'Save',
    'level'=>'Recommend',
    'lock'=>'Lock',
    'unlock'=>'Unlock',
    'disable'=>'Disable',
    'enable'=>'Enable',
    'pause'=>'Pause',
    'normal'=>'Normal',
    'abnormal'=>'Abnormal',
    'back_link'=>'Backlink',
    'page_limit'=>'Items Per Page',
    'copyright'=>'Copyright',
    'browse'=>'Browse',
    'favorites'=>'Favorites',
    'want_see'=>'Want to See',
    'play'=>'Play',
    'down'=>'Download',
    'site_name'=>'Site Name',
    'keyword'=>'Keyword',
    'description'=>'Description',
    'data_name'=>'Data Name',
    'return'=>'Return',
    'integral_recharge'=>'Integral Recharge',
    'registration_promotion'=>'Registration Promotion',
    'visit_promotion'=>'Visit Promotion',
    'one_level_distribution'=>'Level 1 Distribution',
    'two_level_distribution'=>'Level 2 Distribution',
    'three_level_distribution'=>'Level 3 Distribution',
    'points_upgrade'=>'Points Upgrade',
    'integral_consumption'=>'Integral Consumption',
    'integral_withdrawal'=>'Integral Withdrawal',
    'not_sale'=>'Not for Sale',
    'sold'=>'Sold',
    'not_used'=>'Not Used',
    'used'=>'Used',
    'not_paid'=>'Not Paid',
    'paid'=>'Paid',
    'slice'=>'Slice',
    'drama'=>'Drama',
    'the'=>'The',
    'episode'=>'Episode',
    'issue'=>'Issue',
    'just'=>'Just Now',
    'day_after_tomorrow'=>'Day After Tomorrow',
    'tomorrow'=>'Tomorrow',
    'year'=>'Year',
    'years'=>'Years',
    'month'=>'Month',
    'day'=>'Day',
    'yes'=>'Yes',
    'not'=>'No',
    'seconds'=>'seconds ago',
    'yesterday'=>'Yesterday',
    'day_before_yesterday'=>'Day Before Yesterday',
    'seconds_ago'=>'seconds ago',
    'minutes_ago'=>'minutes ago',
    'hours_ago'=>'hours ago',
    'continue_in_second'=>'Continue in seconds',
    'audit'=>'Audit',
    'reviewed'=>'Reviewed',
    'reviewed_not'=>'Not Reviewed',
    'last_run_time'=>'Last Run Time',
    'wait_submit'=>'Data Submitting...',
    'wait_time'=>'Wait Time',
    'audit_time'=>'Audit Time',
    'bank'=>'Bank',
    'request_err'=>'Request Failed',
    'group'=>'Member Group',
    'access'=>'Access',
    'test_ok'=>'Test Successful',
    'test_err'=>'Test Failed',
    'browser_jump'=>'If your browser does not automatically jump, please click here',
    'filed_empty'=>'Please enter content',
    'page_auto'=>'Page Auto',
    'param_err'=>'Parameter Error',
    'name_empty'=>'Please enter name',
    'pass_empty'=>'Please enter password',
    'pass_err'=>'Password incorrect',
    'verify_empty'=>'Please enter verification code',
    'verify_err'=>'Verification code incorrect',
    'url_empty'=>'Please enter URL',
    'cancel_level'=>'Cancel Recommendation',
    'select'=>'Select',
    'select_return'=>'Select Return',
    'select_data'=>'Select Data',
    'select_opt'=>'Select Operation',
    'select_level'=>'Select Recommendation',
    'select_type'=>'Select Type',
    'select_status'=>'Select Status',
    'select_pic'=>'Select Picture',
    'select_sort'=>'Select Sort',
    'select_lock'=>'Select Lock',
    'select_sale_status'=>'Select Sale Status',
    'select_use_status'=>'Select Use Status',
    'select_reply_status'=>'Select Reply Status',
    'select_order_status'=>'Select Order Status',
    'select_time'=>'Select Time',
    'select_please'=>'Please Select',
    'select_model'=>'Select Model',
    'select_report'=>'Select Report',
    'select_template'=>'Select Template',
    'select_genre'=>'Select Genre',
    'select_group'=>'Select User Group',
    'select_area'=>'Select Area',
    'default_val'=>'Default Value',
    'related_data'=>'Related Data',
    'detect'=>'Detect',
    'genre'=>'Genre',
    'portrait'=>'Portrait',
    'tpl_dir'=>'Template Directory',
    'ads_dir'=>'Advertisement Directory',
    'reply'=>'Reply',
    'reply_yes'=>'Replied',
    'reply_not'=>'Not Replied',
    'report_yes'=>'Reported',
    'report_not'=>'Not Reported',
    'current'=>'Current',
    'blank'=>'New Window',
    'report'=>'Report',


    'use'=>'Use',
    'the_last_time'=>'Last Time',
    'that_day'=>'That Day',
    'in_a_week'=>'Within a Week',
    'in_a_month'=>'Within a Month',

    'calcel_level'=>'Cancel Recommendation',
    'pic_empty'=>'No Picture',
    'pic_remote'=>'Remote Picture',
    'pic_sync_err'=>'Sync Error Picture',
    'pic_local'=>'Local Picture',
    'pic_sync'=>'Sync Picture',
    'not_pic_sync_err'=>'Non-Error Picture',
    'not_pic_sync_today_err'=>'Non-Error Picture Today',
    'pic_err'=>'Error Picture',

    'sort'=>'Sort',
    'add'=>'Add',
    'edit'=>'Edit',
    'del'=>'Delete',
    'del_confirm'=>'Are you sure you want to delete?',
    'del_multi'=>'Batch Delete',
    'del_data'=>'Delete Data',
    'del_ok'=>'Delete Successful',
    'del_err'=>'Delete Failed',
    'del_empty'=>'Delete is Empty',
    'add_group'=>'Add a Group',

    'id'=>'ID',
    'sub'=>'Subtitle',
    'hits'=>'Hits',
    'hits_all'=>'Total Hits',
    'hits_month'=>'Monthly Hits',
    'hits_week'=>'Weekly Hits',
    'hits_day'=>'Daily Hits',
    'no'=>'Number',
    'area'=>'Area',
    'lang'=>'Language',
    'sex'=>'Sex',
    'sum'=>'Total',
    'opt'=>'Operation',
    'opt_content'=>'Content Operation',
    'name'=>'Name',
    'height'=>'Height',
    'weight'=>'Weight',
    'type'=>'Type',
    'wd'=>'Keyword',
    'slide'=>'Slide',
    'param'=>'Parameter',
    'base_info'=>'Basic Information',
    'other_info'=>'Other Information',
    'male'=>'Male',
    'female'=>'Female',
    'path'=>'Path',
    'actor_name'=>'Actor Name',
    'alias'=>'Alias',
    'en'=>'Pinyin',
    'letter'=>'First Letter',
    'color'=>'Color',
    'blood'=>'Blood Type',
    'birtharea'=>'Place of Birth',
    'birthday'=>'Birthday',
    'starsign'=>'Zodiac Sign',
    'school'=>'Graduated School',
    'view'=>'View',
    'multi_set'=>'Batch Set',
    'multi_separate_tip'=>'Multiple separated by commas',
    'multi_del_ok'=>'Batch delete completed',
    'multi_set_ok'=>'Batch set completed',
    'multi_opt_ok'=>'Batch operation completed',
    'tip'=>'Tip',
    'target'=>'Target Window',
    'start_exec'=>'Start Execution',
    'min_val'=>'Minimum Value',
    'max_val'=>'Maximum Value',

    'vod_name'=>'Video Name',
    'role_name'=>'Role Name',
    'set_ok'=>'Set Successful',
    'set_err'=>'Set Failed',

    'remarks'=>'Remarks',
    'works'=>'Representative Works',
    'serial_num'=>'Serial Number',
    'auto_make'=>'Auto Generate',
    'make_page'=>'Generate Page',
    'make_all'=>'Generate All',

    'class'=>'Extended Category',
    'pic'=>'Picture',
    'pic_thumb'=>'Thumbnail',
    'pic_slide'=>'Poster',
    'pic_screenshot'=>'Screenshot',
    'upload'=>'Upload',
    'upload_pic'=>'Upload Picture',
    'blurb'=>'Brief Introduction',
    'content'=>'Details',
    'blurb_auto_tip'=>'Automatically get the first 100 characters from page 1 details if not filled in',
    'up'=>'Upvote',
    'hate'=>'Downvote',
    'rnd_make'=>'Random Generate',
    'reset_zero'=>'Reset to zero',
    'score'=>'Average Score',
    'score_all'=>'Total Score',
    'score_num'=>'Total Ratings',
    'tpl'=>'Independent Template',
    'jumpurl'=>'Redirect URL',
    'upload_ing'=>'File uploading...',
    'install_ok'=>'Installation Successful',
    'install_err'=>'Installation Failed',
    'uninstall_ok'=>'Uninstallation Successful',
    'uninstall_err'=>'Uninstallation Failed',
    'url'=>'URL',

    'rel_vod'=>'Related Videos',
    'rel_art'=>'Related Articles',

    'opt_ok'=>'Operation Successful',
    'opt_err'=>'Operation Failed',
    'update_ok'=>'Update Successful',
    'update_err'=>'Update Failed',
    'follow_global'=>'Follow Global',

    'btn_save' =>'Save',
    'btn_reset' =>'Reset',
    'btn_search' =>'Search',
    'search_data'=>'Search Data',

    'save_ok'=>'Save Successful!',
    'save_err'=>'Save Failed!',

    'write_err'=>'File write failed, please try again!',
    'write_err_config'=>'Configuration file write failed, please try again!',
    'write_err_database'=>'Database configuration write failed, please try again!',
    'write_err_route'=>'Route configuration write failed, please try again!',
    'wirte_err_codefile'=>'Failed to save code file, please try again!',
    'import_err'=>'Import failed, please check the file format',
    'import_ok'=>'Import completed',
    'import'=>'Import',
    'import_all'=>'Import All',
    'export'=>'Export',
    'code'=>'Code',
    'sms_not_config'=>'SMS sending service not configured',
    'email_not_config'=>'Email sending service not configured',
    'phone_format_err'=>'Incorrect phone number format',
    'email_format_err'=>'Incorrect email format',
    'format_err'=>'Format error',
    'title_not_empty'=>'Title cannot be empty',
    'body_not_empty'=>'Body cannot be empty',
    'tpl_not'=>'Template ID cannot be empty',
    'sms_not'=>'SMS sending method not found',
    'email_not'=>'Email sending method not found',

    'counting_points'=>'Counting Points',
    'counting_times'=>'Counting Times',
    'counting_ips'=>'Counting IPs',

    'mobile'=>'Mobile',
    'email'=>'Email',
    'verify'=>'Verification Code',
    'account'=>'Account',
    'local_app'=>'Local App',
    'online_app'=>'Online Store',
    'local_setup'=>'Offline Installation',
    'bind_no'=>'Bind Account',
    'bind'=>'Bind',
    'unbind'=>'Unbind',

    'install'=>'Install',
    'uninstall'=>'Uninstall',
    'detail'=>'Detail',
    'config'=>'Configuration',
    'author'=>'Author',
    'intro'=>'Introduction',
    'ver'=>'Version',
    'time'=>'Time',

    'update_time'=>'Update Time',
    'add_time'=>'Add Time',
    'use_time'=>'Usage Time',
    'cj_time'=>'Crawl Time',
    'reply_time'=>'Reply Time',
    'log_time'=>'Log Time',
    'reg_time'=>'Registration Time',
    'related'=>'Related',

    'card_no'=>'Card Number',
    'money'=>'Amount',
    'rule'=>'Rule',
    'mixing'=>'Mixing',
    'number'=>'Number',
    'abc'=>'Alphabet',

    'seo_key'=>'SEO Keywords',
    'seo_des'=>'SEO Description',
    'seo_title'=>'SEO Title',
    'transfer'=>'Transfer',
    'parent_type_id'=>'Parent Category ID',
    'type_id'=>'Category ID',
    'type_name'=>'Category Name',
    'last_login_time'=>'Last Login Time',
    'last_login_ip'=>'Last Login IP',
    'login_num'=>'Login Attempts',
    'popedom'=>'Permissions',
    'check_all'=>'Select All',
    'check_other'=>'Invert Selection',
    'pass'=>'Password',
    'clear_confirm'=>'Confirm to clear data?',
    'audit_confirm'=>'Confirm to audit data?',
    'blacklist_keywords' => 'Blacklist keywords',
    'blacklist_ip' => 'Blacklist IP',

    'clear'=>'Clear',
    'del_auto_keep_min'=>'One-click Delete Duplicates [Keep Small ID]',
    'del_auto_keep_max'=>'One-click Delete Duplicates [Keep Large ID]',
    'update_repeat_cache'=>'Update cache',
    'num_id'=>'Numeric ID',
    'encode_id'=>'Encrypted ID',
    'vod_id'=>'Video ID',
    'art_id'=>'Article ID',
    'type_id'=>'Category ID',
    'topic_id'=>'Topic ID',
    'actor_id'=>'Actor ID',
    'role_id'=>'Role ID',
    'website_id'=>'Website ID',
    'extend_class'=>'Extended Category',
    'extend_area'=>'Extended Area',
    'extend_lang'=>'Extended Language',
    'extend_year'=>'Extended Year',
    'page_title'=>'Page Title',
    'page_note'=>'Page Note',
    'page_content'=>'Page Content',
    'page_add'=>'Add Page Content',
    'from'=>'From',
    'paging'=>'Pagination',
    'referer'=>'Referrer',
    'access_pwd'=>'Access Password',
    'pwd_url'=>'Password Link',
    'pwd_play'=>'Playback Page Password',
    'pwd_detail'=>'Content Page Password',
    'play_group'=>'Playback Group',
    'down_group'=>'Download Group',

    'pwd_down'=>'Download Page Password',
    'not_static_is_ok'=>'Available in non-static mode',
    'points'=>'Points',
    'points_all'=>'Total Points',
    'points_play'=>'Play Points',
    'points_down'=>'Download Points',
    'points_detail'=>'Per Page Points',
    'model'=>'Model',
    'total'=>'Total',
    'nickname'=>'Nickname',
    'data_needs_processed'=>'Data needs to be processed',
    'per_page'=>'Per Page',
    'data'=>'Data',
    'page'=>'Page',
    'processing'=>'Processing',

    'permission_denied'=>'You do not have permission to access this page',
    'illegal_request'=>'Illegal Request',
    'token_err'=>'Please do not submit the form repeatedly',
    'dir'=>'Directory',
    'file'=>'File',
    'file_name'=>'File Name',
    'file_size'=>'File Size',
    'file_time'=>'File Time',
    'file_des'=>'File Description',
    'occupies'=>'Occupies',
    'space'=>'Space',
    'return_parent_dir'=>'Return to Parent Directory',
    'phone'=>'Phone',
    'server_rest'=>'Let the server rest for a while, continue later',
    'to'=>'To',
    'director'=>'Director',
    'clear_ok'=>'Clear Successful',
    'clear_err'=>'Clear Failed, please try again',
    'unknown'=>'Unknown',
    'unknown_type'=>'Unknown Type',
    'obtain_ok'=>'Obtain Successful',
    'obtain_err'=>'Obtain Failed',
    'download_ok'=>'Download Successful',
    'download_err'=>'Download Failed',
    'expand_all'=>'Expand All',
    'fold_all'=>'Collapse All',
    'today_data'=>'Today\'s Data',
    'no_make_data'=>'Data Not Generated',
    'get_info_err'=>'Failed to get information',
    'rnd_data'=>'Random Data',
    'success'=>'Success',
    'diy_ids'=>'Custom IDs',
    'fail'=>'Fail',
    'duplicate_data'=>'Duplicate Data',
    'distinct_into'=>'Distinct into Database',
    'comment_name'=>'Comment Name',
    'comment_content'=>'Comment Content',

    'page_not_found'=>'Page Not Found',
    'search_close'=>'Search function is currently closed',
    'show_close'=>'Filter page function is currently closed',
    'ajax_close'=>'Ajax page function is currently closed',
    'frequently'=>'Please do not operate frequently',
    'search_frequently'=>'Please do not operate frequently, search interval is',
    'score_ok'=>'Thank you for your participation, scoring successful',
    'suggest_close'=>'Suggestion search function is currently closed',

    'please_try_again'=>'Please try again',
    'data_list'=>'Data list',
    'data_not_found'=>'Failed to retrieve data',
    'unverified'=>'Unverified',
    'verified'=>'Verified',
    'registered'=>'Registered',
    'register'=>'Register',
    'findpass'=>'Retrieve Password',
    'access_or_pass_err'=>'Account or password error',
    'playurl'=>'Playback URL',
    'downurl'=>'Download URL',
    'serial'=>'Serial Number',
    'writer'=>'Writer',
    'version'=>'Resource Version',
    'state'=>'Resource Category',
    'tv'=>'TV Channel',
    'weekday'=>'Program Cycle',
    'isend'=>'End',
    'total'=>'Total Episodes',
    'replace'=>'Replace',
    'merge'=>'Merge',
    'douban_id'=>'Douban ID',
    'rel_name'=>'Related Data Name',
    'preview'=>'Preview',
    'screenshot_preview'=>'Screenshot Preview',
    'screenshot_tip'=>'One image URL per line, supporting remote URLs, local paths, and custom name notes, for example:
Pic 1$upload/test.jpg
Pic 2$https://www.baidu.com/logo.png
https://www.baidu.com/123.jpg',

    'menu/index'=>'Home',
    'menu/welcome'=>'Welcome Page',
    'menu/quickmenu'=>'Custom Menu Configuration',
    'menu/system'=>'System',
    'menu/config'=>'Website Parameter Configuration',
    'menu/configseo'=>'SEO Parameter Configuration',
    'menu/configuser'=>'Member Parameter Configuration',
    'menu/configcomment'=>'Comment Message Configuration',
    'menu/configupload'=>'Attachment Parameter Configuration',
    'menu/configurl'=>'URL Address Configuration',
    'menu/configplay'=>'Player Parameter Configuration',
    'menu/configcollect'=>'Collection Parameter Configuration',
    'menu/configinterface'=>'External Library Integration Configuration',
    'menu/configapi'=>'Open API Configuration',
    'menu/configconnect'=>'Integration Login Configuration',
    'menu/configpay'=>'Online Payment Configuration',
    'menu/configweixin'=>'WeChat Integration Configuration',
    'menu/configemail'=>'Email Sending Configuration',
    'menu/configsms'=>'SMS Sending Configuration',
    'menu/timming'=>'Scheduled Task Configuration',
    'menu/domain'=>'Site Group Management Configuration',
    'menu/base'=>'Basic',
    'menu/type'=>'Category Management',
    'menu/topic'=>'Topic Management',
    'menu/link'=>'Friend Link Management',
    'menu/gbook'=>'Message Management',
    'menu/comment'=>'Comment Management',
    'menu/images'=>'Attachment Management',
    'menu/art'=>'Article',
    'menu/art_data'=>'Article Data',
    'menu/art_add'=>'Add Article',
    'menu/art_data_lock'=>'Locked Articles',
    'menu/art_data_audit'=>'Unaudited Articles',
    'menu/art_batch'=>'Batch Operation Articles',
    'menu/art_repeat'=>'Duplicate Article Data',
    'menu/vod'=>'Video',
    'menu/server'=>'Server Group',
    'menu/player'=>'Player',
    'menu/downer'=>'Downloader',
    'menu/vod_data'=>'Video Data',
    'menu/vod_add'=>'Add Video',
    'menu/vod_data_url_empty'=>'Videos without URL',
    'menu/vod_data_lock'=>'Locked Videos',
    'menu/vod_data_audit'=>'Unaudited Videos',
    'menu/vod_data_points'=>'Videos requiring points',
    'menu/vod_data_plot'=>'Episodic Plot',
    'menu/vod_batch'=>'Batch Operation Videos',
    'menu/vod_repeat'=>'Duplicate Video Data',
    'menu/actor'=>'Actor Library',
    'menu/role'=>'Role Library',
    'menu/website'=>'Website',
    'menu/website_data'=>'Website Data',
    'menu/website_add'=>'Add Website',
    'menu/website_data_lock'=>'Locked Websites',
    'menu/website_data_audit'=>'Unaudited Websites',
    'menu/website_batch'=>'Batch Operation Websites',
    'menu/website_repeat'=>'Duplicate Website Data',
    'menu/users'=>'Users',
    'menu/admin'=>'Administrators',
    'menu/group'=>'Member Groups',
    'menu/user'=>'Members',
    'menu/card'=>'Recharge Cards',
    'menu/order'=>'Member Orders',
    'menu/ulog'=>'Access Logs',
    'menu/plog'=>'Points Logs',
    'menu/cash'=>'Withdrawal Records',
    'menu/templates'=>'Templates',
    'menu/template'=>'Template Management',
    'menu/ads'=>'Ad Position Management',
    'menu/wizard'=>'Tag Wizard',
    'menu/make'=>'Generation',
    'menu/make_opt'=>'Generation Options',
    'menu/make_index'=>'Generate Home Page',
    'menu/make_index_wap'=>'Generate WAP Home Page',
    'menu/make_map'=>'Generate Site Map',
    'menu/cjs'=>'Collection',
    'menu/union'=>'Recommended Resources',
    'menu/collect_timming'=>'Scheduled Hanging',
    'menu/collect'=>'Custom Interface',
    'menu/cj'=>'Custom Rules',
    'menu/db'=>'Database',
    'menu/database'=>'Database Management',
    'menu/database_sql'=>'Execute SQL Statements',
    'menu/database_rep'=>'Data Batch Replacement',
    'menu/database_inspect'=>'Hanging Horse Detection',
    'menu/apps'=>'Applications',
    'menu/addon'=>'Application Market',
    'menu/urlsend'=>'URL Push',
    'menu/safety_file'=>'File Security Detection',
    'menu/safety_data'=>'Data Hanging Detection',

    'model/admin/update_login_err'=>'Failed to update login information',
    'model/admin/login_ok'=>'Login successful',
    'model/admin/logout_ok'=>'Logout successful',
    'model/admin/not_login'=>'Not logged in',
    'model/admin/haved_login'=>'Already logged in',

    'model/card/not_found'=>'Incorrect recharge card information, please try again',
    'model/card/update_user_points_err'=>'Failed to update user points, please try again',
    'model/card/update_card_status_err'=>'Failed to update recharge card status, please try again',
    'model/card/used_card_ok'=>'Recharge successful, added %s points',

    'model/cash/not_open'=>'Withdrawal function is not enabled!',
    'model/cash/min_money_err'=>'Minimum withdrawal amount error!',
    'model/cash/mush_money_err'=>'Withdrawal amount is too much, not enough points!',

    'model/collect/flag_err'=>'Flag identifier error, illegal request!',
    'model/collect/cjurl_err'=>'Collection link error or cannot be a local link',
    'model/collect/get_html_err'=>'Failed to connect to API interface, usually due to unstable server network, IP blocking, or disabled related functions!',
    'model/collect/json_err'=>'Incorrect JSON format, not supported for collection',
    'model/collect/xml_err'=>'Incorrect XML format, not supported for collection',
    'model/collect/data_tip1'=>'Current collection task <strong class="green">%s</strong> / <span class="green">%s</span> pages Collection address&nbsp;%s',
    'model/collect/type_err'=>'Category not bound, skip error',
    'model/collect/name_in_filter_err'=>'Data in filter list, skip error',
    'model/collect/name_err'=>'Incomplete data, skip error',
    'model/collect/not_check_add'=>'Data operation does not check add, skip.',
    'model/collect/not_check_update'=>'Data operation does not check update, skip.',
    'model/collect/add_ok'=>'Added to library, successful ok.',
    'model/collect/uprule_empty'=>'No secondary update items set, skip.',
    'model/collect/data_lock'=>'Data is locked, skip.',
    'model/collect/not_need_update'=>'No update needed.',
    'model/collect/is_over'=>'Data collection completed.',
    'model/collect/not_found_rel_vod'=>'No related video found, cannot associate, skip.',
    'model/collect/not_found_rel_data'=>'No related data found, cannot associate, skip.',
    'model/collect/role_data_require'=>'Incomplete data role_name, role_actor, vod_name required, skip error.',
    'model/collect/actor_data_require'=>'Incomplete data actor_name, actor_sex required, skip error.',
    'model/collect/comment_data_require'=>'Incomplete data comment_content, comment_name, rel_name required, skip error.',
    'model/collect/playurl_same'=>'Same playback address, skip.',
    'model/collect/playfrom_empty'=>'Player type is empty, skip.',
    'model/collect/downurl_same'=>'Same download address, skip.',
    'model/collect/downfrom_empty'=>'Downloader type is empty, skip.',

    'model/collect/playgroup_add_ok'=>'Playback group (%s), added ok.',
    'model/collect/playgroup_same'=>'Playback group (%s), no need to update.',
    'model/collect/playgroup_update_ok'=>'Playback group (%s), updated ok.',

    'model/collect/downgroup_add_ok'=>'Download group (%s), added ok.',
    'model/collect/downgroup_same'=>'Download group (%s), no need to update.',
    'model/collect/downgroup_update_ok'=>'Download group (%s), updated ok.',

    'model/group/have_user'=>'There are users under the user group',
    'model/order/pay_over'=>'Order has been fully paid',
    'model/order/update_status_err'=>'Failed to update order status',
    'model/order/update_user_points_err'=>'Failed to update member points',
    'model/order/pay_ok'=>'Recharge completed, callback function executed successfully',

    'model/type/to_info_err'=>'Failed to get target category information',
    'model/type/move_err'=>'Move failed',
    'model/type/move_ok'=>'Move failed',

    'model/user/not_open_reg'=>'Registration is not open',
    'model/user/input_require'=>'Please fill in required fields',
    'model/user/pass_not_pass2'=>'Password and confirmation password do not match',
    'model/user/haved_reg'=>'Username has been registered, please change',
    'model/user/name_contain'=>'Username can only contain letters and numbers, please change',
    'model/user/name_filter'=>'Username cannot contain characters such as %s, please try again',
    'model/user/ip_limit'=> 'IP limit for registration is %s times per day',
    'model/user/phone_haved'=> 'Phone number is already in use, please change',
    'model/user/email_haved'=> 'Email is already in use, please change',
    'model/user/reg_err'=> 'Registration failed, please try again',
    'model/user/reg_ok'=>'Registration successful, please log in to the member center to complete personal information',
    'model/user/input_old_pass'=> 'Please enter the original password',
    'model/user/old_pass_err'=> 'Original password is incorrect',
    'model/user/pass_not_same_pass2'=> 'The two new passwords do not match',
    'model/user/not_found'=>'Failed to retrieve user information',
    'model/user/update_login_err'=>'Failed to update login information',
    'model/user/update_expire_err'=>'Failed to update member group expiration information',
    'model/user/update_expire_ok'=>'Expiration information updated successfully',
    'model/user/login_ok'=>'Login successful',
    'model/user/logout_ok'=>'Logout successful',
    'model/user/not_login'=>'Not logged in',
    'model/user/haved_login'=>'Already logged in',
    'model/user/findpass_not_found'=>'Failed to retrieve user, account, question, or answer may be incorrect',
    'model/user/findpass_ok'=>'Password retrieval successful',
    'model/user/select_diy_group_err'=>'Please select a custom fee-based member group',
    'model/user/group_not_found'=>'Failed to retrieve member group information',
    'model/user/group_is_close'=>'Member group is closed, cannot upgrade',
    'model/user/potins_not_enough'=>'Insufficient points, cannot upgrade',
    'model/user/update_group_err'=>'Failed to upgrade member group',
    'model/user/update_group_ok'=>'Member group upgraded successfully',
    'model/user/msg_not_found'=>'Verification information error, please try again',
    'model/user/do_not_send_frequently'=>'Please do not send frequently',
    'model/user/msg_send_ok'=>'Verification code sent successfully, please check',
    'model/user/msg_send_err'=>'Failed to send verification code',
    'model/user/update_bind_err'=>'Failed to update user binding information',
    'model/user/update_bind_ok'=>'Binding successful',
    'model/user/update_unbind_ok'=>'Unbinding successful',
    'model/user/pass_length_err'=>'Password must be at least 6 characters',
    'model/user/email_format_err'=>'Invalid email format',
    'model/user/email_err'=>'Invalid email address',
    'model/user/email_host_not_allowed'=>'Email domain is not allowed',
    'model/user/phone_format_err'=>'Invalid phone number format',
    'model/user/phone_err'=>'Invalid phone number',
    'model/user/pass_reset_err'=>'Password reset failed, please try again',
    'model/user/pass_reset_ok'=>'Password reset successful',
    'model/user/id_err'=>'User ID error',
    'model/user/visit_tip'=>'You can only earn %s promotion visit points per day',
    'model/user/visit_err'=>'Failed to insert promotion record, please try again',
    'model/user/visit_ok'=>'Promotion successful',
    'model/user/reward_tip'=>'User [%s, %s] consumes %s points and receives %s points reward',
    'model/user/reward_ok'=>'Distribution commission successful',


    'model/website/refer_max'=> 'Only %s referral records allowed per day',
    'model/website/visit_err'=>'Referral record failed, please try again',
    'model/website/visit_ok'=>'Referral record successful',

    'controller/no_popedom'=>'You do not have permission to access this data, please upgrade your membership',
    'controller/pay_play_points'=>'To watch this data, you need to pay %s points. Confirm payment?',
    'controller/pay_down_points'=>'To download this data, you need to pay %s points. Confirm payment?',
    'controller/in_try_see'=>'Enter trial mode',
    'controller/charge_data'=>'This page contains paid data. Please log in before accessing!',
    'controller/try_see_end'=>'Trial ended. Would you like to pay %s points to watch the full data? You have %s points left. Please recharge first!',
    'controller/not_enough_points'=>'Sorry, viewing this page requires %s points. You have %s points left. Please recharge first!',
    'controller/popedom_ok'=>'Permission verified',
    'controller/an_error_occurred'=>'An error occurred',
    'controller/visitor'=>'Visitor',
    'controller/get_type_err'=>'Failed to retrieve category, please select another category!',

    'index/require_login'=>'Login required to leave a message',
    'index/require_content'=>'Content cannot be empty',
    'index/require_cn'=>'Content must contain Chinese characters, please re-enter',
    'index/mid_err'=>'Model mid error',
    'index/thanks_msg_audit'=>'Thank you, your message will be reviewed soon!',
    'index/thanks_msg'=>'Thank you for your message!',
    'index/blacklist_keyword'=>'Your comment contains sensitive words, please modify it and resubmit!',
    'index/blacklist_ip'=>'Comments are not allowed!',
    'index/blacklist_placeholder'=>'Please enter the blacklist keywords, one keyword per line',
    'index/blacklist_placeholder_ip'=>'Please enter the blacklist IPs, one IP per line. Submissions not in IP format will be filtered out after submission.',
    'index/payment_status'=>'This payment option is not enabled!',
    'index/payment_not'=>'Payment option not found!',
    'index/payment_ok'=>'Payment completed!',
    'index/haved'=>'You have already participated!',
    'index/ok'=>'Operation successful!',
    'index/pwd_repeat'=>'Please do not repeat the verification!',
    'index/pwd_frequently'=>'Please do not request frequently, please try again later!',
    'index/no_login'=>'Not logged in',
    'index/ulog_fee'=>'Fee receipts need to be recorded separately',
    'index/buy_popedom1'=>'You have already purchased this data entry. No need to pay again. Please refresh the page and try again!',
    'index/buy_popedom2'=>'Sorry, failed to update user points information. Please refresh and try again!',
    'index/buy_popedom3'=>'Sorry, viewing this page data requires %s points. You have %s points left. Please recharge first!',
    'index/bind_haved'=>'Account already bound',
    'index/bind_ok'=>'Binding successful',
    'index/logincallback1'=>'Synchronization registration information failed. Please contact the administrator',
    'index/logincallback2'=>'Failed to get third-party user information. Please try again',
    'index/reg_ok'=> 'Registration successful',
    'index/portrait_tip1'=> 'Custom avatar function not enabled',
    'index/portrait_no_upload'=> 'Uploaded file not found (reason: form name may be wrong, default form names are "file" or "imgdata")!',
    'index/portrait_ext'=> 'Upload format not allowed by the system!',
    'index/upload_err'=>'File upload failed!',
    'index/portrait_err'=>'Failed to update member avatar information!',
    'index/portrait_thumb_err'=>'Failed to generate scaled avatar image file!',
    'index/min_pay'=>'Minimum recharge amount cannot be less than %s yuan',
    'index/order_not'=>'Failed to get bill',
    'index/order_payed'=>'This bill has been fully paid',
    'index/page_type'=>'List page',
    'index/page_detail'=>'Content page',
    'index/page_play'=>'Play page',
    'index/page_down'=>'Download page',
    'index/try_see'=>'Trial viewing',

    'admin/public/head/title'=>'Security first, do not disclose the background address - Copyright by Apple CMS Content Management System',
    'admin/public/jump/title'=>'Jump prompt',

    'admin/index/login/title'=>'Background Management Center - Copyright by Apple CMS Content Management System',
    'admin/index/login/tip_welcome'=>'Welcome to use',
    'admin/index/login/tip_sys'=>'System Management',
    'admin/index/login/filed_no'=>'Account',
    'admin/index/login/filed_pass'=>'Password',
    'admin/index/login/filed_verify'=>'Verification Code',
    'admin/index/login/btn_submit'=>'Login Now',
    'admin/index/login/tip_declare'=>'Disclaimer',
    'admin/index/login/tip_declare_txt'=>'This program is open source and permanently free with no built-in data. Please use it under local laws. This program is not responsible for any information content of users during use! Freedom! Equality! Sharing! Open source!',
    'admin/index/login/verify_no'=>'Please enter username',
    'admin/index/login/verify_pass'=>'Please enter password',
    'admin/index/login/verify_verify'=>'Please enter verification code',

    'admin/index/index/name' =>'Super Console',
    'admin/index/index/menu_switch' =>'Toggle Left Navigation',
    'admin/index/index/menu_index' =>'Website Home',
    'admin/index/index/menu_lock' =>'Lock Screen',
    'admin/index/index/menu_logout' =>'Log Out',
    'admin/index/index/menu_cache' =>'Cache',
    'admin/index/index/menu_cache_clear' =>'Clear Cache',
    'admin/index/index/menu_welcome' =>'Welcome Page',
    'admin/index/index/menu_opt' =>'Operation',
    'admin/index/index/menu_close_all' =>'Close All',
    'admin/index/index/menu_close_other' =>'Close Others',
    'admin/index/index/menu_max' =>'Up to 10 tabs can be opened',
    'admin/index/index/menu_close_empty' =>'No more windows can be closed @_@',

    'admin/index/quickmenu/name' =>'Custom Quick Menu',
    'admin/index/quickmenu/tip' =>'Format requirements: 1. Menu name, menu link address; 2. Each quick menu occupies one line; <br>
    1. Supports remote addresses, for example: Update log,//www.baidu.com/ <br>
    2. Supports plugin files, for example: Plugin file menu,/application/xxxx.html <br>
    3. Supports system modules, for example: Article management,art/data <br>
    4. Supports line separators, for example: Separator,###',

    'admin/index/welcome/filed_os' =>'Operating Environment',
    'admin/index/welcome/filed_host' =>'Server IP/Port',
    'admin/index/welcome/filed_php_ver' =>'PHP Version',
    'admin/index/welcome/filed_thinkphp_ver' =>'ThinkPHP Version',
    'admin/index/welcome/filed_max_upload' =>'Maximum Upload Limit',
    'admin/index/welcome/filed_date' =>'Server Date',
    'admin/index/welcome/filed_ver' =>'Program Version',
    'admin/index/welcome/filed_license' =>'Authorization Type',
    'admin/index/welcome/tip_update_db' =>'Database Update Prompt',
    'admin/index/welcome/tip_update_db_txt' =>'Prompt, found local database upgrade script? Would you like to execute the upgrade operation! After completion, the script will be automatically deleted!',
    'admin/index/welcome/tip_update_go'=>'[Click to upgrade database script]',
    'admin/index/welcome/filed_login_num' =>'Login Times',
    'admin/index/welcome/filed_last_login_ip' =>'Last Login IP',
    'admin/index/welcome/filed_last_login_time' =>'Last Login Time',
    'admin/index/welcome/tip_warn' =>'Please do not modify system files to avoid upgrade failures! This program does not include any built-in data. Adding any data is a personal action! Please use the program under local laws, otherwise, the consequences will be at your own risk!',

    'admin/index/quick_tit'=>'↓↓↓ Custom Menu Area ↓↓↓',
    'admin/index/title'=>'Background Management Center',
    'admin/index/welcome/title'=>'Welcome Page',
    'admin/index/quickmenu/title'=>'Quick Menu Configuration',
    'admin/index/cache_data'=>'Found cached configuration, please clean it up in time...',
    'admin/index/clear_ok'=>'Cache cleared successfully',
    'admin/index/clear_err'=>'Failed to clear cache',
    'admin/index/iframe'=>'Layout switch successful, redirecting',
    'admin/index/pass_err'=>'Incorrect password',
    'admin/index/unlock_ok'=>'Unlocked successfully',
    'admin/index/title'=>'Background Management Center',


    'admin/system/config/title'=>'Website Parameter Configuration',
    'admin/system/config/base'=>'Basic Settings',
    'admin/system/config/performance'=>'Performance Optimization',
    'admin/system/config/parameters'=>'Reserved Parameters',
    'admin/system/config/backstage'=>'Background Settings',
    'admin/system/config/site_name'=>'Website Name',
    'admin/system/config/site_url'=>'Website Domain',
    'admin/system/config/site_url_tip'=>'Example: www.test.com, do not include http://',
    'admin/system/config/site_wapurl'=>'Mobile Site Domain',
    'admin/system/config/site_wapurl_tip'=>'Example: wap.test.com, do not include http://',
    'admin/system/config/site_keywords'=>'Keywords',
    'admin/system/config/site_description'=>'Description',
    'admin/system/config/site_icp'=>'ICP Record Number',
    'admin/system/config/site_qq'=>'Customer Service QQ',
    'admin/system/config/site_email'=>'Customer Service Email',
    'admin/system/config/install_dir'=>'Installation Directory',


    'admin/system/config/install_dir_tip'=>'Root directory "/" or subdirectory "/maccms/", and so on.',
    'admin/system/config/site_logo'=>'Default Logo',
    'admin/system/config/site_waplogo'=>'Mobile Site Logo',
    'admin/system/config/template_dir'=>'Website Template',
    'admin/system/config/site_polyfill'=>'Compatibility with Old Versions',
    'admin/system/config/site_polyfill_tip'=>'Enable to include polyfill for compatibility with old version browsers.',
    'admin/system/config/site_logo_tip'=>'Image URL or path',
    'admin/system/config/html_dir'=>'Template Directory',
    'admin/system/config/mob_status'=>'Adaptive Mobile',
    'admin/system/config/mob_status_tip'=>'Multi-domain: Accessing the WAP domain automatically uses the mobile template; Single-domain: Mobile access automatically uses the mobile template;',
    'admin/system/config/mob_template_dir'=>'Mobile Template',
    'admin/system/config/mob_one'=>'Single Domain',
    'admin/system/config/mob_multiple'=>'Multi Domain',
    'admin/system/config/site_tj'=>'Statistics Code',
    'admin/system/config/site_status'=>'Site Status',
    'admin/system/config/site_close_tip'=>'Closure Prompt',
    'admin/system/config/pathinfo_depr'=>'PATH Separator',
    'admin/system/config/pathinfo_depr_tip'=>'PATHINFO separator. Changing it will affect non-static mode URL addresses.',
    'admin/system/config/xg'=>'Slash /',
    'admin/system/config/zhx'=>'Hyphen -',
    'admin/system/config/xhx'=>'Underscore _',
    'admin/system/config/suffix'=>'Page Suffix',

    'admin/system/config/wall_filter'=>'Fake Wall Defense',
    'admin/system/config/wall_unicode'=>'Encoding Method',
    'admin/system/config/wall_blank'=>'Blank Method',
    'admin/system/config/wall_filter_tip'=>'Enable to encode or replace parameters passed to some pages to solve fake wall threats.',
    'admin/system/config/popedom_filter'=>'Data Permission Filtering',
    'admin/system/config/popedom_filter_tip'=>'Enable to hide categories and data without permission.',
    'admin/system/config/cache_type'=>'Cache Type',
    'admin/system/config/cache_host'=>'Server',
    'admin/system/config/cache_port'=>'Port',
    'admin/system/config/cache_username'=>'Username',
    'admin/system/config/cache_password'=>'Password',
    'admin/system/config/cache_host_tip'=>'Cache server IP',
    'admin/system/config/cache_port_tip'=>'Cache server port',
    'admin/system/config/cache_username_tip'=>'Cache service account, leave blank if none',
    'admin/system/config/cache_password_tip'=>'Cache service password, leave blank if none',
    'admin/system/config/cache_test'=>'Test Connection',

    'admin/system/config/cache_flag'=>'Cache Identifier',
    'admin/system/config/cache_flag_tip'=>'When using memcache or redis on the same server for multiple sites, distinguish them here.',
    'admin/system/config/cache_flag_auto'=>'Leave blank to auto-generate',
    'admin/system/config/cache_core'=>'Data Cache',
    'admin/system/config/cache_time'=>'Data Cache Time',
    'admin/system/config/cache_time_tip'=>'In seconds, recommended to set above 3600',
    'admin/system/config/cache_page'=>'Page Cache',
    'admin/system/config/cache_time_page'=>'Page Cache Time',
    'admin/system/config/compress'=>'Compress Page',
    'admin/system/config/search'=>'Search Switch',
    'admin/system/config/search_verify'=>'Search Captcha',
    'admin/system/config/search_timespan'=>'Search Interval',
    'admin/system/config/search_timespan_tip'=>'In seconds, recommended to set above 3 seconds',
    'admin/system/config/search_len'=>'Search Parameter Length',
    'admin/system/config/search_len_tip'=>'Length limit for single parameter on search and filter pages, default 10 characters, automatically truncated if exceeded.',
    'admin/system/config/404'=>'404 Page',
    'admin/system/config/404_tip'=>'Custom 404 page, place in the "public" directory of the template without extension, default to "jump".',
    'admin/system/config/show'=>'Filter Page Switch',
    'admin/system/config/show_verify'=>'Filter Captcha',
    'admin/system/config/input_type'=>'Parameter Acquisition Method',
    'admin/system/config/input_type_tip'=>'Recommended to use GET method, secure and easy to analyze logs.',
    'admin/system/config/ajax_page'=>'Reserved AJAX Switch',
    'admin/system/config/ajax_page_tip'=>'Reserve corresponding AJAX methods for each page request of the system, recommended not to close. For example: vod/search and vod/ajax_search',
    'admin/system/config/search_vod_rule'=>'Video Search Rules',
    'admin/system/config/search_rule_tip'=>'Note: Only affects "wd" parameter, selecting too many will affect performance, recommended within 3.',
    'admin/system/config/search_art_rule'=>'Article Search Rules',
    'admin/system/config/vod_search_optimise'=>'Video Search Optimization',
    'admin/system/config/vod_search_optimise/frontend'=>'Frontend',
    'admin/system/config/vod_search_optimise/collect'=>'Collect',
    'admin/system/config/vod_search_optimise_tip'=>'Cache LIKE fuzzy query results',
    'admin/system/config/vod_search_optimise_cache_minutes'=>'Search Cache Minutes',
    'admin/system/config/vod_search_optimise_cache_minutes_tip'=>'Video LIKE fuzzy query cache time (minutes), minimum 1 minute, recommended above 60 minutes.',
    'admin/system/config/copyright_status'=>'Copyright Prompt',
    'admin/system/config/copyright_msg'=>'Prompt Message',
    'admin/system/config/copyright_jump_detail'=>'Detail Page Jump',
    'admin/system/config/copyright_jump_play'=>'Play Page Jump',
    'admin/system/config/copyright_jump_iframe'=>'Iframe Play Page Jump',
    'admin/system/config/copyright_notice'=>'Copyright Notice Information',
    'admin/system/config/browser_junmp'=>'Anti-blocking Jump',
    'admin/system/config/browser_junmp_tip'=>'Visiting via WeChat, QQ will directly display jump prompt page.',
    'admin/system/config/collect_timespan'=>'Collection Interval',
    'admin/system/config/collect_timespan_tip'=>'In seconds, recommended to set above 3 seconds.',
    'admin/system/config/pagesize'=>'Backend Pagesize',
    'admin/system/config/pagesize_tip'=>'Data displayed per page, usually set to around 20.',
    'admin/system/config/lang'=>'Backend Language Pack',


    'admin/system/config/makesize'=>'Generate per page',
    'admin/system/config/makesize_tip'=>'Batch generate a few pages each time, usually set to around 20.',
    'admin/system/config/admin_login_verify'=>'Admin Login Captcha',
    'admin/system/config/editor'=>'Rich Text Editor',
    'admin/system/config/editor_tip'=>'The system comes with ueditor by default. For others, please download the extension package from the official website first.',
    'admin/system/config/player_sort'=>'Player Order',
    'admin/system/config/player_sort_tip'=>'The order of players displayed on the frontend.',
    'admin/system/config/global'=>'Global',
    'admin/system/config/encrypt'=>'Encrypt Address',
    'admin/system/config/encrypt_not'=>'Not Encrypt',
    'admin/system/config/search_hot'=>'Search Hot Words',
    'admin/system/config/art_extend_class'=>'Article Extended Categories',
    'admin/system/config/vod_extend_class'=>'Video Extended Categories',
    'admin/system/config/vod_extend_state'=>'Video Resources',
    'admin/system/config/vod_extend_version'=>'Video Versions',
    'admin/system/config/vod_extend_weekday'=>'Video Release Days',
    'admin/system/config/vod_extend_area'=>'Video Regions',
    'admin/system/config/vod_extend_lang'=>'Video Languages',
    'admin/system/config/vod_extend_year'=>'Video Years',
    'admin/system/config/actor_extend_area'=>'Actor Regions',
    'admin/system/config/filter_words'=>'Word Filtering',
    'admin/system/config/filter_words_tip'=>'Disabled words used in search parameters, comments, and messages; separated by commas.',
    'admin/system/config/extra_var'=>'Custom Parameters',
    'admin/system/config/extra_var_tip'=>'One variable per line, for example: aa$$$I am Mr. Wang; Template call method $GLOBALS[\'config\'][\'extra\'][\'aa\']',
    'admin/system/config/test_err'=>'Error occurred, please check if the extension library and configuration items are enabled!',

    'admin/system/configapi/title'=>'Collection Interface API Configuration',
    'admin/system/configapi/vod'=>'Video API Settings',
    'admin/system/configapi/art'=>'Article API Settings',
    'admin/system/configapi/actor'=>'Actor API Settings',
    'admin/system/configapi/role'=>'Role API Settings',
    'admin/system/configapi/website'=>'Website API Settings',
    'admin/system/configapi/vod_tip'=>'Prompt:<br>
                        1. Video list URL /api.php/provide/vod/?ac=list<br>
                        2. Video detail URL /api.php/provide/vod/?ac=detail',
    'admin/system/configapi/status'=>'Interface Switch',
    'admin/system/configapi/charge'=>'Chargeable',
    'admin/system/configapi/detail_inc_hits'=>'Increase Hits',
    'admin/system/configapi/detail_inc_hits_tip'=>'ac=detail and only one ids, hits +1',
    'admin/system/configapi/pagesize'=>'Number of items per page',
    'admin/system/configapi/pagesize_tip'=>'Number of items per page, not recommended to exceed 50.',
    'admin/system/configapi/imgurl'=>'Image Domain',
    'admin/system/configapi/imgurl_tip'=>'Complete access path for displaying images, starting with http(s):, ending with /, excluding the upload directory.',
    'admin/system/configapi/typefilter'=>'Category Filter Parameters',
    'admin/system/configapi/typefilter_tip'=>'List the category IDs to display, for example: 11,12,13',
    'admin/system/configapi/datafilter'=>'Data Filter Parameters',
    'admin/system/configapi/datafilter_tip'=>'SQL query conditions, for example: vod_status=1',
    'admin/system/configapi/datafilter_tip_art'=>'SQL query conditions, for example: art_status=1',
    'admin/system/configapi/datafilter_tip_actor'=>'SQL query conditions, for example: actor_status=1',
    'admin/system/configapi/datafilter_tip_role'=>'SQL query conditions, for example: role_status=1',
    'admin/system/configapi/datafilter_tip_website'=>'SQL query conditions, for example: website_status=1',
    'admin/system/configapi/cachetime'=>'Data Cache Time',
    'admin/system/configapi/cachetime_tip'=>'Data cache time',
    'admin/system/configapi/from'=>'Specify Playback Group',
    'admin/system/configapi/from_tip'=>'Separate multiple with comma, e.g., youku,iqiyi,qvod',
    'admin/system/configapi/auth'=>'Authorization Domain',
    'admin/system/configapi/art_tip'=>'Prompt:<br>
                        1. Article list URL /api.php/provide/art/?ac=list<br>
                        2. Article detail URL /api.php/provide/art/?ac=detail',
    'admin/system/configapi/actor_tip'=>'Prompt:<br>
                        1. Actor list URL /api.php/provide/actor/?ac=list<br>
                        2. Actor detail URL /api.php/provide/actor/?ac=detail',
    'admin/system/configapi/role_tip'=>'Prompt:<br>
                        1. Role list URL /api.php/provide/role/?ac=list<br>
                        2. Role detail URL /api.php/provide/role/?ac=detail',
    'admin/system/configapi/website_tip'=>'Prompt:<br>
                        1. Website list URL /api.php/provide/website/?ac=list<br>
                        2. Website detail URL /api.php/provide/website/?ac=detail',





    'admin/system/configcollect/title'=>'Collection Parameter Configuration',
    'admin/system/configcollect/vod'=>'Video Collection Settings',
    'admin/system/configcollect/art'=>'Article Collection Settings',
    'admin/system/configcollect/actor'=>'Actor Collection Settings',
    'admin/system/configcollect/role'=>'Role Collection Settings',
    'admin/system/configcollect/website'=>'Website Collection Settings',
    'admin/system/configcollect/comment'=>'Comment Collection Settings',
    'admin/system/configcollect/status'=>'Data Status',
    'admin/system/configcollect/words'=>'Collection Thesaurus Settings',
    'admin/system/configcollect/hits_rnd'=>'Random Hits',
    'admin/system/configcollect/updown_rnd'=>'Random Upvotes and Downvotes',
    'admin/system/configcollect/score_rnd'=>'Random Scores',
    'admin/system/configcollect/sync_pic'=>'Auto Sync Images',
    'admin/system/configcollect/auto_tag'=>'Auto Generate TAGs',
    'admin/system/configcollect/class_filter'=>'Extended Category Optimization',
    'admin/system/configcollect/class_filter_tip'=>'Automatically filter the words "[片,剧]" in extended category names; for example, "动作片" becomes "动作"; "欧美剧" becomes "欧美".',
    'admin/system/configcollect/psename'=>'Name Synonym Replacement',
    'admin/system/configcollect/psename_tip'=>'Automatically replace name synonyms to reduce duplication. For example, "第1季" becomes "第一季".',
    'admin/system/configcollect/psernd'=>'Random Insert Statements in Details',
    'admin/system/configcollect/psesyn'=>'Synonym Replacement in Details',
    'admin/system/configcollect/pseplayer'=>'Player Synonym Replacement',
    'admin/system/configcollect/psearea'=>'Area Synonym Replacement',
    'admin/system/configcollect/pselang'=>'Language Synonym Replacement',
    'admin/system/configcollect/inrule'=>'Duplicate Storage Rules',
    'admin/system/configcollect/inrule_tip_role'=>'For video module, prioritize Douyin ID and video name during import.',
    'admin/system/configcollect/inrule_tip_comment'=>'For related data name or Douyin ID, prioritize Douyin ID during import (Douyin ID only effective in video module).',
    'admin/system/configcollect/uprule'=>'Secondary Update Rules',
    'admin/system/configcollect/filter'=>'Data Filtering',
    'admin/system/configcollect/urlrole'=>'Secondary URL Update Rules',
// 'admin/system/configcollect/urlrole_tip'=>'When updating the URL for the second time and encountering the same type of player. Replace: Keep only the newly submitted address. Merge: Integrate the original address and new address to remove duplicates. Prioritize more episodes: Preferentially use the one with more episodes in the two resources.',
    'admin/system/configcollect/urlrole_tip'=>'When updating the URL for the second time and encountering the same type of player. Replace: Keep only the newly submitted address. Merge: Integrate the original address and new address to remove duplicates.',
    'admin/system/configcollect/content'=>'Details',
    'admin/system/configcollect/playurl'=>'Playback URL',
    'admin/system/configcollect/downurl'=>'Download URL',
    'admin/system/configcollect/words_tip'=>'Thesaurus: One per line, no empty lines, format: before=after, # is not allowed.<br>
                    Random Words: Generally about 20 lines, can be jokes, stories, or include hyperlinks.<br>
                    This feature affects collection performance, do not add too many entries at once. Appropriate pseudo-original can help search engine indexing.<br>
                    Disable word library function in [Collection Parameter Settings].',
    'admin/system/configcollect/vod_namewords'=>'Video Name Thesaurus',
    'admin/system/configcollect/vod_thesaurus'=>'Video Details Thesaurus',
    'admin/system/configcollect/vod_playerwords'=>'Video Player Thesaurus',
    'admin/system/configcollect/vod_areawords'=>'Video Area Thesaurus',
    'admin/system/configcollect/vod_langwords'=>'Video Language Thesaurus',
    'admin/system/configcollect/vod_words'=>'Video Details Random Words',
    'admin/system/configcollect/art_thesaurus'=>'Article Details Thesaurus',
    'admin/system/configcollect/art_words'=>'Article Details Random Words',
    'admin/system/configcollect/actor_thesaurus'=>'Actor Details Thesaurus',
    'admin/system/configcollect/actor_words'=>'Actor Details Random Words',
    'admin/system/configcollect/role_thesaurus'=>'Role Details Thesaurus',
    'admin/system/configcollect/role_words'=>'Role Details Random Words',
    'admin/system/configcollect/website_thesaurus'=>'Website Details Thesaurus',
    'admin/system/configcollect/website_words'=>'Website Details Random Words',
    'admin/system/configcollect/comment_thesaurus'=>'Comment Details Thesaurus',
    'admin/system/configcollect/comment_words'=>'Comment Details Random Words',

    'admin/system/configcomment/title'=>'Comment Message Configuration',
    'admin/system/configcomment/gbook'=>'Guestbook',
    'admin/system/configcomment/gbook_tip'=>'Whether to enable the guestbook',
    'admin/system/configcomment/audit'=>'Audit',
    'admin/system/configcomment/login'=>'Login Comment',
    'admin/system/configcomment/verify'=>'Verification Code',
    'admin/system/configcomment/pagesize'=>'Items per Page',
    'admin/system/configcomment/pagesize_tip'=>'Recommended to set above 20',
    'admin/system/configcomment/timespan'=>'Time Interval',
    'admin/system/configcomment/timespan_tip'=>'Unit: seconds, recommended above 3 seconds',
    'admin/system/configcomment/comment'=>'Comment Status',
    'admin/system/configcomment/comment_tip'=>'Whether to enable comments',

    'admin/system/configconnect/title'=>'Integrated Login Configuration',
    'admin/system/configconnect/tip'=>'Prompt:<br>
                        1. QQ login URL /index.php/user/oauth/?type=qq<br>
                        2. WeChat login URL /index.php/user/oauth/?type=weixin<br>
                        3. Callback URL /index.php/user/logincallback/?type=qq or /index.php/user/logincallback/?type=weixin',
    'admin/system/configconnect/qq'=>'QQ Login',
    'admin/system/configconnect/go_reg'=>'Click to Register',
    'admin/system/configconnect/wx'=>'WeChat Login',

    'admin/system/configemail/title'=>'Email Sending Configuration',
    'admin/system/configemail/tip'=>'Prompt:<br>
                        After modification, please click save and then test sending. Content supports {$maccms.***} tags, {$user.***} tags, {$code} verification code, {$time} validity period.',
    'admin/system/configemail/type'=>'Sending Method',
    'admin/system/configemail/time'=>'Validity Period',
    'admin/system/configemail/time_tip'=>'The number of minutes after which the email verification code expires',
    'admin/system/configemail/nick'=>'Sender Nickname',
    'admin/system/configemail/test'=>'Test Address',
    'admin/system/configemail/btn_test'=>'Send Test Email',
    'admin/system/configemail/test_title'=>'Test Title',
    'admin/system/configemail/test_body'=>'Test Body',
    'admin/system/configemail/user_reg_title'=>'User Registration Title',
    'admin/system/configemail/user_reg_body'=>'User Registration Body',
    'admin/system/configemail/user_bind_title'=>'User Binding Title',
    'admin/system/configemail/user_bind_body'=>'User Binding Body',
    'admin/system/configemail/user_findpass_title'=>'User Retrieval Title',
    'admin/system/configemail/user_findpass_body'=>'User Retrieval Body',
    'admin/system/configemail/test_err'=>'An error occurred, please check if the corresponding extension library is enabled',


    'admin/system/configinterface/pass_check'=>'Save failed, for security reasons the password for database entry must be at least 16 characters long!',
    'admin/system/configinterface/title'=>'External Site Database Configuration',
    'admin/system/configinterface/tip'=>'Tips:<br>
                        1. Each category conversion should be on a separate line;<br>
                        2. Local categories first, collection categories later (Action Movie=Action);<br>
                        3. No extra blank lines;<br>
                        4. For multiple data connections such as video player, remarks, addresses, server groups, article pagination, etc., use $$$ as the delimiter;<br>
                        5. Database entry interface addresses: Video /api.php/receive/vod; Article /api.php/receive/art; Actor /api.php/receive/actor; Role /api.php/receive/role; Website /api.php/receive/website;<br>',
    'admin/system/configinterface/status'=>'Interface Switch',
    'admin/system/configinterface/pass'=>'Database Entry Password',
    'admin/system/configinterface/pass_tip'=>'To prevent brute force password cracking, it is recommended to set a password of at least 16 characters including uppercase, lowercase letters, numbers, and special symbols or modify the api.php entry file name',
    'admin/system/configinterface/vod_type'=>'Video Category Conversion',
    'admin/system/configinterface/art_type'=>'Article Category Conversion',
    'admin/system/configinterface/actor_type'=>'Actor Category Conversion',
    'admin/system/configinterface/website_type'=>'Website Category Conversion',

    'admin/system/configpay/title'=>'Online Payment Configuration',
    'admin/system/configpay/card'=>'Card Password',
    'admin/system/configpay/config'=>'Payment Configuration',
    'admin/system/configpay/notify'=>'Callback Notification URL',
    'admin/system/configpay/notify_tip'=>'Callback URL for payment interface notification',
    'admin/system/configpay/min'=>'Minimum Recharge Amount',
    'admin/system/configpay/min_tip'=>'Unit: RMB Yuan, minimum 1 Yuan',
    'admin/system/configpay/scale'=>'Exchange Ratio',
    'admin/system/configpay/scale_tip'=>'1 RMB equals how many points',
    'admin/system/configpay/card_config'=>'Card Password Configuration',
    'admin/system/configpay/card_url'=>'Sales Website',
    'admin/system/configpay/card_url_tip'=>'Third-party card password platform',

    'admin/system/configplay/title'=>'Player Parameter Configuration',
    'admin/system/configplay/tip'=>'Tips:<br>
                        1. Player size supports px pixels and % percentage units.<br>
                        2. If no unit is specified, it defaults to 100%.',

    'admin/system/configplay/width'=>'Player Width',
    'admin/system/configplay/width_tip'=>'Example: 540px or 100%',
    'admin/system/configplay/height'=>'Player Height',
    'admin/system/configplay/height_tip'=>'Example: 460px or 100%',
    'admin/system/configplay/widthmob'=>'Mobile Player Width',
    'admin/system/configplay/heightmob'=>'Mobile Player Height',
    'admin/system/configplay/widthpop'=>'Popup Window Width',
    'admin/system/configplay/heightpop'=>'Popup Window Height',
    'admin/system/configplay/second'=>'Preload Time',
    'admin/system/configplay/second_tip'=>'Example: 5, in seconds',
    'admin/system/configplay/prestrain'=>'Preload Prompt',
    'admin/system/configplay/prestrain_tip'=>'Avoid using special characters like double quotes, single quotes, etc.',
    'admin/system/configplay/buffer'=>'Buffer Prompt',
    'admin/system/configplay/buffer_tip'=>'Avoid using special characters like double quotes, single quotes, etc.',
    'admin/system/configplay/parse'=>'Interface URL',
    'admin/system/configplay/parse_tip'=>'Third-party processing interface',
    'admin/system/configplay/autofull'=>'Auto Fullscreen',
    'admin/system/configplay/showtop'=>'Header Switch',
    'admin/system/configplay/showlist'=>'List Switch',
    'admin/system/configplay/flag'=>'Player File',
    'admin/system/configplay/flag_tip'=>'Local player file',
    'admin/system/configplay/colors'=>'Player Colors',
    'admin/system/configplay/select_colors'=>'Select Colors',
    'admin/system/configplay/select_colors_tip'=>'Colors are represented in hexadecimal format without the # sign, separated by commas, a total of 15 configurable colors!
                        <br>They are: Background color, Text color, Link color, Group title background color, Group title color, Current group title color, Current episode color, Scroll bar protruding part color, Scroll bar up and down button triangle arrow color, Scroll bar background color, Scroll bar blank part color, Scroll bar stereoscopic scroll bar shadow color, Scroll bar highlight edge color, Scroll bar strong shadow color, Basic color of the scroll bar',

    'admin/system/configweixin/title'=>'WeChat Integration Configuration',
    'admin/system/configweixin/tip'=>'The integration domain name for the official account, search domain name for the official account, and website domain name are usually consistent. Due to possible domain name blocking issues with the official account, you can set a separate domain name for integration.<br>
                    Interface URL: /api.php/wechat',
    'admin/system/configweixin/duijie'=>'Integration Domain Name',
    'admin/system/configweixin/duijie_tip'=>'Please start with http or https. The integration address in the official account backend is [http://wx.test.com/inc/weixin.php]',
    'admin/system/configweixin/sousuo'=>'Search Domain Name',
    'admin/system/configweixin/sousuo_tip'=>'Domain name used to display in the official account. Generally, change this domain name for the official account. Please start with http or https.',
    'admin/system/configweixin/token'=>'Integration TOKEN',
    'admin/system/configweixin/token_tip'=>'Token key for integration in the official account backend',
    'admin/system/configweixin/guanzhu'=>'Follow Reply',
    'admin/system/configweixin/guanzhu_tip'=>'Automatic reply when users follow your official account',
    'admin/system/configweixin/wuziyuan'=>'No Resource Reply',
    'admin/system/configweixin/wuziyuan_tip'=>'Default message returned when users cannot find resources',
    'admin/system/configweixin/wuziyuanlink'=>'No Resource Reply Link',
    'admin/system/configweixin/wuziyuanlink_tip'=>'No resource reply link or content',
    'admin/system/configweixin/pagelink'=>'Return Page Address',
    'admin/system/configweixin/pagelink_detail'=>'Content Page',
    'admin/system/configweixin/pagelink_play'=>'Playback Page',
    'admin/system/configweixin/pagelink_search'=>'Search Page',
    'admin/system/configweixin/msgtype'=>'Return Content Type',
    'admin/system/configweixin/msgtype_pic'=>'Graphic',
    'admin/system/configweixin/msgtype_font'=>'Text',
    'admin/system/configweixin/msgtype_tip'=>'WeChat now mandates only 1 graphic can be returned',

    'admin/system/configuser/title'=>'Member Parameter Configuration',
    'admin/system/configuser/tip'=>'Tips:<br>
                        1. If the trial function is enabled, the playback window will be loaded dynamically via iframe, which may affect performance; <br>',
    'admin/system/configuser/model'=>'Member Module',
    'admin/system/configuser/reg_open'=>'Registration Switch',
    'admin/system/configuser/reg_status'=>'Default Registration Status',
    'admin/system/configuser/phone_reg_verify'=>'Mobile Registration Verification',
    'admin/system/configuser/email_reg_verify'=>'Email Registration Verification',
    'admin/system/configuser/email_white_hosts'=>'Email Whitelist',
    'admin/system/configuser/email_white_hosts_tip'=>"After filling in, only email hostnames in the whitelist are allowed to register. Use commas or line breaks to separate multiple entries. For example: qq.com,360.com\nNote: If both black and white lists are filled, both policies will take effect.",
    'admin/system/configuser/email_black_hosts'=>'Email Blacklist',
    'admin/system/configuser/email_black_hosts_tip'=>"After filling in, email hostnames in the blacklist are not allowed to register. Use commas or line breaks to separate multiple entries. For example: protonmail.com,gmail.com\nNote: If both black and white lists are filled, both policies will take effect.",
    'admin/system/configuser/reg_verify'=>'Registration Verification Code',
    'admin/system/configuser/login_verify'=>'Login Verification Code',
    'admin/system/configuser/reg_points'=>'Registration Points Reward',
    'admin/system/configuser/reg_points_tip'=>'Points awarded to users upon successful registration',
    'admin/system/configuser/reg_num'=>'Registration Limit per IP',
    'admin/system/configuser/reg_num_tip'=>'Limit on the number of registrations per IP address per day',
    'admin/system/configuser/invite_reg_points'=>'Invitation Registration Points',
    'admin/system/configuser/invite_reg_points_tip'=>'Reward points earned by successfully inviting users to register',
    'admin/system/configuser/invite_visit_points'=>'Promotion Visit Points',
    'admin/system/configuser/invite_visit_points_tip'=>'Reward points earned by successfully inviting visits',
    'admin/system/configuser/invite_visit_num_tip'=>'Limit on the number of promotion visit points that can be obtained per IP address per day',
    'admin/system/configuser/reward_status'=>'Three-level Distribution Status',
    'admin/system/configuser/reward_ratio'=>'First-level Commission Ratio',
    'admin/system/configuser/reward_ratio2'=>'Second-level Commission Ratio',
    'admin/system/configuser/reward_ratio3'=>'Third-level Commission Ratio',
    'admin/system/configuser/reward_unit'=>'Percentage',
    'admin/system/configuser/reward_tip'=>'Upon successful payment, distributors receive a certain percentage of points. Commissions less than 1 point will be ignored.',
    'admin/system/configuser/cash_status'=>'Withdrawal Status',
    'admin/system/configuser/cash_ratio'=>'Exchange Ratio',
    'admin/system/configuser/cash_ratio_tip'=>'Exchange rate: 1 yuan = how many points',
    'admin/system/configuser/cash_min'=>'Minimum Withdrawal Amount',
    'admin/system/configuser/cash_min_tip'=>'Minimum amount for withdrawal',
    'admin/system/configuser/trysee'=>'Trial Viewing Duration',
    'admin/system/configuser/trysee_tip'=>'Global setting for the trial viewing duration requiring points, in minutes; 0 means global trial viewing is disabled;',
    'admin/system/configuser/vod_points_type'=>'Video Charging Method',
    'admin/system/configuser/vod_points_0'=>'Per Episode',
    'admin/system/configuser/vod_points_1'=>'Per Data',
    'admin/system/configuser/art_points_type'=>'Article Charging Method',
    'admin/system/configuser/art_points_0'=>'Per Page',
    'admin/system/configuser/art_points_1'=>'Per Data',
    'admin/system/configuser/portrait_status'=>'Avatar Upload',
    'admin/system/configuser/portrait_size'=>'Avatar Size',
    'admin/system/configuser/portrait_size_tip'=>'Recommended size: 100x100',
    'admin/system/configuser/filter_words'=>'Username Filtering',
    'admin/system/configuser/filter_words_tip'=>'Multiple entries separated by commas',

    'admin/system/configurl/title'=>'URL Parameter Configuration',
    'admin/system/configurl/view'=>'Browse Mode Settings',
    'admin/system/configurl/html'=>'Static Path Settings',
    'admin/system/configurl/route'=>'Route Pseudo-static Settings',
    'admin/system/configurl/dynamic'=>'Dynamic Mode',
    'admin/system/configurl/static'=>'Static Mode',
    'admin/system/configurl/static_one'=>'Static One Page per Episode',
    'admin/system/configurl/index'=>'Home',
    'admin/system/configurl/map'=>'Map',
    'admin/system/configurl/search'=>'Search',
    'admin/system/configurl/label'=>'Custom Pages',
    'admin/system/configurl/vod_type'=>'Video Categories',
    'admin/system/configurl/vod_show'=>'Video Category Filtering',
    'admin/system/configurl/art_type'=>'Article Categories',
    'admin/system/configurl/art_show'=>'Article Category Filtering',
    'admin/system/configurl/topic_index'=>'Topic Homepage',
    'admin/system/configurl/topic_detail'=>'Topic Details',
    'admin/system/configurl/vod_detail'=>'Video Details',
    'admin/system/configurl/vod_play'=>'Video Playback',
    'admin/system/configurl/vod_down'=>'Video Download',
    'admin/system/configurl/art_detail'=>'Article Details',
    'admin/system/configurl/variable'=>'Variables',
    'admin/system/configurl/structure'=>'Common Structures',
    'admin/system/configurl/multipage_connector'=>'Multi-page Connector',
    'admin/system/configurl/multipage_connector_tip'=>'For example: separator is -, the second page is type/index-2.html.',
    'admin/system/configurl/common_connector'=>'Multi-page Connector',
    'admin/system/configurl/file_ext'=>'File Extension',
    'admin/system/configurl/file_ext_tip'=>'For example: recommended as html',
    'admin/system/configurl/common_ext'=>'File Extension',
    'admin/system/configurl/route_tip'=>'Tips:<br>
                        1. Under dynamic mode, enabling route status will automatically rewrite URLs;<br>
                        2. Each line in route rules should be separated by =>, where the left side is the route expression and the right side is the route address;<br>
                        3. Route addresses are system-provided and generally do not change, only route expressions need adjustment.<br>
                        4. Avoid unnecessary empty lines;<br>
                        5. Choose ID type according to your needs. When choosing Pinyin, ensure there are no duplicate Pinyin in the data to avoid issues fetching data.<br>
                        6. URL separators support / and -. Avoid using other symbols.',

    'admin/system/configurl/suffix_hide'=>'Hide Suffix',
    'admin/system/configurl/route_status'=>'Route Status',
    'admin/system/configurl/rewrite_status'=>'Pseudo-static Status',
    'admin/system/configurl/route_rule'=>'Route Rules',
    'admin/system/configurl/encode_key'=>'Encryption Key',
    'admin/system/configurl/encode_len'=>'Encryption Length',
    'admin/system/configurl/encode_tip'=>'Changing the key will also change the URL. Length indicates the encrypted length will not be less than the original numeric length.',

    'admin/system/configupload/title'=>'Attachment Parameter Configuration',
    'admin/system/configupload/tip'=>'Tip: Whether uploading locally or using third-party storage, files need to be uploaded locally first before being transferred to third-party storage.<br>
                        Therefore, the temporary file directory of the local operating system must have write permissions, or else file uploads will fail.<br>
                        To modify the PHP temporary file directory, search for sys_temp_dir in the PHP configuration file.<br>
                        Current operating system temporary file directory:',
    'admin/system/configupload/write_ok'=>'Successful test write to temporary file, upload status normal',
    'admin/system/configupload/write_err'=>'Failed to write to temporary file, please check temporary file directory permissions',
    'admin/system/configupload/thumb_tip'=>'Generate Thumbnail Images When Uploading Pictures',
    'admin/system/configupload/thumb_size'=>'Size',
    'admin/system/configupload/thumb_size_tip'=>'Thumbnail size format should be width x height, e.g., 300x300',
    'admin/system/configupload/thumb_type'=>'Crop Method',
    'admin/system/configupload/thumb_type1'=>'Proportional Scaling',
    'admin/system/configupload/thumb_type2'=>'Scaling and Fill',
    'admin/system/configupload/thumb_type3'=>'Center Crop',
    'admin/system/configupload/thumb_type4'=>'Top Left Crop',
    'admin/system/configupload/thumb_type5'=>'Bottom Right Crop',
    'admin/system/configupload/thumb_type6'=>'Fixed Size Scaling',
    'admin/system/configupload/watermark'=>'Text Watermark',
    'admin/system/configupload/watermark_location'=>'Watermark Position',
    'admin/system/configupload/watermark_location1'=>'Top Left',
    'admin/system/configupload/watermark_location2'=>'Top Center',
    'admin/system/configupload/watermark_location3'=>'Top Right',
    'admin/system/configupload/watermark_location4'=>'Left Center',
    'admin/system/configupload/watermark_location5'=>'Center',
    'admin/system/configupload/watermark_location6'=>'Right Center',
    'admin/system/configupload/watermark_location7'=>'Bottom Left',
    'admin/system/configupload/watermark_location8'=>'Bottom Center',
    'admin/system/configupload/watermark_location9'=>'Bottom Right',
    'admin/system/configupload/watermark_content'=>'Watermark Content',
    'admin/system/configupload/watermark_size'=>'Font Size',
    'admin/system/configupload/watermark_size_tip'=>'Unit: px (pixels)',
    'admin/system/configupload/watermark_color'=>'Watermark Color',
    'admin/system/configupload/watermark_color_tip'=>'Format: #000000',
    'admin/system/configupload/protocol'=>'Third-party Access Protocol',
    'admin/system/configupload/protocol_tip'=>'When using third-party storage, URLs displayed in templates will start with mac://, which should be replaced with http or https in image links.',
    'admin/system/configupload/mode'=>'Storage Mode',
    'admin/system/configupload/mode_local'=>'Local Storage',
    'admin/system/configupload/mode_remote'=>'Remote Access',
    'admin/system/configupload/remoteurl'=>'Remote Image URL',
    'admin/system/configupload/remoteurl_tip'=>'For local images that exist remotely, use this function',
    'admin/system/configupload/img_key'=>'Anti-leeching Identifier',
    'admin/system/configupload/img_key_tip'=>'Domains or keywords needing anti-leeching. Use | to separate multiple entries.',
    'admin/system/configupload/img_api'=>'Anti-leeching Interface',
    'admin/system/configupload/img_api_tip'=>'Interface address for handling anti-leeching images',
    'admin/system/configupload/keep_local'=>'Keep Local Copy',
    'admin/system/configupload/keep_local_tip'=>'If uploading to remote, keep a local copy after upload completion',

    'admin/system/configsms/title'=>'SMS Sending Configuration',
    'admin/system/configsms/tip'=>'Tip:<br>
                        Please ensure that you set up SMS signatures and content according to the requirements of the SMS service provider.',
    'admin/system/configsms/type'=>'Service Provider',
    'admin/system/configsms/sign'=>'SMS Signature',
    'admin/system/configsms/tpl_code_reg'=>'Registration Template Code',
    'admin/system/configsms/tpl_code_tip'=>'Template codes need to be applied for in the service provider’s SMS control panel',
    'admin/system/configsms/tpl_code_bind'=>'Binding Template Code',
    'admin/system/configsms/tpl_code_findpass'=>'Retrieve Password Template Code',
    'admin/system/configsms/test_err'=>'Error occurred, please check if the corresponding extension library is enabled!',




    'admin/system/configseo/vod_index'=>'Video Homepage',
    'admin/system/configseo/art_index'=>'Article Homepage',
    'admin/system/configseo/actor_index'=>'Actor Homepage',
    'admin/system/configseo/role_index'=>'Role Homepage',
    'admin/system/configseo/plot_index'=>'Plot Homepage',
    'admin/system/configseo/website_index'=>'Website Homepage',
    'admin/system/configseo/tit'=>'Title',
    'admin/system/configseo/key'=>'Keywords',
    'admin/system/configseo/des'=>'Description',
    'admin/system/configseo/tip_des'=>'Tip Information',

    'admin/actor/title'=>'Actor Management',
    'admin/addon/title'=>'Plugin Management',
    'admin/addon/get_dir_err'=>'Failed to retrieve plugin directory',
    'admin/addon/get_addon_info_err'=>'Failed to retrieve plugin information',
    'admin/addon/lack_config_err'=>'Missing plugin configuration file info.ini',
    'admin/addon/name_empty_err'=>'Plugin name cannot be empty',
    'admin/addon/haved_err'=>'Plugin already exists',
    'admin/addon/path_err'=>'Illegal directory request',
    'admin/addon/add_tip'=>'Tip:<br>
                1. Please ensure third-party plugins comply with development standards.
                2. Perform security checks before use to avoid potential security issues.',

    'admin/admin/title'=>'Administrator Management',
    'admin/admin/del_cur_err'=>'Cannot delete the currently logged-in account',
    'admin/admin/popedom_tip'=>'Tip:<br>
                    1. Permissions are controlled precisely for each operation. Administrator with Founder ID 1 has all permissions.
                    2. Options starting with -- are for internal page buttons.',

    'admin/art/title'=>'Article Management',
    'admin/card/title'=>'Recharge Card Management',
    'admin/card/make_num'=>'Number of Cards to Generate',
    'admin/card/please_input_make_num'=>'Please input the number of cards to generate',
    'admin/card/please_input_money'=>'Please input the face value of the recharge card',
    'admin/card/please_input_points'=>'Please input the points of the recharge card',

    'admin/card/import_tip'=>'Card Number, Password, Creation Time',
    'admin/cash/title'=>'Withdrawal Management',
    'admin/vodserver/title'=>'Server Group Management',
    'admin/vodserver/url'=>'Server Group URL',
    'admin/vodplayer/title'=>'Player Management',
    'admin/voddowner/title'=>'Downloader Management',
    'admin/vodplayer/alone_api_url'=>'Independent API URL',
    'admin/vodplayer/code_tip'=>'Unique identifier in English, automatically adds _ to pure numbers, symbols like ./\\ are prohibited',
    'admin/vodplayer/name_tip'=>'Chinese Name',
    'admin/vodplayer/api_url'=>'API URL',
    'admin/vodplayer/api_url_tip'=>'Independent API URL, takes precedence over global player settings',
    'admin/vodplayer/sort_tip'=>'The higher the number, the higher the priority in sorting',
    'admin/vodplayer/code_empty'=>'Please enter the code',
    'admin/vodplayer/import_tip'=>'Tip:<br>
                       1. Please ensure the imported file format is correct.',



    'admin/timming/title'=>'Scheduled Task Management',
    'admin/timming/unique_id'=>'Unique Identifier (English)',
    'admin/timming/call_method'=>'Method to Call',
    'admin/timming/exec_file'=>'Execution File',
    'admin/timming/collect'=>'Custom Collection (collect)',
    'admin/timming/make'=>'Static Generation (make)',
    'admin/timming/cj'=>'Custom Collection Rules (cj)',
    'admin/timming/cache'=>'Clear Cache (cache)',
    'admin/timming/urlsend'=>'Website Push (urlsend)',
    'admin/timming/attach_param'=>'Additional Parameters',
    'admin/timming/attach_param_tip'=>'Can be left blank, e.g., ac=timming&id=1',
    'admin/timming/exec_cycle'=>'Execution Cycle',
    'admin/timming/exec_time'=>'Execution Time',

    'monday'=>'Monday',
    'tuesday'=>'Tuesday',
    'wednesday'=>'Wednesday',
    'thursday'=>'Thursday',
    'friday'=>'Friday',
    'saturday'=>'Saturday',
    'sunday'=>'Sunday',

    'admin/domain/title'=>'Site Group Management',
    'admin/domain/help_tip'=>'Tip:<br>
                        1. This function supports displaying different templates and website configuration information for different domains under the same database in non-static mode.<br>
                        2. No limit on the number of domain websites.<br>
                        3. Import text format: domain$website name$keywords$description$template$template directory$ad directory. One website per line. Clear existing data.<br>',

    'admin/website/title'=>'Website Management',
    'admin/website/referer'=>'Total Referrals',
    'admin/website/referer_month'=>'Monthly Referrals',
    'admin/website/referer_week'=>'Weekly Referrals',
    'admin/website/referer_day'=>'Daily Referrals',

    'admin/vod/title'=>'Video Management',
    'admin/vod/no'=>'No',
    'admin/vod/have'=>'Yes',
    'admin/vod/plot/title'=>'Episode Plot Management',
    'admin/vod/del_play_must_select_play'=>'When deleting a play group, you must select player parameters',
    'admin/vod/del_down_must_select_down'=>'When deleting a download group, you must select downloader parameters',
    'admin/vod/select_weekday'=>'Select Weekday',
    'admin/vod/select_area'=>'Select Area',
    'admin/vod/select_lang'=>'Select Language',
    'admin/vod/select_player'=>'Select Player',
    'admin/vod/select_server'=>'Select Server',
    'admin/vod/player_empty'=>'Empty Player',
    'admin/vod/select_downer'=>'Select Downloader',
    'admin/vod/downer_empty'=>'Empty Downloader',
    'admin/vod/select_isend'=>'Select Completion',
    'admin/vod/select_copyright'=>'Select Copyright',
    'admin/vod/is_end'=>'Completed',
    'admin/vod/no_end'=>'Not Completed',
    'admin/vod/del_player'=>'Delete Player Group',
    'admin/vod/del_downer'=>'Delete Downloader Group',
    'admin/vod/episode_plot'=>'Episode Plot',
    'admin/vod/plot'=>'Episode Plot',
    'admin/vod/plot_name'=>'Plot Title',
    'admin/vod/move_up'=>'Move Up',
    'admin/vod/move_down'=>'Move Down',
    'admin/vod/copyright_open'=>'Enable Copyright Handling',
    'admin/vod/copyright_close'=>'Disable Copyright Handling',
    'admin/vod/move_behind'=>'Behind the Scenes',
    'admin/vod/total'=>'Total Episodes',
    'admin/vod/serial'=>'Serials',
    'admin/vod/pubdate'=>'Release Date',
    'admin/vod/director'=>'Director',
    'admin/vod/writer'=>'Writer',
    'admin/vod/tv'=>'TV Channel',
    'admin/vod/weekday'=>'Program Weekday',
    'admin/vod/duration'=>'Video Duration',
    'admin/vod/douban_score'=>'Douban Score',
    'admin/vod/douban_id'=>'Douban ID',
    'admin/vod/douban_id_empty'=>'Please enter Douban ID',
    'admin/vod/rel_vod_tip'=>'e.g., For "Transformers" parts 1, 2, 3, the IDs are 11, 12, 13 respectively, or fill in "Transformers" for each part',
    'admin/vod/rel_art_tip'=>'e.g., For "Transformers News" parts 1, 2, 3, the IDs are 11, 12, 13 respectively, or fill in "Transformers News" for each part',
    'admin/vod/version'=>'Resource Version',
    'admin/vod/state'=>'Resource Category',
    'admin/vod/isend'=>'Completion',
    'admin/vod/tpl'=>'Content Page Template',
    'admin/vod/tpl_play'=>'Playback Page Template',
    'admin/vod/tpl_down'=>'Download Page Template',
    'admin/vod/correct'=>'Correction',
    'admin/vod/reverse_order'=>'Reverse Order',
    'admin/vod/del_prefix'=>'Remove Prefix',
    'admin/vod/complete_works'=>'Complete Works',
    'admin/vod/stint_play'=>'Points Required per Episode for Playback',
    'admin/vod/stint_down'=>'Points Required per Episode for Download',
    'admin/vod/select_plot'=>'Select Episode Plot',
    'admin/vod/copyright'=>'Copyright',
    'admin/vod/serialize'=>'Serial',
    'admin/vod/add_group_play'=>'Add a Play Group',
    'admin/vod/add_group_down'=>'Add a Download Group',
    'admin/batch_tip'=>'Total %s data needs processing, %s per page, %s pages in total, processing page %s',


    'admin/template/title'=>'Template Management',
    'admin/template/ads/title'=>'Ad Space Management',
    'admin/template/wizard/title'=>'Tag Wizard Management',
    'admin/template/ext_safe_tip'=>'Security Tip: Only allow file extensions htm, html, js, xml',
    'admin/template/php_safe_tip'=>'Security Tip: Templates containing PHP code are prohibited from being edited in the backend',
    'admin/template/call_code'=>'Call Code',
    'admin/template/current_dir'=>'Current Path',
    'admin/template/name_tip'=>'File extensions allowed are html, htm, js, xml; Custom pages should start with label_',
    'admin/template/reverse_order'=>'Reverse Order',
    'admin/template/positive_order'=>'Positive Order',
    'admin/template/filter_search'=>'Filter Search Links',
    'admin/template/reply_content'=>'Reply Content',

    'admin/cj/title'=>'Custom Collection Management',
    'admin/cj/url/title'=>'Collection URL Address',
    'admin/cj/publish/title'=>'Content Publishing Management',
    'admin/cj/url_list_err'=>'Failed to retrieve URL information',
    'admin/cj/url_cj_complete'=>'URL collection complete',
    'admin/cj/content/tip'=>'Collecting content, total [%s] items, divided into %s pages, %s items per page, currently on page %s',
    'admin/cj/content_cj_complete'=>'Content collection complete',
    'admin/cj/cj_complete'=>'Collection complete',
    'admin/cj/content_into/tip'=>'Importing content, total [%s] items, divided into %s pages, %s items per page, currently on page %s',
    'admin/cj/content_into/complete'=>'Content import complete',
    'admin/cj/cj_url'=>'Collection URL',
    'admin/cj/cj_content'=>'Collection Content',
    'admin/cj/content_publish'=>'Content Publishing',
    'admin/cj/publish_plan'=>'Publishing Plan',
    'admin/cj/collected'=>'Collected',
    'admin/cj/collected_not'=>'Not Collected',
    'admin/cj/published'=>'Published',
    'admin/cj/trim_space'=>'Trim Spaces',
    'admin/cj/label_data_rel'=>'Label-Database Correspondence',
    'admin/cj/data_column'=>'Database Field',
    'admin/cj/label_column'=>'Label Field',
    'admin/cj/processing_function'=>'Processing Function',
    'admin/cj/rule_url'=>'URL Rule',
    'admin/cj/rule_content'=>'Content Rule',
    'admin/cj/rule_diy'=>'Custom Rule',
    'admin/cj/adv_config'=>'Advanced Configuration',
    'admin/cj/rule_name'=>'Rule Name',
    'admin/cj/rule_name_en'=>'Rule English Name',
    'admin/cj/page_charset'=>'Web Page Encoding',
    'admin/cj/cj_model'=>'Collection Module',
    'admin/cj/url_collect'=>'URL Collection',
    'admin/cj/url_type'=>'URL Type',
    'admin/cj/sequence_url'=>'Sequential URL',
    'admin/cj/multi_url'=>'Multiple URLs',
    'admin/cj/one_url'=>'Single URL',
    'admin/cj/wildcard_tip'=>'Used as a wildcard',
    'admin/cj/page_num_config'=>'Page Number Configuration',
    'admin/cj/page_num_increment'=>'Page Number Increment',
    'admin/cj/one_per_line'=>'One per line',
    'admin/cj/url_config'=>'URL Configuration',
    'admin/cj/url_must_contain'=>'URL must contain',
    'admin/cj/url_not_contain'=>'URL must not contain',
    'admin/cj/collect_interval'=>'Collection Interval',
    'admin/cj/wildcard_prompt'=>'<p>1. Match rules should set start and end markers, with "[content]" as the wildcard for specific content.</p>
                            <p>2. Match rules can also be fixed content, as long as no "[content]" wildcard appears.</p>
                            <p>3. Filtering options format is "content to filter[|]replacement value", content to filter supports regular expressions, one per line.</p>',

    'admin/cj/title_rule'=>'Title Rule',
    'admin/cj/match_rule'=>'Match Rule',
    'admin/cj/filter_rule'=>'Filter Rule',
    'admin/cj/type_rule'=>'Type Rule',
    'admin/cj/content_rule'=>'Content Rule',
    'admin/cj/page_mode'=>'Pagination Mode',
    'admin/cj/list_all_mode'=>'List All Mode',
    'admin/cj/next_page_mode'=>'Next Page Mode',
    'admin/cj/next_page_rule'=>'Next Page Rule',
    'admin/cj/next_page_tip'=>'Please fill in the code between the next page hyperlinks. For example: <a href="http://www.xxx.com/page_1.html">Next Page</a>, its "Next Page Rule" is "Next Page".',
    'admin/cj/add_group'=>'Add a Group',


    'admin/cj/content_page'=>'Content Pagination',
    'admin/cj/no_page'=>'No Pagination',
    'admin/cj/original_page'=>'Paginate According to Original',
    'admin/cj/import_sort'=>'Import Order',
    'admin/cj/same_to_site'=>'Same as Target Site',
    'admin/cj/opposite_to_site'=>'Opposite to Target Site',

    'admin/collect/title'=>'Resource Collection Management',
    'admin/collect/load_break'=>'Loading breakpoint position, please wait...',
    'admin/collect/view_all_resource'=>'View All Resources',
    'admin/collect/cj_select'=>'Collect Selected',
    'admin/collect/cj_today'=>'Collect Today',
    'admin/collect/cj_all'=>'Collect All',
    'admin/collect/clear_bind'=>'Clear Binding',
    'admin/collect/name'=>'Resource Name',
    'admin/collect/api_url'=>'API URL',
    'admin/collect/attach_param'=>'Additional Parameters',
    'admin/collect/attach_param_tip'=>'Tip: Generally starts with &, for example, for old version XML format collection, add &ct=1 for download address.',
    'admin/collect/api_type'=>'API Type',
    'admin/collect/data_type'=>'Resource Type',
    'admin/collect/data_opt'=>'Data Operation',
    'admin/collect/data_opt_tip'=>'Tip: If a resource as a secondary resource does not want to add new data, you can only select update.',
    'admin/collect/add_update'=>'Add + Update',
    'admin/collect/add'=>'Add',
    'admin/collect/update'=>'Update',
    'admin/collect/url_filter'=>'URL Filter',
    'admin/collect/no_filter'=>'No Filter',
    'admin/collect/filter_code'=>'Filter Code',
    'admin/collect/filter_code_tip'=>'For resources with multiple address groups, enabling whitelist will only store addresses with specified codes. For example, youku, iqiyi.',
    'admin/collect/filter_year'=>'Filter Year',
    'admin/collect/filter_year_tip'=>'Enter only the specified year videos. Multiple years are separated by commas, e.g., 2022,2023',
    'admin/collect/test_ok'=>'Test type successful, API type',

    'admin/comment/title'=>'Comment Management',

    'admin/gbook/title'=>'Guestbook Management',

    'admin/group/title'=>'Member Group Management',
    'admin/group/reg_group_del_err'=>'Cannot delete default registered member group!',
    'admin/group/help_tip'=>'Tip:<br>1. Guests and regular members are built-in member groups and cannot be deleted or disabled.<br>2. Please set permissions for each member group separately without inheritance of permissions.',
    'admin/group/pack_day'=>'Daily',
    'admin/group/pack_week'=>'Weekly',
    'admin/group/pack_month'=>'Monthly',
    'admin/group/pack_year'=>'Annual',
    'admin/group/popedom'=>'Related Permissions',
    'admin/group/popedom_tip'=>'Tip:<br>1. List Page, Content Page, Play Page, Download Page permissions control access to these pages. Without permission, users will receive a prompt.<br>2. Trial Viewing Permission: Even without access to play pages or needing points for access, users can enter pages if trial viewing permission is enabled.',
    'admin/group/popedom_list'=>'List Page',
    'admin/group/popedom_detail'=>'Content Page',
    'admin/group/popedom_play'=>'Play Page',
    'admin/group/popedom_down'=>'Download Page',
    'admin/group/popedom_trysee'=>'Trial Viewing',

    'admin/annex/title'=>'Attachment Management',
    'admin/annex/check'=>'Check Invalid Files',
    'admin/annex/check_complete'=>'Invalid file cleanup completed',
    'admin/annex/info_tip'=>'Total %s data, divided into %s checks, %s per check, currently on check number %s',


    'admin/annex/init_tip'=>'<strong>Attachment Data Initialization 1.0 Version</strong><br>
                            1. Search will be conducted on tables such as categories, videos, articles, websites, actors, roles, and members.<br>
                            2. Local image address content will be inserted into the attachment table.<br>
                            3. It is recommended to execute once for upgraded versions.',

    'admin/annex/init_data'=>'Initialize Data',
    'admin/annex/dir_model'=>'Folder Mode',
    'admin/annex/check_ok'=>'Attachment data initialization completed',
    'admin/annex/check_tip1'=>'Checking %s table... Total %s records, divided into %s checks, %s records per check, currently on check number %s',
    'admin/annex/check_jump'=>'Table %s checked, continuing shortly...',

    'admin/images/title'=>'Image Management',
    'admin/images/sync_complete'=>'Synchronization operation completed!',
    'admin/images/sync_tip'=>'Total %s data to process, %s records per page, %s pages in total, currently processing page %s',
    'admin/images/sync_range'=>'Synchronization Range',
    'admin/images/sync_option'=>'Synchronization Options',
    'admin/images/date'=>'Data Date',
    'admin/images/opt/tip1'=>'Synchronize fields - Synchronization option parameters do not affect when synchronizing detail images!',
    'admin/images/opt/tip2'=>'Number of records synchronized per page, not recommended to set too large',
    'admin/images/opt/pic'=>'Main Image',
    'admin/images/pic_content'=>'Detail Images',

    'admin/database/title'=>'Database Management',
    'admin/database/select_export_table'=>'Please select the tables you want to backup!',
    'admin/database/lock_check'=>'A backup task is currently running, please try again later!',
    'admin/database/backup_err'=>'Backup error!',
    'admin/database/backup_ok'=>'Backup completed!',
    'admin/database/select_file'=>'Please select the backup file you want to restore!',
    'admin/database/import_ok'=>'Data restore completed!',
    'admin/database/import_err'=>'Data restore error!',
    'admin/database/file_damage'=>'Backup file may be damaged, please check!',
    'admin/database/select_optimize_table'=>'Please select the tables you want to optimize!',
    'admin/database/optimize_ok'=>'Table optimization completed!',
    'admin/database/optimize_err'=>'Table optimization failed!',
    'admin/database/select_repair_table'=>'Please select the tables you want to repair!',
    'admin/database/repair_ok'=>'Table repair completed!',
    'admin/database/repair_err'=>'Table repair failed!',
    'admin/database/select_del_file'=>'Please select the backup files you want to delete!',
    'admin/database/backup_db'=>'Backup Database',
    'admin/database/import_db'=>'Restore Database',
    'admin/database/optimize_db'=>'Optimize Database',
    'admin/database/repair_db'=>'Repair Database',
    'admin/database/table'=>'Table Name',
    'admin/database/count'=>'Data Count',
    'admin/database/size'=>'Size',
    'admin/database/redundancy'=>'Redundancy',
    'admin/database/optimize'=>'Optimize',
    'admin/database/repair'=>'Repair',
    'admin/database/backup_name'=>'Backup Name',
    'admin/database/backup_num'=>'Backup Volume',
    'admin/database/backup_zip'=>'Backup Compression',
    'admin/database/backup_size'=>'Backup Size',
    'admin/database/backup_time'=>'Backup Time',
    'admin/database/import'=>'Restore',
    'admin/database/import_confirm'=>'Confirm restore of this backup? This action cannot be undone',
    'admin/database/batch_replace'=>'Batch Replace',
    'admin/database/select_table'=>'Select Data Table',
    'admin/database/select_col'=>'Select Fields',
    'admin/database/field'=>'Field to Replace',
    'admin/database/findstr'=>'Content to Replace',
    'admin/database/tostr'=>'Replace With',
    'admin/database/where'=>'Replacement Condition',


    'admin/database/sql'=>'Execute SQL Statement',
    'admin/database/sql_tip'=>'Common Statements:<br>
                        1. Query Data<br>
                        SELECT * FROM {pre}vod   Query all data<br>
                        SELECT * FROM {pre}vod WHERE vod_id=1000   Query specified ID data<br>
                        <br>
                        2. Delete Data<br>
                        DELETE FROM {pre}vod   Delete all data<br>
                        DELETE FROM {pre}vod WHERE vod_id=1000   Delete specific data<br>
                        DELETE FROM {pre}vod WHERE vod_actor LIKE \'%刘德华%\'   Data where vod_actor contains "刘德华"<br>
                        <br>
                        3. Modify Data<br>
                        UPDATE {pre}vod SET vod_hits=1   Set all vod_hits fields to "1"<br>
                        UPDATE {pre}vod SET vod_hits=1 WHERE vod_id=1000  Set vod_hits field to "1" for specific data<br>
                        <br>
                        4. Replace Image URLs<br>
                        UPDATE {pre}vod SET vod_pic=REPLACE(vod_pic, \'original_string\', \'replacement_string\')<br>
                        <br>
                        5. Reset Data ID Starting from 1<br>
                        TRUNCATE {pre}vod',

    'admin/safety/data_inspect'=>'Malware Detection',
    'admin/safety/data_inspect_tip'=>'<strong>Malware Detection 3.0 Version</strong><br>
                            1. Check table structures such as categories, video, article, member, etc.<br>
                            2. Detect special strings like script, iframe, etc.<br>
                            3. Automatically remove malware code.<br>
                            4. Cannot guarantee 100% removal. For persistent issues, use phpmyadmin or other database management tools.<br>
                            5. Recommend multiple clean-ups until no problematic data remains.',

    'admin/safety/data_clear_ok'=>'Cleaning completed. Please execute again to avoid missing any data',
    'admin/safety/data_check_tip1'=>'Starting check on %s table...',
    'admin/safety/data_check_tip2'=>'Detected %s dangerous data records...',

    'admin/safety/exec'=>'Confirm Execution',
    'admin/safety/file_inspect'=>'File Security Check',
    'admin/safety/file_inspect_tip'=>'<strong>Security Check 3.0 Version</strong><br>
                            1. Compare and filter all files within the website for inspection.<br>
                            2. List files included in the original program package with their MD5 hashes.<br>
                            3. List newly added files not found in the original program package.<br>
                            4. Cannot guarantee 100% accuracy. Report issues on official GitHub if problems persist.<br>
                            5. Recommend multiple checks. Thoroughly examine each listed file.',

    'admin/safety/file_msg1'=>'Failed to retrieve official file data. Please retry.',
    'admin/safety/file_msg2'=>'Failed to retrieve local file list. Please retry.',
    'admin/safety/file_msg3'=>'Newly Added Files',
    'admin/safety/file_msg4'=>'Different Files',

    'admin/link/title'=>'Friendship Link Management',
    'admin/link/text_link'=>'Text Links',
    'admin/link/pic_link'=>'Image Links',

    'admin/make/title'=>'Static Generation Management',
    'admin/make/view_model_static_err'=>'Non-static browsing mode, unable to generate',
    'admin/make/typepage_make_complete'=>'Category page generation completed',
    'admin/make/typepage_make_complete_later_make_index'=>'Category page generation completed, continue to generate homepage later',
    'admin/make/list_make_complate_later'=>'List page generation completed, continue later',
    'admin/make/type_tip'=>'Generating [%s] list pages, total %s pages, divided into %s batches, currently batch %s',
    'admin/make/type_timming_tip'=>'Scheduled task completed. %s list pages generated per category to prevent website freezing!',
    'admin/make/topicpage_make_complete'=>'Topic list page generation completed',
    'admin/make/topic_index_tip'=>'Generating topic list pages, total %s pages, divided into %s batches, currently batch %s',
    'admin/make/topic_tip'=>'Generating topic content pages, total %s',
    'admin/make/topic_make_complete'=>'Topic content pages generation completed',
    'admin/make/info_make_complete'=>'Content pages generation completed',
    'admin/make/info_make_complete_later_make_type'=>'Content pages generation completed, continue to generate category pages later',
    'admin/make/info_make_complete_later'=>'Content pages generation completed, continue later',
    'admin/make/info_tip'=>'Generating [%s] content pages, total %s, divided into %s batches, %s per batch, currently batch %s',
    'admin/make/label_tip'=>'Generating custom pages, total %s pages',
    'admin/make/label_complete'=>'Custom pages generation completed',
    'admin/make/select_type'=>'Select Category',
    'admin/make/all_type'=>'All Categories',
    'admin/make/today_type'=>'Today\'s Categories',
    'admin/make/select_info'=>'Select Content',
    'admin/make/all_info'=>'All Content',
    'admin/make/today_info'=>'Today\'s Content',
    'admin/make/no_make_info'=>'Not Generated',
    'admin/make/one_today'=>'Generate Today',
    'admin/make/topic_list'=>'Topic List',
    'admin/make/select_topic'=>'Select Topic',
    'admin/make/all_topic'=>'All Topics',
    'admin/make/topic_index'=>'Topic Index',
    'admin/make/label_page'=>'Custom Pages',
    'admin/make/rss'=>'RSS Feed File',
    'admin/make/google'=>'Google SiteMap',
    'admin/make/baidu'=>'Baidu SiteMap',
    'admin/make/so'=>'SO-SiteMap',
    'admin/make/sogou'=>'Sogou SiteMap',
    'admin/make/bing'=>'Bing SiteMap',
    'admin/make/sm'=>'Sm SiteMap',
    'admin/make/make_page_num'=>'Pages to Generate',

    'admin/order/title'=>'Order Management',
    'admin/order/order_no'=>'Order Number',
    'admin/order/order_money'=>'Order Amount',
    'admin/order/order_status'=>'Order Status',
    'admin/order/order_time'=>'Order Time',
    'admin/order/pay_type'=>'Payment Type',
    'admin/order/pay_time'=>'Payment Time',





    'admin/plog/title'=>'Points Log Management',
    'admin/plog/log_time'=>'Log Time',
    'admin/plog/points_recharge'=>'Points Recharge',
    'admin/plog/reg_promote'=>'Registration Promotion',
    'admin/plog/visit_promote'=>'Visit Promotion',
    'admin/plog/three_distribution'=>'Three-level Distribution',
    'admin/plog/points_upgrade'=>'Points Upgrade',
    'admin/plog/points_buy'=>'Points Purchase',
    'admin/plog/points_withdrawal'=>'Points Withdrawal',

    'admin/role/title'=>'Role Management',
    'admin/topic/title'=>'Topic Management',
    'admin/topic/vod_include'=>'Video Inclusion',
    'admin/topic/art_include'=>'Article Inclusion',
    'admin/topic/tpl_empty'=>'Please enter topic template',
    'admin/topic/count'=>'Data Count',

    'admin/type/title'=>'Category Management',
    'admin/type/type_tpl'=>'Category Page Template',
    'admin/type/show_tpl'=>'Filter Page Template',
    'admin/type/detail_tpl'=>'Content Page Template',
    'admin/type/play_tpl'=>'Player Page Template',
    'admin/type/down_tpl'=>'Download Page Template',
    'admin/type/tip'=>'Tips:<br>
            1. After adding new categories, please set permissions for each user group under User - Member Group. Otherwise, access may be denied.',
    'admin/type/parent_type'=>'Parent Category',
    'admin/type/top_type'=>'Top-level Category',
    'admin/type/logo'=>'Category Icon',
    'admin/type/pic'=>'Category Cover',
    'admin/type/tpl_empty'=>'Please enter category page template',
    'admin/type/extend_version'=>'Extended Version',
    'admin/type/extend_state'=>'Extended Resources',
    'admin/type/extend_director'=>'Extended Director',
    'admin/type/extend_star'=>'Extended Actors',

    'admin/ulog/title'=>'Log Management',

    'admin/update/step1_a'=>'Online upgrade in progress, Step 1 [File Upgrade], please wait...',
    'admin/update/step1_b'=>'Downloading upgrade package...',
    'admin/update/download_err'=>'Failed to download upgrade package, please retry...',
    'admin/update/download_ok'=>'Upgrade package downloaded successfully...',
    'admin/update/upgrade_package_processed'=>'Processing files in upgrade package...',
    'admin/update/upgrade_err'=>'Upgrade failed, please check system directory and file permissions!...',
    'admin/update/step2_a'=>'Online upgrade in progress, Step 2 [Data Upgrade], please wait...',
    'admin/update/upgrade_sql'=>'Found database upgrade script files, processing...',
    'admin/update/no_sql'=>'No database upgrade script found, proceeding to update data cache...',
    'admin/update/step3_a'=>'Online upgrade in progress, Step 3 [Update Cache], please wait...',
    'admin/update/update_cache'=>'Updating data cache files...',
    'admin/update/upgrade_complete'=>'Congratulations, system upgrade completed...',

    'admin/upload/test_write_ok'=>'Test write successful',
    'admin/upload/test_write_ok'=>'Write failed, please check temporary file directory permissions',
    'admin/upload/not_find_extend'=>'Third-party extension upload library not found',
    'admin/upload/no_input_file'=>'No uploaded file found (Reason: Form name may be incorrect, default form names are "file" or "imgdata")!',
    'admin/upload/forbidden_ext'=>'Upload format not allowed by the system!',
    'admin/upload/upload_success'=>'File uploaded successfully!',
    'admin/upload/upload_faild'=>'File upload failed!',
    'admin/upload/make_thumb_faild'=>'Thumbnail creation failed!',
    'admin/upload/upload_safe'=>'File contains dangerous content!',

    'admin/urlsend/title'=>'URL Push Management',
    'admin/urlsend/no_data'=>'No data retrieved',
    'admin/urlsend/tip'=>'Total %s data waiting to be pushed, divided into %s pages, currently on page %s',
    'admin/urlsend/complete'=>'Data push completed',
    'admin/urlsend/tip2'=>'Breakpoints will be recorded in the cache. They will disappear after updating the cache.<br>
            Before starting the push, please fill in the required configuration items above.<br>
            Current site configuration domain:',

    'admin/urlsend/send_genre'=>'Push Type',
    'admin/urlsend/page_send_num'=>'Push per Page',
    'admin/urlsend/start_page'=>'Starting Page Number',
    'admin/urlsend/in_break_point_exec'=>'Continue from breakpoint',
    'admin/urlsend/send_range'=>'Push Range',
    'admin/urlsend/add_update'=>'Add + Update',
    'admin/urlsend/add'=>'Add',

    'admin/user/title'=>'User Management',
    'admin/user/comment_record'=>'Comment Records',
    'admin/user/order_record'=>'Order Records',
    'admin/user/visit_record'=>'Visit Records',
    'admin/user/point_record'=>'Points Records',
    'admin/user/withdrawals_record'=>'Withdrawal Records',
    'admin/user/three_distribution'=>'Three-level Distribution',
    'admin/user/time_end'=>'Time End',
    'admin/user/find_question'=>'Retrieve Question',
    'admin/user/find_answer'=>'Retrieve Answer',
    'admin/user/access_empty'=>'Please enter account',
    'admin/user/pass_empty'=>'Please enter password',

    'admin/user/reward/select_level'=>'Select Level',
    'admin/user/reward/one_distribution'=>'First-level Distribution',
    'admin/user/reward/two_distribution'=>'Second-level Distribution',
    'admin/user/reward/three_distribution'=>'Third-level Distribution',
    'admin/user/reward/distribution_level'=>'Three-level Distribution',
    'admin/user/reward/one_people_num'=>'Total number of first-level distributions',
    'admin/user/reward/two_people_num'=>'Total number of second-level distributions',
    'admin/user/reward/three_people_num'=>'Total number of third-level distributions',
    'admin/user/reward/total_commission_points'=>'Total Commission Points',

    'admin/visit/title'=>'Visit Records Management',


    'api/auth_err'=>'Domain not authorized',
    'api/close_err'=>'API closed error',
    'api/pass_err'=>'Illegal use error',
    'api/pass_safe_err'=>'For security reasons, the password must be at least 16 characters long when storing it',
    'api/require_name'=>'Name is required',
    'api/require_type'=>'Category name or category ID must be filled in at least one',
    'api/require_sex'=>'Gender is required',
    'api/require_actor_name'=>'Actor name is required',
    'api/require_role_name'=>'Role name is required',
    'api/require_rel_vod'=>'Either related data name (vod_name) or Douban ID (douban_id) must be filled in at least one',
    'api/require_rel_name'=>'Either related data name (rel_name) or Douban ID (douban_id) must be filled in at least one',
    'api/require_mid'=>'Module ID is required',
    'api/require_comment_name'=>'Comment nickname is required',
    'api/require_comment_name'=>'Comment content is required',
    'api/never'=>'Never',
    'api/task_tip_exec'=>'Task: %s, Status: %s, Last execution time: %s --- Executed',
    'api/task_tip_jump'=>'Task: %s, Status: %s, Last execution time: %s --- Skipped',


    'install/title'=>'Apple CMS-V10 System Installation',
    'install/header'=>'Thank you for choosing Apple CMS-V10 to build your website',
    'install/lang'=>'Language pack [langs]',
    'install/select_lang'=>'Please select language pack [select lang]',
    'install/lang_tip'=>'Please choose the backend language pack according to your needs',

    'install/user_agreement_title'=>'Apple CMS User Agreement Applicable to All Users',
    'install/user_agreement'=>'Before using Apple CMS, please carefully read the following terms. This includes disclaimers that exempt or limit the author\'s liability and restrictions on user rights. Your installation and use will be deemed acceptance of this "User License Agreement" and agreement to abide by all terms of this "User License Agreement". <br /><br />
                I. Installation and Use: <br />
                Apple CMS is provided to you for free and open-source, and you can install an unlimited number of copies. You must ensure that you use this software without engaging in illegal activities and without violating the relevant policies and regulations of your country. <br /><br />
                II. Disclaimer: <br />
                This software comes with no express or implied warranties, including but not limited to the implied warranties of merchantability, non-infringement, or fitness for a particular purpose regarding this software. In no event shall the author be liable for any damages resulting from the use or inability to use this software. The author does not guarantee the accuracy or completeness of the information, text, graphics, links, or other items included in this software. The author may change this software at any time without notice. <br />
                The software does not assume any responsibility for any copyright issues or disputes arising from third-party information, data, and plugins created, downloaded, or used by users.<br /><br />
                III. Constraints and Restrictions of the Agreement: <br />
                Removing copyright information from Apple CMS source code is prohibited. The commercial licensed version may remove copyright information from the backend and frontend interfaces.<br />
                It is prohibited to develop any derivative versions, modified versions, or third-party versions based on Apple CMS in whole or in part for redistribution.</br></br>
                <strong>Copyright (c) 2020, Apple CMS. All rights reserved.</strong>',

    'install/user_agreement_agree'=>'Agree to the agreement and install the system',
    'install/environment_title'=>'Runtime Environment Check',
    'install/environment_name'=>'Environment Name',
    'install/current_config'=>'Current Configuration',
    'install/required_config'=>'Required Configuration',

    'install/dir_file'=>'Directory / File',
    'install/required_popedom'=>'Required Permissions',
    'install/current_popedom'=>'Current Permissions',

    'install/func_ext'=>'Functions / Extensions',
    'install/type'=>'Type',
    'install/result'=>'Result',
    'install/back_step'=>'Back to Previous Step',
    'install/next_step'=>'Proceed to Next Step',
    'install/question'=>'Frequently Asked Questions and Solutions',
    'install/database_config'=>'Database Configuration',

    'install/server_address'=>'Server Address',
    'install/server_address_tip'=>'Database server address, usually 127.0.0.1',
    'install/database_port'=>'Database Port',
    'install/database_port_tip'=>'System database port, usually 3306',
    'install/database_name'=>'Database Name',
    'install/database_name_tip'=>'System database name, must contain letters',
    'install/database_username'=>'Database Username',
    'install/database_username_tip'=>'Username for connecting to the database',
    'install/database_pass'=>'Database Password',
    'install/database_pass_tip'=>'Password for connecting to the database',
    'install/database_pre'=>'Database Prefix',
    'install/database_pre_tip'=>'Recommended to use default, database prefix must end with _',
    'install/overwrite_database'=>'Overwrite Database',
    'install/overwrite'=>'Overwrite',
    'install/not_overwrite'=>'Do Not Overwrite',
    'install/overwrite_tip'=>'Select "Do Not Overwrite" if you want to retain existing data',
    'install/test_connect'=>'Test Database Connection',
    'install/test_connect_tip'=>'Please click "Test Database Connection" before proceeding with the installation',
    'install/other_config'=>'Other Settings',
    'install/admin_name'=>'Administrator Account',
    'install/admin_name_tip'=>'Administrator account, minimum 4 characters',
    'install/admin_pass'=>'Administrator Password',
    'install/admin_pass_tip'=>'Password must be at least 6 characters',
    'install/init_data'=>'Initialize Data',
    'install/create'=>'Create',
    'install/not_create'=>'Do Not Create',
    'install/create_tip'=>'Whether to create basic category data',
    'install/exec'=>'Install Now',
    'install/submit_tip'=>'Please click and pass the database connection test first!',

    'install/environment_failed'=>'Environment check failed, cannot proceed to the next step!',
    'install/init_err'=>'Initialization failed!',
    'install/write_read_err'=>'No read and write permissions!',
    'install/not_found'=>'Not Found',
    'install/database_connect_err'=>'Database connection failed, please check database configuration!',
    'install/database_name_haved'=>'The database already exists and can be installed directly. If you need to overwrite, please select "Overwrite Database"!',
    'install/database_connect_ok'=>'Database connection successful',
    'install/access_denied'=>'Illegal access',
    'install/please_test_connect'=>'Please click "Test Database Connection" first!',
    'install/please_input_admin_name_pass'=>'Please enter the administrator account and password!',
    'install/sql_err'=>'Failed to import table structure SQL, please check if the syntax in install.sql is correct.',
    'install/init_data_err'=>'Failed to import initialization data SQL, please check if the syntax in initdata.sql is correct.',
    'install/admin_err'=>'Failed to create administrator account',
    'install/is_ok'=>'System installed successfully. Welcome to use Apple CMS for building your website',
    'install/os'=>'Operating System',
    'install/php'=>'PHP Version',
    'install/gd'=>'GD Library',

    'install/not_limited'=>'Not Limited',
    'install/not_installed'=>'Not Installed',
    'install/read_and_write'=>'Read and Write',
    'install/not_writable'=>'Not Writable',
    'install/support'=>'Supported',
    'install/not_support'=>'Not Supported',
    'install/class'=>'Class',
    'install/model'=>'Model',
    'install/function'=>'Function',
    'install/config'=>'Configuration',

    'validate/require_name'=>'Name is required',
    'validate/require_type'=>'Category is required',
    'validate/require_content'=>'Content is required',
    'validate/require_nick'=>'Nickname is required',
    'validate/require_mid'=>'Model ID is required',
    'validate/require_rid'=>'Related ID is required',
    'validate/require_pass'=>'Password is required',
    'validate/require_url'=>'URL is required',
    'validate/require_actor'=>'Actor is required',
    'validate/require_user'=>'User is required',
    'validate/require_no'=>'Card number is required',
    'validate/require_name_min'=>'Name must be at least 6 characters long',
    'validate/require_money'=>'Amount is required',
    'validate/require_points'=>'Points are required',
    'validate/require_bank_name'=>'Bank name is required',
    'validate/require_payee_name'=>'Payee name is required',
    'validate/require_bank_no'=>'Bank account number is required',
    'validate/require_sourcecharset'=>'Target website encoding type is required',
    'validate/require_sourcetype'=>'Current website type is required',
    'validate/require_urlpage'=>'Current website URL is required',
    'validate/require_order_code'=>'Order code is required',
    'validate/require_order_price'=>'Price is required',
    'validate/require_order_points'=>'Points are required',
    'validate/require_msg_to'=>'Receiving address is required',
    'validate/require_verify'=>'Verification code is required',
    'validate/require_path'=>'Path is required',
    'validate/require_tpl'=>'Template is required',
    'validate/require_ip'=>'IP address is required',
    'validate/require_time'=>'Time is required',
];