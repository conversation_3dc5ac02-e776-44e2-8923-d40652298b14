{include file="../../../application/admin/view/public/head" /}
<script>
    var MAC_VERSION='{$version.code}',PHP_VERSION='{php}echo PHP_VERSION{/php}',THINK_VERSION='{php}echo THINK_VERSION{/php}';MAC_LANG='{$mac_lang}';
</script>
<div class="page-container">
    <?php $pass="<strong class='green'>√</strong>";$error="<strong class='red'>×</strong>";?>

    <blockquote class="layui-elem-quote layui-quote-nm mt10">
        <p class="f-20 text-success">{:lang('admin/index/welcome/tip_warn')}</p>
        <p>{:lang('admin/index/welcome/filed_login_num')}：{$admin.admin_login_num}  {:lang('admin/index/welcome/filed_last_login_ip')}：{$admin.admin_last_login_ip|long2ip}  {:lang('admin/index/welcome/filed_last_login_time')}：{$admin.admin_last_login_time|mac_day}</p>
    </blockquote>

    <table class="layui-table" >
        <tbody>
        <tr>
            <td width="110">{:lang('admin/index/welcome/filed_os')}</td>
            <td><?php echo PHP_OS ?> (<?php echo $_SERVER['SERVER_SOFTWARE'] ?>)</td>
        </tr>
        <tr>
            <td>{:lang('admin/index/welcome/filed_host')}</td>
            <td><?php echo $_SERVER['HTTP_HOST'] ?></td>
        </tr>
        <tr>
            <td>{:lang('admin/index/welcome/filed_max_upload')}</td>
            <td><?php echo get_cfg_var("file_uploads") ? get_cfg_var("upload_max_filesize") : $error;?></td>
        </tr>
        <tr>
            <td>{:lang('admin/index/welcome/filed_date')}</td>
            <td><?php echo date('Y-m-d'); ?></td>
        </tr>
         <tr>
            <td>{:lang('admin/index/welcome/filed_php_ver')}</td>
            <td><?php echo PHP_VERSION ?></td>
        </tr>
        <tr>
            <td>{:lang('admin/index/welcome/filed_thinkphp_ver')}</td>
            <td><?php echo THINK_VERSION; ?></td>
        </tr>
        <tr>
            <td>{:lang('admin/index/welcome/filed_ver')}</td>
            <td><span class="layui-badge">{$version.code}</span></td>
        </tr>
        </tbody>
    </table>
    {if condition="$update_sql"}
    <table class="tbinfo pleft layui-table" ><thead><th colspan="2">{:lang('admin/index/welcome/tip_update_db')}</th></thead><tr><td colspan="2"><font class="tif s20">{:lang('admin/index/welcome/tip_update_db_txt')}</font><a class="j-iframe" title="{:lang('admin/index/welcome/tip_update_go')}" data-href="{:url('update/step2')}"><font class="tit s20">{:lang('admin/index/welcome/tip_update_go')}</font></a> </td></tr></table>
    {/if}
</div>
{include file="../../../application/admin/view/public/foot" /}
</body>
</html>