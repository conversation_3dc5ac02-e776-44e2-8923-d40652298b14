{include file="../../../application/admin/view/public/head" /}
<div class="page-container p10">

    <div class="my-toolbar-box">

        <div class="center mb10">
            <form class="layui-form " method="post" action="{:url('data')}">
                <div class="layui-input-inline w150">
                    <select name="status">
                        <option value="">{:lang('select_status')}</option>
                        <option value="0" {if condition="$param['status'] eq '0'"}selected {/if}>{:lang('reviewed_not')}</option>
                        <option value="1" {if condition="$param['status'] eq '1'"}selected {/if}>{:lang('reviewed')}</option>
                    </select>
                </div>
                <div class="layui-input-inline w150">
                    <select name="group">
                        <option value="">{:lang('select_group')}</option>
                        {volist name="group_list" id="vo"}
                        <option value="{$vo.group_id}" {if condition="$param['group'] eq $vo.group_id"}selected {/if}>{$vo.group_name}</option>
                        {/volist}
                    </select>
                </div>
                <div class="layui-input-inline">
                    <input type="text" autocomplete="off" placeholder="{:lang('wd')}" class="layui-input" name="wd" value="{$param['wd']|mac_filter_xss}">
                </div>
                <button class="layui-btn mgl-20 j-search" >{:lang('btn_search')}</button>
            </form>
        </div>

        <div class="layui-btn-group">
            <a data-href="{:url('info')}" class="layui-btn layui-btn-primary j-iframe"><i class="layui-icon">&#xe654;</i>{:lang('add')}</a>
            <a data-href="{:url('del')}" class="layui-btn layui-btn-primary j-page-btns confirm"><i class="layui-icon">&#xe640;</i>{:lang('del')}</a>
        </div>

    </div>

    <form class="layui-form " method="post" id="pageListForm">
        <table class="layui-table" lay-size="sm">
            <thead>
            <tr>
                <th width="25"><input type="checkbox" lay-skin="primary" lay-filter="allChoose"></th>
                <th width="100">{:lang('id')}</th>
                <th >{:lang('name')}</th>
                <th width="100">{:lang('group')}</th>
                <th width="80">{:lang('status')}</th>
                <th width="80">{:lang('points')}</th>
                <th width="130">{:lang('last_login_time')}</th>
                <th width="130">{:lang('last_login_ip')}</th>
                <th width="80">{:lang('login_num')}</th>
                <th width="220">{:lang('related_data')}</th>
                <th width="100">{:lang('opt')}</th>
            </tr>
            </thead>

            {volist name="list" id="vo"}
            <tr>
                <td><input type="checkbox" name="ids[]" value="{$vo.user_id}" class="layui-checkbox checkbox-ids" lay-skin="primary"></td>
                <td>{$vo.user_id}</td>
                <td>{$vo.user_name|htmlspecialchars}</td>
                <td>{$vo.group_name|htmlspecialchars}</td>
                <td>
                    <input type="checkbox" name="status" {if condition="$vo['user_status'] eq 1"}checked{/if} value="{$vo['user_status']}" lay-skin="switch" lay-filter="switchStatus" lay-text="{:lang('open')}|{:lang('close')}" data-href="{:url('field?col=user_status&ids='.$vo['user_id'])}">
                </td>
                <td>{$vo.user_points}</td>
                <td>{$vo.user_login_time|mac_day='color'}</td>
                <td>{$vo.user_login_ip|long2ip}</td>
                <td>{$vo.user_login_num}</td>
                <td>
                    <a class="layui-badge-rim j-iframe" data-full="1" data-href="{:url('comment/data?uid='.$vo['user_id'])}" href="javascript:;" title="{:lang('admin/user/comment_record')}">{:lang('admin/user/comment_record')}</a>
                    <a class="layui-badge-rim j-iframe" data-full="1" data-href="{:url('order/index?uid='.$vo['user_id'])}" href="javascript:;" title="{:lang('admin/user/order_record')}">{:lang('admin/user/order_record')}</a>
                    <a class="layui-badge-rim j-iframe" data-full="1" data-href="{:url('ulog/index?uid='.$vo['user_id'])}" href="javascript:;" title="{:lang('admin/user/visit_record')}">{:lang('admin/user/visit_record')}</a>
                    <a class="layui-badge-rim j-iframe" data-full="1" data-href="{:url('plog/index?uid='.$vo['user_id'])}" href="javascript:;" title="{:lang('admin/user/point_record')}">{:lang('admin/user/point_record')}</a>
                    <a class="layui-badge-rim j-iframe" data-full="1" data-href="{:url('cash/index?uid='.$vo['user_id'])}" href="javascript:;" title="{:lang('admin/user/withdrawals_record')}">{:lang('admin/user/withdrawals_record')}</a>
                    <a class="layui-badge-rim j-iframe" data-full="1" data-href="{:url('user/reward?uid='.$vo['user_id'])}" href="javascript:;" title="{:lang('admin/user/three_distribution')}">{:lang('admin/user/three_distribution')}</a>
                </td>
                <td>
                    <a class="layui-badge-rim j-iframe" data-href="{:url('info?id='.$vo['user_id'])}" href="javascript:;" title="{:lang('edit')}">{:lang('edit')}</a>
                    <a class="layui-badge-rim j-tr-del" data-href="{:url('del?ids='.$vo['user_id'])}" href="javascript:;" title="{:lang('del')}">{:lang('del')}</a>
                </td>
            </tr>
            {/volist}
            </tbody>
        </table>
        <div id="pages" class="center"></div>
    </form>
</div>

{include file="../../../application/admin/view/public/foot" /}

<script type="text/javascript">
    var curUrl="{:url('user/data',$param)}";
    layui.use(['laypage', 'layer'], function() {
        var laypage = layui.laypage
                , layer = layui.layer;

        laypage.render({
            elem: 'pages'
            ,count: {$total}
            ,limit: {$limit}
            ,curr: {$page}
            ,layout: ['count', 'prev', 'page', 'next', 'limit', 'skip']
            ,jump: function(obj,first){
                if(!first){
                    location.href = curUrl.replace('%7Bpage%7D',obj.curr).replace('%7Blimit%7D',obj.limit);
                }
            }
        });
    });
</script>
</body>
</html>