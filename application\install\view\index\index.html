{include file="../../../application/install/view/index/head" /}
<div class="install-box">
    <form class="layui-form layui-form-pane" action="" method="post">
        <div class="layui-form-item">
            <label class="layui-form-label">{:lang('install/lang')}</label>
            <div class="layui-input-inline w200 ">
                <select class="" name="lang" lay-filter="lang" style="z-index:99999;">
                    <option value="">{:lang('install/select_lang')}</option>
                    {volist name="langs" id="vo"}
                    <option value="{$vo}" {if condition="$lang eq $vo"}selected {/if}>{$vo}</option>
                    {/volist}
                </select>
            </div>
            <div class="layui-form-mid layui-word-aux">{:lang('install/lang_tip')}</div>
        </div>
    </form>

    <fieldset class="layui-elem-field site-demo-button">
        <legend>{:lang('install/user_agreement_title')}</legend>
        <div class="protocol">
            <p>
                {:lang('install/user_agreement')}
            </p>
        </div>
    </fieldset>
    <div class="step-btns">
        <a href="?step=2" class="layui-btn layui-btn-big layui-btn-normal">{:lang('install/user_agreement_agree')}</a>
    </div>
</div>
{include file="../../../application/install/view/index/foot" /}
<script type="text/javascript">
    var test=0;
    layui.define(['element', 'form'], function(exports) {
        var $ = layui.jquery, layer = layui.layer, form = layui.form;
        form.on('select(lang)',function(data){
            if(data.value !='') {
                location.href = "{:url('index')}?lang=" + (data.value);
            }
        });
    });
</script>