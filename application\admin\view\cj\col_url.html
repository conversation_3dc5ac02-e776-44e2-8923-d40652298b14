{include file="../../../application/admin/view/public/head" /}
<div class="page-container p10">

    <fieldset class="layui-elem-field">
        <legend>{:lang('base_info')}</legend>
        <div class="layui-field-box">
            {$url_list} <br>{:lang('sum')}：{$total} {:lang('data')}，{:lang('duplicate_data')}：{$re}{:lang('data')}，{:lang('distinct_into')}{$total-$re}{:lang('data')}。
        </div>
    </fieldset>

    <table class="layui-table" lay-size="sm">
    <thead>
      <tr>
        <th width="50">{:lang('serial_num')}</th>
		<th>{:lang('link')}</th>
        <th>{:lang('name')}</th>
      </tr> 
    </thead>
    <tbody>
		{foreach name="url" item="v"}
		  <tr>
			  <td>{$n=$n+1}</td>
			  <td>{$v.url}</td>
			  <td>{$v.title}</td>
		  </tr>
	  {/foreach}
    </tbody>
  </table>
</div>
<script>
var total_page = {$total_page};
var page = {$param['page']|mac_filter_xss};
var id = {$param['id']|mac_filter_xss};
if (total_page > page) {
    var url = "{:url('cj/col_url_list')}";
	page += 1;
    //location.href= url + '?page='+page+'&id='+id;
} else {
	//alert('采集完成');
}
</script>
</body>
</html>