const axios = require('axios');
const logger = require('../utils/logger');

// 代理连接监控
const proxyStats = {
  activeConnections: 0,
  totalConnections: 0,
  totalBytesTransferred: 0,
  connectionsByStream: new Map(),
  startTime: Date.now()
};

// 活跃连接管理
const activeConnections = new Map(); // 存储活跃的连接，key为连接ID，value为连接对象

// 获取代理统计信息
const getProxyStats = () => {
  const uptime = Date.now() - proxyStats.startTime;
  const avgBandwidth = proxyStats.totalBytesTransferred / (uptime / 1000); // bytes/second

  return {
    ...proxyStats,
    uptime: Math.floor(uptime / 1000), // seconds
    avgBandwidthMbps: (avgBandwidth * 8 / 1024 / 1024).toFixed(2), // Mbps
    uniqueStreams: proxyStats.connectionsByStream.size
  };
};

/**
 * 流媒体代理 - 处理HTTP-FLV流
 */
const streamProxy = async (req, res) => {
  const { url } = req.query;
  
  if (!url) {
    return res.status(400).json({
      success: false,
      message: 'URL parameter required'
    });
  }

  try {
    const streamUrl = decodeURIComponent(url);
    
    // 验证流地址 - 只允许HTTP-FLV
    const allowedPatterns = [
      /^http:\/\/.*\.flv$/,
      /^http:\/\/.*\/live\/cx_\d+\.flv$/,
      /api\.hclyz\.com/,
      /api\.maiyoux\.com/,
      /\.xinzhitushu\.xyz/,
      /\.jinxuan\.xyz/,
      /\.wljzml\.top/,
      /\.dianshe\.top/,
      /\.csfm\.xyz/
    ];
    
    const isValidHTTPFLV = allowedPatterns.some(pattern => 
      pattern.test(streamUrl)
    ) && streamUrl.startsWith('http://');
    
    if (!isValidHTTPFLV) {
      return res.status(403).json({
        success: false,
        message: 'Only HTTP-FLV streams are supported'
      });
    }
    
    // 返回代理信息供前端播放器使用
    res.json({
      success: true,
      data: {
        type: 'http-flv',
        originalUrl: streamUrl,
        proxyUrl: `/api/proxy/direct?url=${encodeURIComponent(streamUrl)}`,
        playable: true
      },
      timestamp: Date.now()
    });
    
  } catch (error) {
    logger.error('Stream proxy failed:', error);
    res.status(500).json({
      success: false,
      message: 'Stream proxy failed'
    });
  }
};

/**
 * 直接代理HTTP-FLV流
 */
const directProxy = async (req, res) => {
  const { url } = req.query;

  if (!url) {
    return res.status(400).json({
      success: false,
      message: 'URL parameter required'
    });
  }

  const streamUrl = decodeURIComponent(url);

  // 再次验证只允许HTTP-FLV
  if (!streamUrl.startsWith('http://') || !streamUrl.includes('.flv')) {
    return res.status(403).json({
      success: false,
      message: 'Only HTTP-FLV streams allowed'
    });
  }

  let streamResponse = null;
  const connectionId = `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

  try {
    // 更新统计信息
    proxyStats.activeConnections++;
    proxyStats.totalConnections++;

    // 记录每个流的连接数
    const streamConnections = proxyStats.connectionsByStream.get(streamUrl) || 0;
    proxyStats.connectionsByStream.set(streamUrl, streamConnections + 1);

    logger.info(`Starting proxy for stream: ${streamUrl}`, {
      connectionId,
      activeConnections: proxyStats.activeConnections,
      streamConnections: streamConnections + 1
    });

    const response = await axios({
      method: 'GET',
      url: streamUrl,
      responseType: 'stream',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Referer': 'https://yourdomain.com',
        'Accept': '*/*',
        'Connection': 'keep-alive'
      },
      timeout: 60000, // 增加到60秒
      maxRedirects: 5
    });

    streamResponse = response;

    // 存储活跃连接
    const connectionInfo = {
      id: connectionId,
      streamUrl,
      response: streamResponse,
      startTime: Date.now(),
      req,
      res
    };
    activeConnections.set(connectionId, connectionInfo);

    // 设置FLV流的响应头
    res.setHeader('Content-Type', 'video/x-flv');
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
    res.setHeader('X-Connection-ID', connectionId); // 返回连接ID给客户端

    // 流量统计
    let bytesTransferred = 0;

    // 处理客户端断开连接
    const cleanup = () => {
      proxyStats.activeConnections--;
      proxyStats.totalBytesTransferred += bytesTransferred;

      const currentStreamConnections = proxyStats.connectionsByStream.get(streamUrl) || 1;
      if (currentStreamConnections <= 1) {
        proxyStats.connectionsByStream.delete(streamUrl);
      } else {
        proxyStats.connectionsByStream.set(streamUrl, currentStreamConnections - 1);
      }

      // 从活跃连接中移除
      activeConnections.delete(connectionId);

      logger.info('Connection cleaned up', {
        connectionId,
        activeConnections: proxyStats.activeConnections,
        bytesTransferred,
        streamUrl
      });

      // 优雅地清理流连接
      if (streamResponse && streamResponse.data && !streamResponse.data.destroyed) {
        try {
          streamResponse.data.removeAllListeners();
          streamResponse.data.destroy();
        } catch (cleanupError) {
          logger.debug('Stream already destroyed during cleanup');
        }
      }
    };

    req.on('close', cleanup);
    req.on('aborted', cleanup);

    // 处理流错误
    response.data.on('error', (error) => {
      // 忽略连接中断错误，这是正常的断开行为
      if (error.code === 'ECONNABORTED' || error.code === 'ECONNRESET') {
        logger.debug('Stream connection aborted (normal disconnect):', error.code);
      } else {
        logger.error('Stream error:', error);
      }
      cleanup();
      if (!res.headersSent && !res.destroyed) {
        try {
          res.status(500).json({
            success: false,
            message: 'Stream error occurred'
          });
        } catch (resError) {
          logger.debug('Response already closed');
        }
      }
    });

    response.data.on('end', () => {
      logger.info('Stream ended');
      cleanup();
    });

    // 监控传输的数据量
    response.data.on('data', (chunk) => {
      bytesTransferred += chunk.length;
    });

    // 处理管道错误
    response.data.on('pipe', () => {
      logger.debug('Stream piped to response');
    });

    // 管道传输流数据，并处理管道错误
    response.data.pipe(res).on('error', (pipeError) => {
      // 忽略管道错误，通常是客户端断开连接导致的
      if (pipeError.code === 'ECONNABORTED' || pipeError.code === 'ECONNRESET') {
        logger.debug('Pipe connection aborted (normal disconnect):', pipeError.code);
      } else {
        logger.error('Pipe error:', pipeError);
      }
      cleanup();
    });

  } catch (error) {
    logger.error('Direct proxy failed:', {
      url: streamUrl,
      error: error.message,
      code: error.code,
      status: error.response?.status
    });

    // 清理资源
    if (streamResponse && streamResponse.data) {
      streamResponse.data.destroy();
    }

    if (!res.headersSent) {
      res.status(500).json({
        success: false,
        message: 'Stream unavailable',
        error: error.code === 'ECONNABORTED' ? 'Connection timeout' : 'Stream error'
      });
    }
  }
};

/**
 * 强制断开指定流的所有连接
 */
const forceDisconnectStream = (req, res) => {
  const { url } = req.query;

  if (!url) {
    return res.status(400).json({
      success: false,
      message: 'URL parameter required'
    });
  }

  const streamUrl = decodeURIComponent(url);
  let disconnectedCount = 0;

  try {
    // 查找并断开指定流的所有连接
    for (const [connectionId, connectionInfo] of activeConnections.entries()) {
      if (connectionInfo.streamUrl === streamUrl) {
        try {
          // 优雅地断开连接
          if (connectionInfo.response && connectionInfo.response.data) {
            // 先移除所有监听器，避免触发错误事件
            connectionInfo.response.data.removeAllListeners();
            // 然后销毁流
            connectionInfo.response.data.destroy();
          }

          // 优雅地关闭响应
          if (connectionInfo.res && !connectionInfo.res.headersSent && !connectionInfo.res.destroyed) {
            try {
              connectionInfo.res.end();
            } catch (resError) {
              // 如果响应已经关闭，忽略错误
              logger.debug(`Response already closed for connection ${connectionId}`);
            }
          }

          // 从活跃连接中移除
          activeConnections.delete(connectionId);
          disconnectedCount++;

          logger.info(`Force disconnected connection: ${connectionId} for stream: ${streamUrl}`);
        } catch (error) {
          logger.error(`Error disconnecting connection ${connectionId}:`, error);
          // 即使出错也要从活跃连接中移除
          activeConnections.delete(connectionId);
        }
      }
    }

    res.json({
      success: true,
      message: `Disconnected ${disconnectedCount} connections for stream`,
      data: {
        streamUrl,
        disconnectedConnections: disconnectedCount,
        remainingActiveConnections: activeConnections.size
      }
    });

  } catch (error) {
    logger.error('Force disconnect failed:', error);
    res.status(500).json({
      success: false,
      message: 'Force disconnect failed'
    });
  }
};

/**
 * 获取代理统计信息
 */
const getStats = (req, res) => {
  const stats = getProxyStats();

  res.json({
    success: true,
    data: {
      ...stats,
      activeConnectionsList: Array.from(activeConnections.values()).map(conn => ({
        id: conn.id,
        streamUrl: conn.streamUrl,
        startTime: conn.startTime,
        duration: Date.now() - conn.startTime
      })),
      message: 'CDN环境下的代理统计',
      impact: {
        serverBandwidth: `${stats.avgBandwidthMbps} Mbps (上行+下行)`,
        cdnBenefit: 'CDN承担用户端传输，但源站压力不变',
        recommendation: stats.activeConnections > 20 ? '建议限制并发连接数' : '当前负载正常'
      }
    }
  });
};

module.exports = {
  streamProxy,
  directProxy,
  forceDisconnectStream,
  getStats
};
