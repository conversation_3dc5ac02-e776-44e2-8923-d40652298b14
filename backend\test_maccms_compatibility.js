#!/usr/bin/env node

/**
 * 测试苹果CMS采集接口兼容性
 * 直接测试接口输出格式是否符合苹果CMS标准
 */

const http = require('http');
const express = require('express');

// 启动测试服务器
async function startTestServer() {
  const app = express();
  
  // 加载采集路由
  const collectRouter = require('./src/routes/api/collect.js');
  app.use('/api.php/provide', collectRouter);
  
  const server = app.listen(3001, () => {
    console.log('🚀 测试服务器启动在端口 3001');
  });
  
  return server;
}

// 发送HTTP请求
function makeRequest(path) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3001,
      path: path,
      method: 'GET'
    };

    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          body: data
        });
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    req.end();
  });
}

// 验证苹果CMS JSON格式
function validateMacCMSJSON(data) {
  const errors = [];
  
  try {
    const json = JSON.parse(data);
    
    // 检查必需字段
    const requiredFields = ['code', 'msg', 'page', 'pagecount', 'limit', 'total', 'list'];
    requiredFields.forEach(field => {
      if (!(field in json)) {
        errors.push(`缺少必需字段: ${field}`);
      }
    });
    
    // 检查数据类型
    if (typeof json.code !== 'number') errors.push('code 应该是数字类型');
    if (typeof json.msg !== 'string') errors.push('msg 应该是字符串类型');
    if (typeof json.page !== 'number') errors.push('page 应该是数字类型');
    if (typeof json.pagecount !== 'number') errors.push('pagecount 应该是数字类型');
    if (typeof json.limit !== 'string') errors.push('limit 应该是字符串类型');
    if (typeof json.total !== 'number') errors.push('total 应该是数字类型');
    if (!Array.isArray(json.list)) errors.push('list 应该是数组类型');
    
    // 检查视频列表字段
    if (json.list && json.list.length > 0) {
      const video = json.list[0];
      const videoFields = ['vod_id', 'vod_name', 'type_id', 'type_name', 'vod_time'];
      videoFields.forEach(field => {
        if (!(field in video)) {
          errors.push(`视频对象缺少字段: ${field}`);
        }
      });
    }
    
    // 检查分类信息（列表接口）
    if (json.class && !Array.isArray(json.class)) {
      errors.push('class 应该是数组类型');
    }
    
    return {
      valid: errors.length === 0,
      errors: errors,
      data: json
    };
    
  } catch (e) {
    return {
      valid: false,
      errors: [`JSON解析失败: ${e.message}`],
      data: null
    };
  }
}

// 验证苹果CMS XML格式
function validateMacCMSXML(data) {
  const errors = [];
  
  // 基本XML结构检查
  if (!data.includes('<?xml version="1.0" encoding="utf-8"?>')) {
    errors.push('缺少XML声明');
  }
  
  if (!data.includes('<rss version="2.0">')) {
    errors.push('缺少RSS根元素');
  }
  
  if (!data.includes('<list ')) {
    errors.push('缺少list元素');
  }
  
  // 检查分页属性
  const listMatch = data.match(/<list\s+([^>]+)>/);
  if (listMatch) {
    const attrs = listMatch[1];
    const requiredAttrs = ['page=', 'pagecount=', 'pagesize=', 'recordcount='];
    requiredAttrs.forEach(attr => {
      if (!attrs.includes(attr)) {
        errors.push(`list元素缺少属性: ${attr.replace('=', '')}`);
      }
    });
  }
  
  // 检查视频元素
  if (!data.includes('<video>')) {
    errors.push('缺少video元素');
  }
  
  return {
    valid: errors.length === 0,
    errors: errors
  };
}

// 主测试函数
async function testMacCMSCompatibility() {
  console.log('🧪 开始测试苹果CMS兼容性...\n');
  
  let server;
  
  try {
    // 启动测试服务器
    server = await startTestServer();
    
    // 等待服务器启动
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    console.log('📋 测试1: 视频列表接口 (JSON格式)');
    console.log('请求: /api.php/provide/vod?ac=list&pg=1&pagesize=5');
    
    const listResponse = await makeRequest('/api.php/provide/vod?ac=list&pg=1&pagesize=5');
    console.log(`状态码: ${listResponse.statusCode}`);
    console.log(`Content-Type: ${listResponse.headers['content-type']}`);
    
    if (listResponse.statusCode === 200) {
      const validation = validateMacCMSJSON(listResponse.body);
      if (validation.valid) {
        console.log('✅ JSON格式验证通过');
        console.log(`   - 返回 ${validation.data.total} 条记录`);
        console.log(`   - 当前第 ${validation.data.page} 页，共 ${validation.data.pagecount} 页`);
        console.log(`   - 视频列表包含 ${validation.data.list.length} 个项目`);
        if (validation.data.class) {
          console.log(`   - 分类列表包含 ${validation.data.class.length} 个分类`);
        }
      } else {
        console.log('❌ JSON格式验证失败:');
        validation.errors.forEach(error => console.log(`   - ${error}`));
      }
    } else {
      console.log(`❌ 请求失败，状态码: ${listResponse.statusCode}`);
      console.log('响应内容:', listResponse.body.substring(0, 200));
    }
    
    console.log('\n📄 测试2: 视频详情接口 (JSON格式)');
    console.log('请求: /api.php/provide/vod?ac=detail&ids=1');
    
    const detailResponse = await makeRequest('/api.php/provide/vod?ac=detail&ids=1');
    console.log(`状态码: ${detailResponse.statusCode}`);
    
    if (detailResponse.statusCode === 200) {
      const validation = validateMacCMSJSON(detailResponse.body);
      if (validation.valid) {
        console.log('✅ 详情接口JSON格式验证通过');
        if (validation.data.list.length > 0) {
          const video = validation.data.list[0];
          console.log(`   - 视频ID: ${video.vod_id}`);
          console.log(`   - 视频名称: ${video.vod_name}`);
          console.log(`   - 播放地址: ${video.vod_play_url ? '已设置' : '未设置'}`);
        }
      } else {
        console.log('❌ 详情接口JSON格式验证失败:');
        validation.errors.forEach(error => console.log(`   - ${error}`));
      }
    } else {
      console.log(`❌ 详情请求失败，状态码: ${detailResponse.statusCode}`);
    }
    
    console.log('\n📄 测试3: XML格式输出');
    console.log('请求: /api.php/provide/vod?ac=list&at=xml&pg=1&pagesize=3');
    
    const xmlResponse = await makeRequest('/api.php/provide/vod?ac=list&at=xml&pg=1&pagesize=3');
    console.log(`状态码: ${xmlResponse.statusCode}`);
    console.log(`Content-Type: ${xmlResponse.headers['content-type']}`);
    
    if (xmlResponse.statusCode === 200) {
      const validation = validateMacCMSXML(xmlResponse.body);
      if (validation.valid) {
        console.log('✅ XML格式验证通过');
        console.log(`   - XML长度: ${xmlResponse.body.length} 字符`);
      } else {
        console.log('❌ XML格式验证失败:');
        validation.errors.forEach(error => console.log(`   - ${error}`));
      }
      
      // 显示XML片段
      console.log('\nXML输出片段:');
      console.log(xmlResponse.body.substring(0, 300) + '...');
    } else {
      console.log(`❌ XML请求失败，状态码: ${xmlResponse.statusCode}`);
    }
    
    console.log('\n🎉 苹果CMS兼容性测试完成！');
    
  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error.message);
  } finally {
    if (server) {
      server.close();
      console.log('🔚 测试服务器已关闭');
    }
  }
}

// 运行测试
if (require.main === module) {
  testMacCMSCompatibility();
}

module.exports = { testMacCMSCompatibility };
