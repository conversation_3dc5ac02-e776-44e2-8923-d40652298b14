{include file="../../../application/admin/view/public/head" /}
<div class="page-container p10">
    <form class="layui-form layui-form-pane" method="post" action="">
        <input type="hidden" name="__token__" value="{$Request.token}" />
                    <div class="layui-form-item">
                        <label class="layui-form-label">{:lang('status')}：</label>
                        <div class="layui-input-inline">
                            <input name="status" type="radio" id="rad-1" value="0" title="{:lang('disable')}" {if condition="$info['status'] neq 1"}checked {/if}>
                            <input name="status" type="radio" id="rad-2" value="1" title="{:lang('enable')}" {if condition="$info['status'] eq 1"}checked {/if}>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">{:lang('name')}：</label>
                        <div class="layui-input-inline w500">
                            <input type="text" class="layui-input" value="{$info.name}" placeholder="{:lang('admin/timming/unique_id')}" id="name" name="name" {if condition="$info.name neq ''"} readonly="readonly"{/if}>
                        </div>
                        <div class="layui-form-mid layui-word-aux">{:lang('admin/timming/call_method')}/api.php/timming/index?name={$info.name}</div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">{:lang('remarks')}：</label>
                        <div class="layui-input-inline w500">
                            <input type="text" class="layui-input" value="{$info.des}" placeholder="" id="des" name="des">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">{:lang('admin/timming/exec_file')}：</label>
                        <div class="layui-input-inline w500">
                            <select class="" name="file">
                                <option value="collect" {if condition="$info['file'] eq 'collect'"}selected{/if}>{:lang('admin/timming/collect')}</option>
                                <option value="make" {if condition="$info['file'] eq 'make'"}selected{/if}>{:lang('admin/timming/make')}</option>
                                <option value="cj" {if condition="$info['file'] eq 'cj'"}selected{/if}>{:lang('admin/timming/cj')}</option>
                                <option value="cache" {if condition="$info['file'] eq 'cache'"}selected{/if}>{:lang('admin/timming/cache')}</option>
                                <option value="urlsend" {if condition="$info['file'] eq 'urlsend'"}selected{/if}>{:lang('admin/timming/urlsend')}</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">{:lang('admin/timming/attach_param')}：</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" value="{$info.param}" placeholder="{:lang('admin/timming/attach_param_tip')}" id="param" name="param">
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">{:lang('admin/timming/exec_cycle')}：</label>
                        <div class="layui-input-block role-list-form">
                            <input type="checkbox" name="weeks[]" class="layui-checkbox" lay-skin="primary" value="1" title="{:lang('monday')}" {if condition="strpos($info['weeks'],'1')!==false"}checked{/if}>
                            <input type="checkbox" name="weeks[]" class="layui-checkbox" lay-skin="primary" value="2" title="{:lang('tuesday')}" {if condition="strpos($info['weeks'],'2')!==false"}checked{/if}>
                            <input type="checkbox" name="weeks[]" class="layui-checkbox" lay-skin="primary" value="3" title="{:lang('wednesday')}" {if condition="strpos($info['weeks'],'3')!==false"}checked{/if}>
                            <input type="checkbox" name="weeks[]" class="layui-checkbox" lay-skin="primary" value="4" title="{:lang('thursday')}" {if condition="strpos($info['weeks'],'4')!==false"}checked{/if}>
                            <input type="checkbox" name="weeks[]" class="layui-checkbox" lay-skin="primary" value="5" title="{:lang('friday')}" {if condition="strpos($info['weeks'],'5')!==false"}checked{/if}>
                            <input type="checkbox" name="weeks[]" class="layui-checkbox" lay-skin="primary" value="6" title="{:lang('saturday')}" {if condition="strpos($info['weeks'],'6')!==false"}checked{/if}>
                            <input type="checkbox" name="weeks[]" class="layui-checkbox" lay-skin="primary" value="0" title="{:lang('sunday')}" {if condition="strpos($info['weeks'],'0')!==false"}checked{/if}>

                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">{:lang('admin/timming/exec_time')}：</label>
                        <div class="layui-input-block role-list-form">
                            <input type="checkbox" name="hours[]" class="layui-checkbox" lay-skin="primary" value="00" title="00" {if condition="strpos($info['hours'],'00')!==false"}checked{/if}>
                            <input type="checkbox" name="hours[]" class="layui-checkbox" lay-skin="primary" value="01" title="01" {if condition="strpos($info['hours'],'01')!==false"}checked{/if}>
                            <input type="checkbox" name="hours[]" class="layui-checkbox" lay-skin="primary" value="02" title="02" {if condition="strpos($info['hours'],'02')!==false"}checked{/if}>
                            <input type="checkbox" name="hours[]" class="layui-checkbox" lay-skin="primary" value="03" title="03" {if condition="strpos($info['hours'],'03')!==false"}checked{/if}>
                            <input type="checkbox" name="hours[]" class="layui-checkbox" lay-skin="primary" value="04" title="04" {if condition="strpos($info['hours'],'04')!==false"}checked{/if}>
                            <input type="checkbox" name="hours[]" class="layui-checkbox" lay-skin="primary" value="05" title="05" {if condition="strpos($info['hours'],'05')!==false"}checked{/if}>
                            <input type="checkbox" name="hours[]" class="layui-checkbox" lay-skin="primary" value="06" title="06" {if condition="strpos($info['hours'],'06')!==false"}checked{/if}>
                            <input type="checkbox" name="hours[]" class="layui-checkbox" lay-skin="primary" value="07" title="07" {if condition="strpos($info['hours'],'07')!==false"}checked{/if}>
                            <input type="checkbox" name="hours[]" class="layui-checkbox" lay-skin="primary" value="08" title="08" {if condition="strpos($info['hours'],'08')!==false"}checked{/if}>
                            <input type="checkbox" name="hours[]" class="layui-checkbox" lay-skin="primary" value="09" title="09" {if condition="strpos($info['hours'],'09')!==false"}checked{/if}>
                            <input type="checkbox" name="hours[]" class="layui-checkbox" lay-skin="primary" value="10" title="10" {if condition="strpos($info['hours'],'10')!==false"}checked{/if}>
                            <input type="checkbox" name="hours[]" class="layui-checkbox" lay-skin="primary" value="11" title="11" {if condition="strpos($info['hours'],'11')!==false"}checked{/if}>
                            <input type="checkbox" name="hours[]" class="layui-checkbox" lay-skin="primary" value="12" title="12" {if condition="strpos($info['hours'],'12')!==false"}checked{/if}>
                            <input type="checkbox" name="hours[]" class="layui-checkbox" lay-skin="primary" value="13" title="13" {if condition="strpos($info['hours'],'13')!==false"}checked{/if}>
                            <input type="checkbox" name="hours[]" class="layui-checkbox" lay-skin="primary" value="14" title="14" {if condition="strpos($info['hours'],'14')!==false"}checked{/if}>
                            <input type="checkbox" name="hours[]" class="layui-checkbox" lay-skin="primary" value="15" title="15" {if condition="strpos($info['hours'],'15')!==false"}checked{/if}>
                            <input type="checkbox" name="hours[]" class="layui-checkbox" lay-skin="primary" value="16" title="16" {if condition="strpos($info['hours'],'16')!==false"}checked{/if}>
                            <input type="checkbox" name="hours[]" class="layui-checkbox" lay-skin="primary" value="17" title="17" {if condition="strpos($info['hours'],'17')!==false"}checked{/if}>
                            <input type="checkbox" name="hours[]" class="layui-checkbox" lay-skin="primary" value="18" title="18" {if condition="strpos($info['hours'],'18')!==false"}checked{/if}>
                            <input type="checkbox" name="hours[]" class="layui-checkbox" lay-skin="primary" value="19" title="19" {if condition="strpos($info['hours'],'19')!==false"}checked{/if}>
                            <input type="checkbox" name="hours[]" class="layui-checkbox" lay-skin="primary" value="20" title="20" {if condition="strpos($info['hours'],'20')!==false"}checked{/if}>
                            <input type="checkbox" name="hours[]" class="layui-checkbox" lay-skin="primary" value="21" title="21" {if condition="strpos($info['hours'],'21')!==false"}checked{/if}>
                            <input type="checkbox" name="hours[]" class="layui-checkbox" lay-skin="primary" value="22" title="22" {if condition="strpos($info['hours'],'22')!==false"}checked{/if}>
                            <input type="checkbox" name="hours[]" class="layui-checkbox" lay-skin="primary" value="23" title="23" {if condition="strpos($info['hours'],'23')!==false"}checked{/if}>
                        </div>
                    </div>
        

        <div class="layui-form-item center">
            <div class="layui-input-block">
                <button type="button" class="layui-btn layui-btn-normal formCheckAll" lay-filter="formCheckAll" >{:lang('check_all')}</button>
                <button type="button" class="layui-btn layui-btn-normal formCheckOther" lay-filter="formCheckOther">{:lang('check_other')}</button>

                <button type="submit" class="layui-btn" lay-submit="" lay-filter="formSubmit" data-child="true">{:lang('btn_save')}</button>
                <button class="layui-btn layui-btn-warm" type="reset">{:lang('btn_reset')}</button>
            </div>
        </div>
    </form>

</div>
{include file="../../../application/admin/view/public/foot" /}
<script type="text/javascript">

    layui.use(['form', 'layer'], function () {
        // 操作对象
        var form = layui.form
                , layer = layui.layer
                , $ = layui.jquery;

        // 验证
        form.verify({
            show: function (value) {
                if (value == "") {
                    return "{:lang('name_empty')}";
                }
            }
        });

        $('.formCheckAll').click(function(){
            var child = $('.role-list-form').find('input');
            /* 自动选中子节点 */
            child.each(function(index, item) {
                item.checked = true;
            });
            form.render('checkbox');
        });
        $('.formCheckOther').click(function(){
            var child = $('.role-list-form').find('input');
            /* 自动选中子节点 */
            child.each(function(index, item) {
                item.checked = (item.checked  ? false : true);
            });
            form.render('checkbox');
        });


    });
</script>

</body>
</html>