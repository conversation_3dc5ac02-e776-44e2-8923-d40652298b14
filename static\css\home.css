/* mac_* 样式 */

/*延时加载*/
.lazy{background:url('../images/home/<USER>') center no-repeat}

/*滑动显示*/
.mac_drop_box {
	text-align:left; padding: 5px;margin-top:8px;border: 1px solid #ddd; clear: both; display: none; position: absolute; background-color: #f6f6f6; z-index:9999;
}
.mac_drop_box::after{
	content: '';
	width: 0px;
	height: 0px;
	border-color: transparent transparent #f6f6f6 transparent;
	border-style: solid;
	border-width: 6px;
	position: absolute;
	top: -12px;
	border-radius: 3px;
	left: 41px;
	right: auto;
}
.mac_drop_box::before{
	content: '';
	width: 0px;
	height: 0px;
	border-color: transparent transparent #ddd transparent;
	border-style: solid;
	border-width: 7px;
	position: absolute;
	top: -14px;
	border-radius: 3px;
	left: 40px;
	right: auto;
}
.mac_user_box::after{left: 21px;}
.mac_user_box::before{left: 20px;}
/*用户滑动*/
.mac_user_box { width:100px; margin-left: -20px;}
.mac_user_box ul{}
.mac_user_box ul li{margin: 3px 0;}

/*历史记录*/
.mac_history_box { width:180px; margin-left: -20px;}
.mac_history_box dt{text-align:right; padding-right:15px; height:24px; line-height:24px; border-bottom:1px solid #F9F9F9; cursor:pointer}
.mac_history_box dt a{color:#000000}
.mac_history_box dd{clear:both;margin:0px;height:24px;line-height:24px;text-overflow:ellipsis;white-space: nowrap;width: 96%;padding:2px;overflow: hidden;}
.mac_history_box dd a{color:#990033; display:block;padding-left:0px;}
.mac_history_box dd.odd {background-color: #eee;}
.mac_history_box dd a:hover{background-color: #acacac;color: white;}



/*验证码*/
.mac_verify_img {
	width: 70px;
	height: 30px;
	line-height: 30px;
	vertical-align: middle;
}

.mac_comment,.mac_digg{font-size: 12px;}
.fl {float: left; _display: inline;}
.fr {float: right; _display: inline;}
.clearfix:after {visibility:hidden;display:block;font-size:0;content:" ";clear:both;height:0} .clearfix{*zoom:1;}

/*-Pages-*/
.mac_pages {text-align:center; padding:4px 0; overflow:hidden;}
.mac_pages .page_tip {margin-left:4px; font-weight:bold; color:#7f7f7f;  font-weight:normal;}
.mac_pages .page_link{display:inline-block; padding:2px 4px; border:1px solid #ddd; margin-left:4px;}
.mac_pages .page_link{display:inline-block; padding:2px 4px; border:1px solid #ddd; margin-left:4px;}
.mac_pages .page_current{display:inline-block; padding:2px 4px; background-color : #fee; border : 1px solid red; color : red; margin-left:4px;}
.mac_pages .page_input {margin-left:4px; height:22px; width:30px;}
.mac_pages .page_btn{border:1px solid #CCCCCC; height:24px; width:40px;text-align:center; cursor: hand;}
/*-/Page-*/


/*文章顶踩图标*/
.mac_art_digg{ width:400px; margin:0px auto; clear:both; padding:10px 0px; overflow:hidden}
.mac_art_digg .digg_artup,.mac_art_digg .digg_artdown {background:url('../images/home/<USER>') no-repeat -189px 0;border:0;cursor:pointer;float:left;height:48px;margin:0;overflow:hidden;padding:0;position:relative;width:189px;}
#digg_artup_num, #digg_artdown_num {color:#333333;font-family:arial;font-size:10px;font-weight:400;left:70px;line-height:12px;position:absolute;top:30px;}
.mac_art_digg .digg_bar {background-color:#FFFFFF;border:1px solid #40A300;height:5px;left:9px;overflow:hidden;position:absolute;text-align:left;top:32px;width:55px;}
.mac_art_digg.digg_bar div{background:transparent url('../images/home/<USER>') repeat-x scroll 0 -265px;height:5px;overflow:hidden;width:0px}
.mac_art_digg.digg_artdown {background-position:-378px 0;margin-left:10px;}
.mac_art_digg.digg_artdown .dig_bar {border-color:#555555;}
.mac_art_digg.digg_artdown .dig_bar div{background-position:0 -270px;}


/*搜索下拉*/
.mac_results {background-color:#fcfcfc;overflow:hidden;z-index:99999;margin:0;padding:0;text-align:left;min-width: 290px;box-shadow: 1px 1px 3px 1px #ccc;}
.mac_results ul {width: 100%;list-style-position: outside;list-style: none;padding: 0;margin: 0;}
.mac_results li {margin: 0px;padding: 2px 5px;cursor: default;display: block;font: menu;font-size: 12px;height:25px; line-height: 25px;overflow: hidden;}
.mac_results .mac_loading {background: white right center no-repeat;}
.mac_results .ac_odd {background-color: #f6f6f6;}
.mac_results .ac_over {background-color: #0A246A;color: white;}




/*普通评分*/
.mac_score { height:60px; }
.mac_score .score_info strong{font-size:14px;color:red}
.mac_score .score_tip{position:absolute;width:140px;color:#999;font-size:14px;margin-left:0px;}
.mac_score .score_hover{margin:24px 8px 0 0;width:30px;color:green;font-family:Arial;font-weight:bold;font-size:14px;}
.mac_score .score_shi{font-size:32px;line-height:28px;font-weight:bold;}
.mac_score .score_ge{font-size:18px;line-height:22px;}
.mac_score p{width:100px;float:left;color:#666;}
.mac_score p span{font-family:Arial;font-style:italic;color:#f60;}
.mac_score p span.score_no{width:110px;clear:both;color:#666;font-style:normal;font-size:12px;}
.mac_score .score_btn{width:55px;height:21px}


/*星星评分*/
.mac_star{
	font-weight:normal;width: 240px;
}
.mac_star dd{display: inline-block;float: right;margin-left: 10px;}
.mac_star dt{
	color:#999;
	font-size:1.0em;
	display: inline-block;
	float: left;
	margin-top: 3px;
}
.mac_star .star_box{
	color: #00b269;
	font-size:0.5em;
}
.mac_star .star_tips{
	font-size:1.8em;
	line-height: 0.5;
	color:#ffc00a;
	font-family: Arial, Helvetica, sans-serif;
}
.mac_star i{background: url("../images/home/<USER>") no-repeat;cursor: pointer;font-size: 1.5em;color: transparent;}
.mac_star i.star-half-png,.mac_star i.star-on-png{background: url("../images/home/<USER>") 0 -35px no-repeat;}

/*评论*/
.mac_comment { margin-top:5px; padding:10px; padding-left:10px; border:1px solid #C6D9E7; background:#fff; font-family: "Microsoft YaHei"; }


.mac_comment .clearfix::after {
	height: 0px; clear: both; font-size: 0px; display: block; visibility: hidden; content: " ";
}

.mac_comment .cmt_form {
	margin-bottom: 10px;
}
.mac_comment .face_wrap img {
	width: 50px; height: 50px;
}
.mac_comment .input_wrap {
	width: 98%; padding-left: 1%; position: relative;
}
.mac_comment .cmt_wrap .input_wrap {
	width: 98%;
}
.mac_comment .input_wrap textarea {
	background: #fff; border-width: 1px 1px medium; border-style: solid solid none; border-color: rgb(187, 187, 187) rgb(187, 187, 187) currentColor; border-radius: 3px 3px 0px 0px; width: 100%; height: 53px; line-height: 1.4; overflow: auto; padding-top: 8px; padding-left: 1%;color: #666;box-sizing: border-box;
}
.mac_comment .input_wrap .handle {
	position:relative;background: #fbfbfb; border-width: 1px; border-style:solid; border-color: rgb(187, 187, 187); border-top-color:#e5e5e5;border-radius: 0px 0px 3px 3px; width: 100%; height:46px; color: rgb(206, 206, 206);box-sizing: border-box;
}
.mac_comment .input_wrap .face{
	cursor: pointer;position: absolute;width: 42px;height: 45px;cursor: pointer;z-index: 2;
}
.mac_comment .input_wrap .face.curr{background: #fff;}
.mac_comment .input_wrap .face-box{display:none;padding: 8px 0 3px 5px;width: 280px;position: absolute;top: 44px;background: #fff;border: 1px solid #bbb;border-top-color:#e5e5e5;margin-left: -1px;z-index: 1;}
.mac_comment .input_wrap .face-box img{
	display:inline-block;margin: 2px;
}
.mac_comment .icon-face{display:block;width:42px;height:44px;background: url("../images/home/<USER>") center no-repeat;}
.mac_comment .icon-face:hover,.mac_comment .input_wrap .face.curr .icon-face{background: url("../images/home/<USER>") center no-repeat;}
.mac_comment .expression {
	background: url("../images/home/<USER>") no-repeat 0px 0px; margin: 6px 0px 0px 6px; width: 18px; height: 16px; display: block; cursor: pointer;
}
.mac_comment .expression:hover {
	background: url("../images/home/<USER>") no-repeat 0px -16px;
}
.mac_comment .remaining-w {
	float: left;margin-left: 42px;padding-left:20px;line-height:44px;border-left: 1px solid #e5e5e5; font-size: 14px;
}
.mac_comment .smt_wrap {
	width: 100%; margin-top: 8px;
}
.mac_comment .smt_wrap span {
	color: rgb(66, 66, 66);
}
.mac_comment .smt_wrap .total {
	font-size: 16px; font-weight: bold;
}
.mac_comment .smt_wrap span span {
	margin: 0px; padding: 0px;
}
.mac_comment .smt_wrap span a {
	margin: 0px 0px 0px 8px; color: rgb(49, 139, 183);
}
.mac_comment .smt .cmt_post:hover{background: #17abff;}
.mac_comment .smt .cmt_post {
	background: #169dea; border: currentColor; width: 81px; height: 30px; text-align: center; color: rgb(255, 255, 255); line-height: 30px; font-size: 14px; margin-left: 20px; display: inline-block; cursor: pointer;vertical-align:middle;border-radius: 3px;
}
.mac_comment .smt .cmt_text {
	background: rgb(251, 251, 251); padding: 0px 8px; border-radius: 3px; border: 1px solid rgb(204, 204, 204); width:50px; height: 30px; line-height: 30px; margin-left: 5px;margin-right:2px;vertical-align:middle;
}
.mac_comment .smt .cmt_verify {
	 width: 80px; height: 30px; line-height: 30px;vertical-align:middle;
}

.mac_comment .smt input:hover {
	background-position: -1px -603px;
}
.mac_comment .smt{margin-top: 6px;margin-right: 12px;color: #454545;}
.mac_comment .sort_wrap {
	padding-bottom: 2px; border-bottom-color: rgb(215, 215, 215); border-bottom-width: 1px; border-bottom-style: solid;
}
.mac_comment .sort_wrap a:hover {
	text-decoration: none;
}
.mac_comment .sort_wrap .current {
	color: rgb(49, 139, 183); font-weight: bold; border-bottom-color: rgb(43, 140, 230); border-bottom-width: 2px; border-bottom-style: solid;
}
.mac_comment .cmt_item {
	padding: 15px 0px 8px; width: 100%; color: rgb(153, 153, 153); border-bottom-color: rgb(215, 215, 215); border-bottom-width: 1px; border-bottom-style: dotted; _zoom: 1;
}
.mac_comment .item_con {
	width: 93%; padding-left: 1%;
}
.mac_comment .item_con p.top {
	padding: 0;
	width: auto;
	margin-bottom: 8px;
}
.mac_comment .item_con a {
	color: rgb(153, 153, 153);
}
.mac_comment .item_con a:hover {
	color: rgb(153, 153, 153); text-decoration: none;
}
.mac_comment .item_con .name {
	color: rgb(43, 140, 230);
}
.mac_comment .item_con .name:hover {
	color: rgb(1, 106, 159); text-decoration: none;
}
.mac_comment .item_con .con {
	color: rgb(51, 51, 51); line-height: 20px; overflow: hidden; margin-top: 8px; -ms-word-wrap: break-word;
}
.mac_comment .item_con .bottom li {
	height: 16px; line-height: 15px; overflow: hidden; margin-right: 10px; float: left; _display: inline;
}
.mac_comment .item_con .bottom .reply {
	background: url("../images/home/<USER>") no-repeat 0px 0px; color: rgb(153, 153, 153); text-indent: 19px; font-size: 11px; display: block;
}
.mac_comment .item_con .bottom .reply:hover {
	color: rgb(1, 106, 159); text-decoration: none;
}
.mac_comment .item_con .bottom .bdshare_t {
	margin-top: -5px;
}
.mac_comment .item_con .bottom .bds_more {
	padding-top: 5px;
}
.mac_comment .inner {
	padding: 3px; border: 1px solid rgb(153, 153, 153); width: auto; background-color: rgb(254, 254, 241);
}
.mac_comment .inner .top {
	margin: 8px 0px 8px 3px;
}
.mac_comment .inner .con {
	margin: 5px 0px 5px 3px;
}
.mac_comment .inner .bottom {
	margin-bottom: 5px; visibility: hidden;
}
.mac_comment .mid_cmt_item {
	margin-top: 3px; border-top-color: currentColor; border-top-width: medium; border-top-style: none;
}
.mac_comment .pages {
	margin-top: 15px;
}
.mac_comment .item_con .gw-action{
	text-align: right;
}
.mac_comment .item_con .gw-action a{margin-left: 10px;}
.mac_comment .item_con a:hover{
	color: #333;
}
.mac_comment .item_con .click-ding-gw{margin-right: 10px;}
.mac_comment .item_con .click-ding-gw a,.mac_digg .click-ding-gw a{color: #f29898;}
.mac_comment .item_con .click-ding-gw a:hover,.mac_digg .click-ding-gw a{color: #f29898;}
.mac_comment .item_con .click-ding-gw a i,.mac_digg a i{display: inline-block;width: 14px;height: 14px;vertical-align: middle;}

.mac_comment .cmt_wrap .item_con .input_wrap{width: 100%;margin: 10px 0;padding-left: 0;}
.mac_comment .cmt_wrap .item_con .cmt_item{clear: left;border-bottom: none;}
.mac_comment .cmt_wrap .item_con .cmt_item .gw-action{text-align: left;clear: left;}
.mac_comment .cmt_wrap .item_con .cmt_item .gw-action a{margin-right: 10px;margin-left: 0;}

/*留言气泡版本*/
.reply_box .mac_msg_l{padding:10px 1%;float: left;width:70%;}
.reply_box .mac_msg_l .mac_msg_item{width: 100%;float: left;}
.reply_box .mac_msg_l .msg_tag{float:left;width: 60px;margin-right:2%;margin-bottom:5px;border-radius: 5px;overflow: hidden;}
.reply_box .mac_msg_l .count_bg{width:60px;height:5px;background: linear-gradient(90deg, #f25f4d 30%, #59b84b 20%, #30a1d9 65%, #fdcd34 10%);}
.reply_box .mac_msg_l .msg_count{
	padding:8px 0;color: #fff;text-align: center;font-size: 13px;
	background: -moz-linear-gradient(top, #444444 0%, #222222 100%);
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#444444), color-stop(100%,#222222));
	background: -webkit-linear-gradient(top, #444444 0%,#222222 100%);
	background: -o-linear-gradient(top, #444444 0%,#222222 100%);
	background: -ms-linear-gradient(top, #444444 0%,#222222 100%);
	background: linear-gradient(to bottom, #444444 0%,#222222 100%);
}
.reply_box .mac_msg_l .msg_count strong{display: block;}
.reply_box .mac_msg_l  .msg_list{float: left;width: 85%;margin-bottom: 10px;}
.reply_box .mac_msg_l .msg_list .msg_title{text-align:left;font-size:16px;border-bottom: 1px #d3e9fb dotted;line-height: 26px;}
.reply_box .mac_msg_l .msg_list .msg_title span.time{float: right;font-size: 12px;}
.reply_box .mac_msg_l .msg_list .msg_reply{color: #666;}
.reply_box .mac_msg_l .msg_list .reply_answer{color: #3c8f3f;}
.reply_box .mac_msg_l .msg_list .reply_answer .msg_title{font-size: 13px;}
.reply_box .mac_msg_l .msg_list .msg_cont{padding: 8px 0 0;}
.reply_box .mac_msg_l .msg_list .msg_reply,.reply_box .mac_msg_l .msg_list .reply_answer {
	position: relative;
	margin: 0 0 10px;
	padding: 6px 10px 8px;
	border: 1px solid #d3e9fb;
	border-radius: 0.2rem;
	background-color: #f5fbff;
}
.reply_box .mac_msg_l .msg_list .reply_answer:after {
	content: '';
	width: 0px;
	height: 0px;
	border-color:  transparent transparent #f5fbff transparent ;
	border-style: solid;
	border-width: 6px;
	position: absolute;
	top: -11px;
	border-radius: 3px;
	left: 18px;
	right: auto;
}
.reply_box .mac_msg_l .msg_list .reply_answer:before {
	content: '';
	width: 0px;
	height: 0px;
	border-color: transparent transparent #d3e9fb transparent;
	border-style: solid;
	border-width: 7px;
	position: absolute;
	top: -14px;
	border-radius: 3px;
	left: 17px;
	right: auto;
}
.reply_box .mac_msg_l .msg_list .msg_reply:after {
	content: '';
	width: 0px;
	height: 0px;
	border-color: transparent #f5fbff #f5fbff transparent;
	border-style: solid;
	border-width: 7px;
	position: absolute;
	top: 8px;
	border-radius: 3px;
	left: -12px;
	right: auto;
}
.reply_box .mac_msg_l .msg_list .msg_reply:before {
	content: '';
	width: 0px;
	height: 0px;
	border-color: transparent #d3e9fb #d3e9fb transparent;
	border-style: solid;
	border-width: 7px;
	position: absolute;
	top: 9px;
	border-radius: 3px;
	left: -14px;
	right: auto;
}
.reply_box .mac_msg_r{margin: 10px 1%;float: right;width: 25%;border: 1px solid #ddd;border-radius: 5px;color: #666;}
.reply_box .mac_msg_r .msg_tit{background: #f1f1f1;padding: 10px;border-bottom: 1px solid #ddd;}
.reply_box .mac_msg_r form{padding: 15px;}
.reply_box .mac_msg_r form .msg_cue{margin-bottom: 8px;}
.reply_box .mac_msg_r form textarea{width:100%;height: 106px;padding: 10px;overflow-y: auto;box-sizing: border-box;border-color: #ddd;}
.reply_box .mac_msg_r .msg_code{margin: 10px 0;}
.reply_box .mac_msg_r .msg_code input{border: 1px solid #ddd;padding: 4px;width: 65px;}
.reply_box .mac_msg_r .msg_code .mac_verify_img{height: 30px;margin-left: 2px;vertical-align: top;}
.reply_box .mac_msg_r .submit_btn{width: 100px;height: 32px;background: #4c8fe9;color: #fff;border: 1px solid #4c8fe9;margin:20px auto
	0;display: block;cursor: pointer;}
.mac_msg_r .submit_btn:hover{background: #619ff3;}
.mac_msg_r .submit_btn:active{background: #3883e6;}


/*顶踩图标*/
.mac_comment i.icon-ding,.mac_digg i.icon-ding{background: url("../images/home/<USER>") center no-repeat;}
.mac_comment i.icon-ding:hover,.mac_digg i.icon-ding:hover{background: url("../images/home/<USER>") center no-repeat;}
.mac_comment i.icon-dw,.mac_digg i.icon-dw{background: url("../images/home/<USER>") center no-repeat;}
.mac_comment i.icon-dw:hover,.mac_digg i.icon-dw:hover{background: url("../images/home/<USER>") center no-repeat;}

/*顶踩页面*/
.mac_digg a{margin-right: 10px;color: #999;}
.mac_digg a:hover{color: #333;}

/*登录页面*/
.mac_login{padding: 30px;width:400px;height:350px;background: #fff;box-sizing: border-box;font-family: "microsoft yahei"}
.mac_login h3{font-weight: normal;color: #333;margin: 0 0 30px;}
.mac_login .mac_login_form .login_form_group{margin-top: 20px;}
.mac_login .mac_login_form .login_form_group input{padding:4px 10px 4px 36px;width:100%;height:38px;line-height:38px;border: 1px solid #cacaca;box-sizing: border-box;}
.mac_login .mac_login_form .login_form_group input.mac_u_name{background: url("../images/home/<USER>") no-repeat 7px 8px;}
.mac_login .mac_login_form .login_form_group input.mac_u_pwd{background: url("../images/home/<USER>") 8px 8px no-repeat;}
.mac_login .mac_login_form .login_form_group input.mac_u_verify{float:left;width:50%;background: url("../images/home/<USER>") 8px 9px no-repeat;}
.mac_login .mac_login_form .login_form_group img.mac_verify_img{float:left;margin-left:1%;width: 88px;height: 38px;border: 1px solid #cacaca;box-sizing: border-box;cursor: pointer;}
.mac_login .mac_login_form .login_form_link{margin-top: 10px;text-align: right;}
.mac_login .mac_login_form .login_form_link a{font-size: 12px;color: #333;margin-left: 15px;}
.mac_login .mac_login_form .login_form_link a:hover{color: #2fa5ff}
.mac_login .mac_login_form input.login_form_submit{background: #2fa5ff;color: #fff;font-size: 14px;line-height: normal;border: none;cursor: pointer;}
.mac_login .mac_login_form input.login_form_submit:hover{background: #1d94ef;}

/*弹出层*/
.mac_pop_bg{position: fixed; z-index: 129; left: 0;top: 0; width: 100%; height: 100%; background: rgba(0,0,0,.2);}
.mac_pop {  z-index:99998; display: none; min-height: 20px; max-height: 750px; position: fixed;  top: 0;  left: 0;  bottom: 0;  right: 0;  margin: auto;  padding: 25px;  z-index: 130;  border-radius: 8px;  background-color: #fff;  box-shadow: 0 3px 18px rgba(0, 0, 0, .5);}
.mac_pop .pop_top{  height:40px;  width:100%;  border-bottom: 1px #E5E5E5 solid;}
.mac_pop .pop_top h2{  float: left; display:block;margin: 0;font-size: 18px;font-weight: normal;font-family:"microsoft yahei";}
.mac_pop span.pop_close{  float: right;width: 23px;height: 23px;font-size: 0;text-indent: 9999; cursor: pointer;  font-weight: bold; display:block;background: url("../images/home/<USER>") -10px 0 no-repeat;}
.mac_pop .pop-foot{  height:50px;  line-height:50px;  width:100%;  border-top: 1px #E5E5E5 solid;  text-align: right;  }
.mac_pop .pop-cancel, .pop-ok {  padding:8px 15px;  margin:15px 5px;  border: none;  border-radius: 5px;  background-color: #337AB7;  color: #fff;  cursor:pointer;  }
.mac_pop .pop-cancel {  background-color: #FFF;  border:1px #CECECE solid;  color: #000;  }
.mac_pop .pop-content{  height: 380px;  }
.mac_pop .pop-content-left{  float: left;  }
.mac_pop .pop-content-right{  width:310px;  float: left;  padding-top:20px;  padding-left:20px;  font-size: 16px;  line-height:35px;  }
.mac_pop .bgPop{  display: none;  position: absolute;  z-index: 129;  left: 0;  top: 0;  width: 100%;  height: 100%;  background: rgba(0,0,0,.2);  }
.mac_pop .pop-msg{  text-align:center;  font-size: 14px;  }

.mac_pop_msg_bg{position: fixed; z-index: 129; left: 0;top: 0; width: 100%; height: 100%; background: rgba(0,0,0,.2);}
.mac_pop_msg { z-index:99999; display: none; min-height: 20px; max-height: 750px; position: fixed;  top: 0;  left: 0;  bottom: 0;  right: 0;  margin: auto;  padding: 25px;  z-index: 130;  border-radius: 8px;  background-color: #fff;  box-shadow: 0 3px 18px rgba(0, 0, 0, .5);}
.mac_pop_msg .pop-msg{  text-align:center;  font-size: 14px;  }

/*跳转信息*/
.mac_msg_jump{width:90%;max-width:624px;min-height:60px;padding:20px 50px 50px;margin:20% auto 0;font-size:14px;line-height:24px;border: 1px solid #cdd5e0;border-radius: 10px;background:#fff;box-sizing: border-box;}
.mac_msg_jump .title{margin-bottom:11px;}
.mac_msg_jump .text{padding-left:29px;margin-bottom:11px;}
.mac_msg._jump .jump{ }
.msg_jump_tit{width:100%;height: 35px;margin: 25px 0 10px;text-align:center; font-size: 25px;color: #23c319;font-family: "黑体","microsoft yahei";letter-spacing: 5px;}

@media screen and (max-width: 900px) {
	.reply_box .mac_msg_l{float: none;width: 100%;}
	.reply_box .mac_msg_r{float: none;width: 96%;margin-left: 2%;}
	.reply_box .mac_msg_l  .msg_list{width: 80%;}
	.reply_box .mac_msg_r .submit_btn{width: 100%;border-radius: 3px;}
}
@media (min-width: 700px) and (max-width: 900px) {
	.reply_box .mac_msg_l  .msg_list{float:right;width: 88%;}
}
@media (min-width: 500px) and (max-width: 700px) {
	.reply_box .mac_msg_l  .msg_list{float:right;width: 85%;}
}
@media screen and (max-width: 500px) {
	.reply_box .mac_msg_l  .msg_list{float:right;}
}