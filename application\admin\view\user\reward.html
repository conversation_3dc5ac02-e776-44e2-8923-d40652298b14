{include file="../../../application/admin/view/public/head" /}
<div class="page-container p10">

    <div class="my-toolbar-box">

        <div class="center mb10">
            <form class="layui-form " method="post">
                <div class="layui-input-inline w150">
                    <select name="level">
                        <option value="">{:lang('admin/user/reward/select_level')}</option>
                        <option value="1" {if condition="$param['level'] eq '1'"}selected {/if}>{:lang('admin/user/reward/one_distribution')}</option>
                        <option value="2" {if condition="$param['level'] eq '2'"}selected {/if}>{:lang('admin/user/reward/two_distribution')}</option>
                        <option value="3" {if condition="$param['level'] eq '3'"}selected {/if}>{:lang('admin/user/reward/three_distribution')}</option>
                    </select>
                </div>
                <div class="layui-input-inline">
                    <input type="text" autocomplete="off" placeholder="{:lang('wd')}" class="layui-input" name="wd" value="{$param['wd']|mac_filter_xss}">
                </div>
                <input type="hidden" name="uid" value="{$param['uid']|mac_filter_xss}">
                <button class="layui-btn mgl-20 j-search" >{:lang('btn_search')}</button>
            </form>
        </div>

        <div class="layui-btn-group">
            <a class="layui-btn ">{:lang('admin/user/reward/one_people_num')}【{$data.level_cc_1}】{:lang('admin/user/reward/total_commission_points')}【{$data.points_cc_1}】</a>
            <a class="layui-btn layui-btn-normal">{:lang('admin/user/reward/two_people_num')}【{$data.level_cc_2}】{:lang('admin/user/reward/total_commission_points')}【{$data.points_cc_2}】</a>
            <a class="layui-btn layui-btn-warm">{:lang('admin/user/reward/three_people_num')}【{$data.level_cc_3}】{:lang('admin/user/reward/total_commission_points')}【{$data.points_cc_3}】</a>
        </div>

    </div>

    <form class="layui-form " method="post" id="pageListForm">
        <table class="layui-table" lay-size="sm">
            <thead>
            <tr>
                <th width="25"><input type="checkbox" lay-skin="primary" lay-filter="allChoose"></th>
                <th width="100">{:lang('id')}</th>
                <th>{:lang('name')}</th>
                <th width="120">{:lang('group')}</th>
                <th width="120">{:lang('status')}</th>
                <th width="120">{:lang('admin/user/reward/distribution_level')}</th>
                <th width="130">{:lang('reg_time')}</th>
            </tr>
            </thead>
            {volist name="list" id="vo"}
            <tr>
                <td><input type="checkbox" name="ids[]" value="{$vo.user_id}" class="layui-checkbox checkbox-ids" lay-skin="primary"></td>
                <td>{$vo.user_id}</td>
                <td>{$vo.user_name}</td>
                <td>{$vo.group_name}</td>
                <td>{if condition="$vo['user_status'] eq 1"}<span class="layui-badge layui-bg-green">{:lang('open')}</span>{else/}<span class="layui-badge">{:lang('close')}</span>{/if}</td>
                <td>{if condition="$vo['user_pid'] eq $param['uid']"}{:lang('admin/user/reward/one_distribution')}{elseif condition="$vo['user_pid_2'] eq $param['uid']"}{:lang('admin/user/reward/two_distribution')}{else/}{:lang('admin/user/reward/three_distribution')}{/if}</td>
                <td>{$vo.user_reg_time|mac_day='color'}</td>
            </tr>
            {/volist}
            </tbody>
        </table>
        <div id="pages" class="center"></div>
    </form>
</div>

{include file="../../../application/admin/view/public/foot" /}

<script type="text/javascript">
    var curUrl="{:url('user/reward',$param)}";
    layui.use(['laypage', 'layer'], function() {
        var laypage = layui.laypage
            , layer = layui.layer;

        laypage.render({
            elem: 'pages'
            ,count: {$total}
            ,limit: {$limit}
            ,curr: {$page}
            ,layout: ['count', 'prev', 'page', 'next', 'limit', 'skip']
            ,jump: function(obj,first){
                if(!first){
                    location.href = curUrl.replace('%7Bpage%7D',obj.curr).replace('%7Blimit%7D',obj.limit);
                }
            }
        });
    });
</script>
</body>
</html>