﻿v10版本增加了多语言的支持，方便更多全球用户使用，系统内所有显示和提示的信息完全由语言包控制。
系统默认内置了简体中文，繁体中文语言包，安装的时候选择切换您熟悉的语言即可在 安装、后台中完全显示对应的语言，所有模块提示信息也将显示对应语言。
后续还将内置或提供更多的语言包扩展，也欢迎大家友情提供。
建议不要修改公共语言包，如果想替换公共语言包里的内容，可以到对应的模块语言包下创建同样key的内容即可自动加载替换。

==语言包结构=
│─application/
│ ├─lang/ 公共语言包
│ ├──zh-cn.php 简体中文
│ ├──zh-tw.php 繁体中文
│─…
│ ├─admin/lang/ admin模块自定义语言包
│ ├──zh-cn.php
│ ├──zh-tw.php
│─…
│ ├─api/lang/ api模块自定义语言包
│ ├──zh-cn.php
│ ├──zh-tw.php
│─…
等等…