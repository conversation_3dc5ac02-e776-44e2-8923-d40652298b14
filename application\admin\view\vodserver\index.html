{include file="../../../application/admin/view/public/head" /}
<div class="page-container p10">
    <div class="my-toolbar-box">

        <div class="layui-btn-group">
            <a data-href="{:url('info')}" class="layui-btn layui-btn-primary j-iframe"><i class="layui-icon">&#xe654;</i>{:lang('add')}</a>
        </div>

    </div>

    <form class="layui-form " method="post" id="pageListForm">
        <table class="layui-table" lay-size="sm">
            <thead>
            <tr>
                <th width="80">{:lang('sort')}</th>
                <th width="80">{:lang('code')}</th>
                <th width="80">{:lang('status')}</th>
                <th width="150">{:lang('name')}</th>
                <th>{:lang('remarks')}</th>
                <th>{:lang('tip')}</th>
                <th width="100">{:lang('opt')}</th>
            </tr>
            </thead>

            {volist name="list" id="vo"}
            <tr>
                <td>{$vo.sort}</td>
                <td>{$vo.from}</td>
                <td>{if condition="$vo.status eq 1"}<span class="layui-badge layui-bg-green">{:lang('enable')}</span>{else}<span class="layui-badge">{:lang('disable')}</span>{/if} </td>
                <td>{$vo.show|mac_filter_xss}</td>
                <td>{$vo.des|mac_filter_xss}</td>
                <td>{$vo.tip|mac_filter_xss}</td>
                <td>
                    <a class="layui-badge-rim j-iframe" data-href="{:url('info?id='.$vo['from'])}" href="javascript:;" title="{:lang('edit')}">{:lang('edit')}</a>
                    <a class="layui-badge-rim j-tr-del" data-href="{:url('del?ids='.$vo['from'])}" href="javascript:;" title="{:lang('del')}">{:lang('del')}</a>
                </td>
            </tr>
            {/volist}
            </tbody>
        </table>

    </form>
</div>
{include file="../../../application/admin/view/public/foot" /}

<script type="text/javascript">

</script>
</body>
</html>