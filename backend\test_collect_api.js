#!/usr/bin/env node

/**
 * 测试苹果CMS采集接口
 * 用于验证接口是否完全兼容苹果CMS格式
 */

const express = require('express');
const request = require('supertest');

// 模拟数据
const mockVideos = [
  {
    id: 1,
    title: '测试电影1',
    categoryId: 1,
    categoryName: '动作片',
    createdAt: new Date(),
    status: 'active',
    coverUrl: 'https://example.com/cover1.jpg',
    videoUrl: 'https://example.com/video1.mp4',
    description: '这是一部测试电影',
    views: 1000,
    rating: 8.5,
    area: '大陆',
    language: '国语',
    duration: '120分钟',
    tags: '["动作", "冒险"]'
  },
  {
    id: 2,
    title: '测试电影2',
    categoryId: 2,
    categoryName: '喜剧片',
    createdAt: new Date(),
    status: 'active',
    coverUrl: 'https://example.com/cover2.jpg',
    videoUrl: 'https://example.com/video2.mp4',
    description: '这是另一部测试电影',
    views: 2000,
    rating: 7.8,
    area: '香港',
    language: '粤语',
    duration: '90分钟',
    tags: '["喜剧", "爱情"]'
  }
];

const mockCategories = [
  { id: 1, name: '动作片', parentId: 0, status: 'active' },
  { id: 2, name: '喜剧片', parentId: 0, status: 'active' },
  { id: 3, name: '爱情片', parentId: 0, status: 'active' }
];

// 模拟Video和Category模型
const Video = {
  findAll: async (options) => {
    const { page = 1, limit = 20 } = options;
    const start = (page - 1) * limit;
    const end = start + limit;
    const videos = mockVideos.slice(start, end);
    
    return {
      videos,
      pagination: {
        page,
        limit,
        total: mockVideos.length,
        totalPages: Math.ceil(mockVideos.length / limit)
      }
    };
  },
  
  findById: async (id) => {
    return mockVideos.find(v => v.id === parseInt(id));
  }
};

const Category = {
  findAll: async () => mockCategories
};

// 模拟logger
const logger = {
  api: (action, data) => console.log(`[API] ${action}:`, data),
  error: (msg, error) => console.error(`[ERROR] ${msg}:`, error)
};

// 创建测试应用
const app = express();

// 注入模拟依赖到全局
global.Video = Video;
global.Category = Category;
global.logger = logger;

// 创建一个简化的采集路由用于测试
const router = express.Router();

// 简化的视频采集接口
router.get('/vod', async (req, res) => {
  try {
    const {
      ac,
      t,
      pg = 1,
      wd,
      h,
      ids,
      at = 'json',
      pagesize = 20,
      year,
      isend,
      from,
      sort_direction = 'desc'
    } = req.query;

    logger.api('COLLECT_VOD_REQUEST', { ac, t, pg, wd, h, ids, at, pagesize });

    switch (ac) {
      case 'list':
        const listResult = await Video.findAll({ page: parseInt(pg), limit: parseInt(pagesize) });
        const categories = await Category.findAll();

        const listResponse = {
          code: 1,
          msg: '数据列表',
          page: listResult.pagination.page,
          pagecount: listResult.pagination.totalPages,
          limit: listResult.pagination.limit.toString(),
          total: listResult.pagination.total,
          list: listResult.videos.map(video => ({
            vod_id: parseInt(video.id) || 0,
            vod_name: video.title || '',
            type_id: parseInt(video.categoryId) || 1,
            type_name: video.categoryName || '其他',
            vod_en: video.title.toLowerCase().replace(/[^a-z0-9]/g, '').substring(0, 20),
            vod_time: Math.floor(video.createdAt.getTime() / 1000).toString(),
            vod_remarks: video.rating >= 9 ? '超清' : video.rating >= 7 ? '高清' : '标清',
            vod_play_from: 'default',
            vod_pic: video.coverUrl || '',
            vod_area: video.area || '大陆',
            vod_lang: video.language || '国语',
            vod_year: video.createdAt.getFullYear().toString(),
            vod_serial: '1',
            vod_status: video.status === 'active' ? 1 : 0,
            vod_hits: parseInt(video.views) || 0,
            vod_score: parseFloat(video.rating) || 0,
            vod_isend: 1
          })),
          class: categories.map(cat => ({
            type_id: parseInt(cat.id) || 0,
            type_pid: parseInt(cat.parentId) || 0,
            type_name: cat.name || '其他'
          }))
        };

        if (at === 'xml') {
          res.set('Content-Type', 'application/xml; charset=utf-8');
          let xml = '<?xml version="1.0" encoding="utf-8"?>\n';
          xml += '<rss version="2.0">\n';
          xml += `<list page="${listResponse.page}" pagecount="${listResponse.pagecount}" pagesize="${listResponse.limit}" recordcount="${listResponse.total}">\n`;
          listResponse.list.forEach(item => {
            xml += '<video>\n';
            xml += `<id>${item.vod_id}</id>\n`;
            xml += `<n><![CDATA[${item.vod_name}]]></n>\n`;
            xml += `<type>${item.type_name}</type>\n`;
            xml += '</video>\n';
          });
          xml += '</list>\n';
          xml += '</rss>';
          res.send(xml);
        } else {
          res.json(listResponse);
        }
        break;

      case 'detail':
      case 'videolist':
        const videos = [];
        if (ids) {
          const idArray = ids.split(',').map(id => parseInt(id.trim()));
          for (const id of idArray) {
            const video = await Video.findById(id);
            if (video) videos.push(video);
          }
        }

        const detailResponse = {
          code: 1,
          msg: '数据列表',
          page: 1,
          pagecount: 1,
          limit: videos.length.toString(),
          total: videos.length,
          list: videos.map(video => ({
            vod_id: parseInt(video.id) || 0,
            vod_name: video.title || '',
            type_id: parseInt(video.categoryId) || 1,
            type_name: video.categoryName || '其他',
            vod_en: video.title.toLowerCase().replace(/[^a-z0-9]/g, '').substring(0, 20),
            vod_time: Math.floor(video.createdAt.getTime() / 1000).toString(),
            vod_remarks: video.rating >= 9 ? '超清' : video.rating >= 7 ? '高清' : '标清',
            vod_play_from: 'default',
            vod_play_url: video.videoUrl ? `正片$${video.videoUrl}` : '',
            vod_pic: video.coverUrl || '',
            vod_area: video.area || '大陆',
            vod_lang: video.language || '国语',
            vod_year: video.createdAt.getFullYear().toString(),
            vod_serial: '1',
            vod_actor: '',
            vod_director: '',
            vod_content: video.description || '',
            vod_status: video.status === 'active' ? 1 : 0,
            vod_hits: parseInt(video.views) || 0,
            vod_score: parseFloat(video.rating) || 0,
            vod_duration: video.duration || '',
            vod_tag: '',
            vod_class: video.categoryName || '其他',
            vod_isend: 1
          }))
        };

        res.json(detailResponse);
        break;

      default:
        res.json({
          code: 0,
          msg: '参数错误：ac参数必须为list、detail或videolist',
          page: 1,
          pagecount: 0,
          limit: '0',
          total: 0,
          list: [],
          class: []
        });
    }
  } catch (error) {
    logger.error('采集接口错误:', error);
    res.json({
      code: 0,
      msg: '服务器内部错误',
      page: 1,
      pagecount: 0,
      limit: '0',
      total: 0,
      list: [],
      class: []
    });
  }
});

app.use('/api.php/provide', router);

// 测试函数
async function testCollectAPI() {
  console.log('🚀 开始测试苹果CMS采集接口...\n');

  try {
    // 测试1: 视频列表接口 (JSON格式)
    console.log('📋 测试1: 视频列表接口 (JSON格式)');
    const listResponse = await request(app)
      .get('/api.php/provide/vod')
      .query({ ac: 'list', pg: 1, pagesize: 10 });
    
    console.log('状态码:', listResponse.status);
    console.log('响应格式:', typeof listResponse.body);
    console.log('响应结构:', Object.keys(listResponse.body));
    
    if (listResponse.body.code === 1) {
      console.log('✅ 列表接口测试通过');
      console.log(`   - 总数: ${listResponse.body.total}`);
      console.log(`   - 页数: ${listResponse.body.page}/${listResponse.body.pagecount}`);
      console.log(`   - 视频数量: ${listResponse.body.list.length}`);
      console.log(`   - 分类数量: ${listResponse.body.class.length}`);
    } else {
      console.log('❌ 列表接口测试失败');
    }

    // 测试2: 视频详情接口 (JSON格式)
    console.log('\n📄 测试2: 视频详情接口 (JSON格式)');
    const detailResponse = await request(app)
      .get('/api.php/provide/vod')
      .query({ ac: 'detail', ids: '1,2' });
    
    console.log('状态码:', detailResponse.status);
    if (detailResponse.body.code === 1) {
      console.log('✅ 详情接口测试通过');
      console.log(`   - 视频数量: ${detailResponse.body.list.length}`);
      if (detailResponse.body.list.length > 0) {
        const video = detailResponse.body.list[0];
        console.log(`   - 视频字段: ${Object.keys(video).join(', ')}`);
      }
    } else {
      console.log('❌ 详情接口测试失败');
    }

    // 测试3: XML格式输出
    console.log('\n📄 测试3: XML格式输出');
    const xmlResponse = await request(app)
      .get('/api.php/provide/vod')
      .query({ ac: 'list', at: 'xml', pg: 1, pagesize: 5 });
    
    console.log('状态码:', xmlResponse.status);
    console.log('Content-Type:', xmlResponse.headers['content-type']);
    
    if (xmlResponse.text && xmlResponse.text.includes('<rss')) {
      console.log('✅ XML格式测试通过');
      console.log('   - XML长度:', xmlResponse.text.length);
    } else {
      console.log('❌ XML格式测试失败');
    }

    console.log('\n🎉 所有测试完成！');

  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error.message);
  }
}

// 运行测试
if (require.main === module) {
  testCollectAPI();
}

module.exports = { testCollectAPI, app };
