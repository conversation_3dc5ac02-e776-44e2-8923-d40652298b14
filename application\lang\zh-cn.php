<?php
/*
*当前语言包是系统全局语言包；自定义模块语言包请在模块的lang目录下创建比如 application/admin/lang/显示的时候会优先于全局包
*全局key：单词可能在系统内任何地方使用
*模块key：开头  admin/ 、 install/ 、只在模块内使用
*后台菜单key：开头menu/
*内部处理key：开头 model/、controller/、只在模块内使用
*/
return [
    'lang_ver'=>'3021+',
    'hello'  => '欢迎使用',
    'maccms_name'=>'苹果CMS-v10',
    'maccms_copyright'=>'© MacCMS All Rights Reserved.',

    'vod'=>'视频',
    'art'=>'文章',
    'topic'=>'专题',
    'comment'=>'评论',
    'gbook'=>'留言',
    'user'=>'用户',
    'label'=>'自定义页面',
    'actor'=>'演员',
    'role'=>'角色',
    'plot'=>'分集剧情',
    'website'=>'网址',
    'domain'=>'域名',
    'or'=>'或',
    'all'=>'全部',
    'open'=>'开启',
    'close'=>'关闭',
    'task'=>'任务',
    'status'=>'状态',
    'status_parse'=>'解析状态',
    'test'=>'测试',
    'copy'=>'复制',
    'run'=>'执行',
    'run_ok'=>'执行成功',
    'skip'=>'跳过',
    'jump'=>'跳转',
    'jump_over'=>'跳过',
    'quantity'=>'数量',
    'start'=>'起始',
    'end'=>'截止',
    'save'=>'保存',
    'level'=>'推荐',
    'lock'=>'锁定',
    'unlock'=>'解锁',
    'disable'=>'禁用',
    'enable'=>'启用',
    'pause'=>'暂停',
    'normal'=>'正常',
    'abnormal'=>'异常',
    'back_link'=>'反向链接',
    'page_limit'=>'每页条数',
    'copyright'=>'版权',
    'browse'=>'浏览',
    'favorites'=>'收藏',
    'want_see'=>'想看',
    'play'=>'播放',
    'down'=>'下载',
    'website'=>'网站',
    'site_name'=>'网站名称',
    'keyword'=>'关键字',
    'description'=>'描述',
    'data_name'=>'数据名称',
    'return'=>'返回',
    'integral_recharge'=>'积分充值',
    'registration_promotion'=>'注册推广',
    'visit_promotion'=>'访问推广',
    'one_level_distribution'=>'一级分销',
    'two_level_distribution'=>'二级分销',
    'three_level_distribution'=>'三级分销',
    'points_upgrade'=>'积分升级',
    'integral_consumption'=>'积分消费',
    'integral_withdrawal'=>'积分提现',
    'not_sale'=>'未出售',
    'sold'=>'已出售',
    'not_used'=>'未使用',
    'used'=>'已使用',
    'not_paid'=>'未支付',
    'paid'=>'已支付',
    'slice'=>'片',
    'drama'=>'剧',
    'the'=>'第',
    'episode'=>'集',
    'issue'=>'期',
    'just'=>'刚刚',
    'day_after_tomorrow'=>'后天',
    'tomorrow'=>'明天',
    'year'=>'年',
    'years'=>'年份',
    'month'=>'月',
    'day'=>'日',
    'yes'=>'是',
    'not'=>'否',
    'seconds'=>'秒前',
    'yesterday'=>'昨天',
    'day_before_yesterday'=>'前天',
    'seconds_ago'=>'秒前',
    'minutes_ago'=>'分钟前',
    'hours_ago'=>'小时前',
    'continue_in_second'=>'秒后继续',
    'audit'=>'审核',
    'reviewed'=>'已审核',
    'reviewed_not'=>'未审核',
    'last_run_time'=>'上次执行时间',
    'wait_submit'=>'数据提交中...',
    'wait_time'=>'等待时间',
    'audit_time'=>'审核时间',
    'bank'=>'银行',
    'request_err'=>'请求失败',
    'group'=>'会员组',
    'access'=>'账号',
    'test_ok'=>'测试成功',
    'test_err'=>'测试失败',
    'browser_jump'=>'如果您的浏览器没有自动跳转，请点击这里',
    'filed_empty'=>'请输入内容',
    'page_auto'=>'页面自动',
    'param_err'=>'参数错误',
    'name_empty'=>'请输入名称',
    'pass_empty'=>'请输入密码',
    'pass_err'=>'密码错误',
    'verify_empty'=>'请输入验证码',
    'verify_err'=>'验证码错误',
    'url_empty'=>'请输入URL',
    'cancel_level'=>'取消推荐',
    'select'=>'选择',
    'select_return'=>'选择返回',
    'select_data'=>'选择数据',
    'select_opt'=>'选择操作',
    'select_level'=>'选择推荐',
    'select_type'=>'选择分类',
    'select_status'=>'选择状态',
    'select_pic'=>'选择图片',
    'select_sort'=>'选择排序',
    'select_lock'=>'选择锁定',
    'select_sale_status'=>'选择出售状态',
    'select_use_status'=>'选择使用状态',
    'select_reply_status'=>'选择回复状态',
    'select_order_status'=>'选择订单状态',
    'select_time'=>'选择时间',
    'select_please'=>'请选择',
    'select_model'=>'选择模块',
    'select_report'=>'选择举报',
    'select_template'=>'选择模板',
    'select_genre'=>'选择类型',
    'select_group'=>'选择用户组',
    'select_area'=>'选择地区',
    'default_val'=>'默认值',
    'related_data'=>'相关数据',
    'detect'=>'检测',
    'genre'=>'类型',
    'portrait'=>'头像',
    'tpl_dir'=>'模板目录',
    'ads_dir'=>'广告目录',
    'reply'=>'回复',
    'reply_yes'=>'已回复',
    'reply_not'=>'未回复',
    'report_yes'=>'有举报',
    'report_not'=>'未举报',
    'current'=>'当前',
    'blank'=>'新窗口',
    'report'=>'举报',

    'use'=>'使用',
    'the_last_time'=>'最后一次',
    'that_day'=>'当天',
    'in_a_week'=>'一周内',
    'in_a_month'=>'一月内',

    'calcel_level'=>'取消推荐',
    'pic_empty'=>'无图片',
    'pic_remote'=>'远程图片',
    'pic_sync_err'=>'同步出错图',
    'pic_local'=>'本地图片',
    'pic_sync'=>'同步图片',
    'not_pic_sync_err'=>'非出错图',
    'not_pic_sync_today_err'=>'非当天出错图',
    'pic_err'=>'出错图',

    'sort'=>'排序',
    'add'=>'添加',
    'edit'=>'编辑',
    'del'=>'删除',
    'del_confirm'=>'确认要删除吗？',
    'del_multi'=>'批量删除',
    'del_data'=>'删除数据',
    'del_ok'=>'删除成功',
    'del_err'=>'删除失败',
    'del_empty'=>'删除为空',
    'add_group'=>'添加一组',

    'id'=>'编号',
    'sub'=>'副标',
    'hits'=>'人气',
    'hits_all'=>'总人气',
    'hits_month'=>'月人气',
    'hits_week'=>'周人气',
    'hits_day'=>'日人气',
    'no'=>'编号',
    'area'=>'地区',
    'lang'=>'语言',
    'sex'=>'性别',
    'sum'=>'共',
    'opt'=>'操作',
    'opt_content'=>'内容操作',
    'name'=>'名称',
    'height'=>'身高',
    'weight'=>'体重',
    'type'=>'分类',
    'wd'=>'关键字',
    'slide'=>'幻灯片',
    'param'=>'参数',
    'base_info'=>'基本信息',
    'other_info'=>'其他信息',
    'male'=>'男',
    'female'=>'女',
    'path'=>'路径',
    'actor_name'=>'演员名',
    'alias'=>'别名',
    'en'=>'拼音',
    'letter'=>'首字母',
    'color'=>'颜色',
    'blood'=>'血型',
    'birtharea'=>'出生地',
    'birthday'=>'生日',
    'starsign'=>'星座',
    'school'=>'毕业学校',
    'view'=>'查看',
    'multi_set'=>'批量设置',
    'multi_separate_tip'=>'多个用,号分隔',
    'multi_del_ok'=>'批量删除完毕',
    'multi_set_ok'=>'批量设置完毕',
    'multi_opt_ok'=>'批量操作完毕',
    'tip'=>'提示',
    'target'=>'目标窗口',
    'start_exec'=>'开始执行',
    'min_val'=>'最小值',
    'max_val'=>'最大值',

    'vod_name'=>'视频名称',
    'role_name'=>'角色名称',
    'set_ok'=>'设置成功',
    'set_err'=>'设置失败',

    'remarks'=>'备注',
    'works'=>'代表作',
    'serial_num'=>'序号',
    'auto_make'=>'自动生成',
    'make_page'=>'生成页面',
    'make_all'=>'生成全部',

    'class'=>'扩展分类',
    'pic'=>'图片',
    'pic_thumb'=>'缩略图',
    'pic_slide'=>'海报图',
    'pic_screenshot'=>'截图',
    'upload'=>'上传',
    'upload_pic'=>'上传图片',
    'blurb'=>'简介',
    'content'=>'详情',
    'blurb_auto_tip'=>'不填写将自动从第一页详情里获取前100个字',
    'up'=>'顶',
    'hate'=>'踩',
    'rnd_make'=>'随机生成',
    'reset_zero'=>'归零',
    'score'=>'平均分',
    'score_all'=>'总评分',
    'score_num'=>'总评次',
    'tpl'=>'独立模板',
    'jumpurl'=>'跳转URL',
    'upload_ing'=>'文件上传中...',
    'install_ok'=>'安装成功',
    'install_err'=>'安装失败',
    'uninstall_ok'=>'卸载成功',
    'uninstall_err'=>'卸载失败',
    'url'=>'网址',

    'rel_vod'=>'关联视频',
    'rel_art'=>'关联文章',

    'opt_ok'=>'操作成功',
    'opt_err'=>'操作失败',
    'update_ok'=>'更新成功',
    'update_err'=>'更新失败',
    'follow_global'=>'跟随全局',

    'btn_save' =>'保 存',
    'btn_reset' =>'还 原',
    'btn_search' =>'查询',
    'search_data'=>'查询数据',

    'save_ok'=>'保存成功!',
    'save_err'=>'保存失败!',

    'write_err'=>'文件写入失，请重试!',
    'write_err_config'=>'配置文件写入失败，请重试!',
    'write_err_database'=>'数据库配置写入失败，请重试!',
    'write_err_route'=>'路由配置写入失败，请重试!',
    'wirte_err_codefile'=>'保存代码文件失败，请重试!',
    'import_err'=>'导入失败，请检查文件格式',
    'import_ok'=>'导入完成',
    'import'=>'导入',
    'import_all'=>'导入全部',
    'export'=>'导出',
    'code'=>'编码',
    'sms_not_config'=>'未配置短信发送服务',
    'email_not_config'=>'未配置邮箱发送服务',
    'phone_format_err'=>'手机号格式不正确',
    'email_format_err'=>'邮箱格式不正确',
    'format_err'=>'格式错误',
    'title_not_empty'=>'标题不能为空',
    'body_not_empty'=>'标题不能为空',
    'tpl_not'=>'模板编号不能为空',
    'sms_not'=>'未找到该短信发送方式',
    'email_not'=>'未找到该邮件发送方式',

    'counting_points'=>'计点',
    'counting_times'=>'计时',
    'counting_ips'=>'ip段',

    'mobile'=>'手机',
    'email'=>'邮箱',
    'verify'=>'验证码',
    'account'=>'账号',
    'local_app'=>'本地应用',
    'online_app'=>'应用商店',
    'local_setup'=>'离线安装',
    'bind_no'=>'绑定账号',
    'bind'=>'绑定',
    'unbind'=>'解绑',

    'install'=>'安装',
    'uninstall'=>'卸载',
    'detail'=>'详情',
    'config'=>'配置',
    'author'=>'作者',
    'intro'=>'介绍',
    'ver'=>'版本',
    'time'=>'时间',

    'update_time'=>'更新时间',
    'add_time'=>'添加时间',
    'use_time'=>'使用时间',
    'cj_time'=>'采集时间',
    'reply_time'=>'回复时间',
    'log_time'=>'日志时间',
    'reg_time'=>'注册时间',
    'related'=>'关联',

    'card_no'=>'卡号',
    'money'=>'金额',
    'rule'=>'规则',
    'mixing'=>'混合',
    'number'=>'数字',
    'abc'=>'字母',

    'seo_key'=>'SEO关键字',
    'seo_des'=>'SEO描述',
    'seo_title'=>'SEO标题',
    'transfer'=>'转移',
    'parent_type_id'=>'父分类编号',
    'type_id'=>'分类编号',
    'type_name'=>'分类名称',
    'last_login_time'=>'上次登录时间',
    'last_login_ip'=>'上次登录IP',
    'login_num'=>'登录次数',
    'popedom'=>'权限',
    'check_all'=>'全选',
    'check_other'=>'反选',
    'pass'=>'密码',
    'clear_confirm'=>'确认清空数据吗？',
    'audit_confirm'=>'确认审核数据吗？',
    'blacklist_keywords' => '黑名单关键字',
    'blacklist_ip' => '黑名单IP',

    'clear'=>'清空',
    'del_auto_keep_min'=>'一键删重[保留小ID]',
    'del_auto_keep_max'=>'一键删重[保留大ID]',
    'update_repeat_cache'=>'更新缓存',
    'num_id'=>'数字ID',
    'encode_id'=>'加密ID',
    'vod_id'=>'视频ID',
    'art_id'=>'文章ID',
    'type_id'=>'分类ID',
    'topic_id'=>'专题ID',
    'actor_id'=>'演员ID',
    'role_id'=>'角色ID',
    'website_id'=>'网址ID',
    'extend_class'=>'扩展分类',
    'extend_area'=>'扩展地区',
    'extend_lang'=>'扩展语言',
    'extend_year'=>'扩展年代',
    'page_title'=>'页标题',
    'page_note'=>'页备注',
    'page_content'=>'分页内容',
    'page_add'=>'添加一页内容',
    'from'=>'来源',
    'paging'=>'分页',
    'referer'=>'来路',
    'access_pwd'=>'访问密码',
    'pwd_url'=>'密码链接',
    'pwd_play'=>'播放页密码',
    'pwd_detail'=>'内容页密码',
    'play_group'=>'播放组',
    'down_group'=>'下载组',

    'pwd_down'=>'下载页密码',
    'not_static_is_ok'=>'非静态模式下可用',
    'points'=>'积分',
    'points_all'=>'整数据积分',
    'points_play'=>'播放积分',
    'points_down'=>'下载积分',
    'points_detail'=>'每页积分',
    'model'=>'模块',
    'total'=>'总共',
    'nickname'=>'昵称',
    'data_needs_processed'=>'条数据需要处理',
    'per_page'=>'每页',
    'data'=>'条',
    'page'=>'页',
    'processing'=>'正在处理第',
    'permission_denied'=>'您没有权限访问此页面',
    'illegal_request'=>'非法请求',
    'token_err'=>'请不要重复提交表单',
    'dir'=>'文件夹',
    'file'=>'文件',
    'file_name'=>'文件名称',
    'file_size'=>'文件大小',
    'file_time'=>'文件时间',
    'file_des'=>'文件描述',
    'occupies'=>'占用',
    'space'=>'空间',
    'return_parent_dir'=>'返回上级目录',
    'phone'=>'电话',
    'server_rest'=>'让服务器休息一会，稍后继续',
    'to'=>'到',
    'director'=>'导演',
    'clear_ok'=>'清理成功',
    'clear_err'=>'清理失败，请重试',
    'unknown'=>'未知',
    'unknown_type'=>'未知分类',
    'obtain_ok'=>'获取成功',
    'obtain_err'=>'获取失败',
    'download_ok'=>'下载成功',
    'download_err'=>'下载失败',
    'expand_all'=>'全部展开',
    'fold_all'=>'全部合上',
    'data'=>'数据',
    'today_data'=>'今日数据',
    'no_make_data'=>'未生成数据',
    'get_info_err'=>'获取信息失败',
    'rnd_data'=>'随机数据',
    'success'=>'成功',
    'diy_ids'=>'自定义ids',
    'fail'=>'失败',
    'duplicate_data'=>'重复数据',
    'distinct_into'=>'去重入库',
    'comment_name'=>'评论昵称',
    'comment_content'=>'评论内容',

    'page_not_found'=>'页面不存在',
    'search_close'=>'搜索功能关闭中',
    'show_close'=>'筛选页功能关闭中',
    'ajax_close'=>'ajax页功能关闭中',
    'frequently'=>'请不要频繁操作',
    'search_frequently'=>'请不要频繁操作，搜索时间间隔为',
    'score_ok'=>'感谢您的参与，评分成功',
    'suggest_close'=>'联想搜索功能关闭中',

    'please_try_again'=>'请重试',
    'data_list'=>'数据列表',
    'data_not_found'=>'数据获取失败',
    'unverified'=>'未验证',
    'verified'=>'已验证',
    'registered'=>'已注册',
    'register'=>'注册',
    'findpass'=>'找回密码',
    'access_or_pass_err'=>'账号或密码错误',
    'playurl'=>'播放地址',
    'downurl'=>'下载地址',
    'serial'=>'连载数',
    'writer'=>'编剧',
    'version'=>'资源版本',
    'state'=>'资源类别',
    'tv'=>'电视频道',
    'weekday'=>'节目周期',
    'isend'=>'完结',
    'total'=>'总集数',
    'replace'=>'替换',
    'merge'=>'合并',
    'douban_id'=>'豆瓣ID',
    'rel_name'=>'关联数据名称',
    'preview'=>'预览',
    'screenshot_preview'=>'截图预览',
    'screenshot_tip'=>'每行一个图片地址支持远程url、本地路径、自定义名称备注，例如：
图1$upload/test.jpg
图2$https://www.baidu.com/logo.png
https://www.baidu.com/123.jpg
    ',

    'menu/index'=>'首页',
    'menu/welcome'=>'欢迎页面',
    'menu/quickmenu'=>'自定义菜单配置',
    'menu/system'=>'系统',
    'menu/config'=>'网站参数配置',
    'menu/configseo'=>'SEO参数配置',
    'menu/configuser'=>'会员参数配置',
    'menu/configcomment'=>'评论留言配置',
    'menu/configupload'=>'附件参数配置',
    'menu/configurl'=>'URL地址配置',
    'menu/configplay'=>'播放器参数配置',
    'menu/configcollect'=>'采集参数配置',
    'menu/configinterface'=>'站外入库配置',
    'menu/configapi'=>'开放API配置',
    'menu/configconnect'=>'整合登录配置',
    'menu/configpay'=>'在线支付配置',
    'menu/configweixin'=>'微信对接配置',
    'menu/configemail'=>'邮件发送配置',
    'menu/configsms'=>'短信发送配置',
    'menu/timming'=>'定时任务配置',
    'menu/domain'=>'站群管理配置',
    'menu/base'=>'基础',
    'menu/type'=>'分类管理',
    'menu/topic'=>'专题管理',
    'menu/link'=>'友链管理',
    'menu/gbook'=>'留言管理',
    'menu/comment'=>'评论管理',
    'menu/images'=>'附件管理',
    'menu/art'=>'文章',
    'menu/art_data'=>'文章数据',
    'menu/art_add'=>'添加文章',
    'menu/art_data_lock'=>'已锁定文章',
    'menu/art_data_audit'=>'未审核文章',
    'menu/art_batch'=>'批量操作文章',
    'menu/art_repeat'=>'重名文章数据',
    'menu/vod'=>'视频',
    'menu/server'=>'服务器组',
    'menu/player'=>'播放器',
    'menu/downer'=>'下载器',
    'menu/vod_data'=>'视频数据',
    'menu/vod_add'=>'添加视频',
    'menu/vod_data_url_empty'=>'无地址视频',
    'menu/vod_data_lock'=>'已锁定视频',
    'menu/vod_data_audit'=>'未审核视频',
    'menu/vod_data_points'=>'需积分视频',
    'menu/vod_data_plot'=>'有分集剧情',
    'menu/vod_batch'=>'批量操作视频',
    'menu/vod_repeat'=>'重名视频数据',
    'menu/actor'=>'演员库',
    'menu/role'=>'角色库',
    'menu/website'=>'网址',
    'menu/website_data'=>'网址数据',
    'menu/website_add'=>'添加网址',
    'menu/website_data_lock'=>'已锁定网址',
    'menu/website_data_audit'=>'未审核网址',
    'menu/website_batch'=>'批量操作网址',
    'menu/website_repeat'=>'重名网址数据',
    'menu/users'=>'用户',
    'menu/admin'=>'管理员',
    'menu/group'=>'会员组',
    'menu/user'=>'会员',
    'menu/card'=>'充值卡',
    'menu/order'=>'会员订单',
    'menu/ulog'=>'访问日志',
    'menu/plog'=>'积分日志',
    'menu/cash'=>'提现记录',
    'menu/templates'=>'模版',
    'menu/template'=>'模板管理',
    'menu/ads'=>'广告位管理',
    'menu/wizard'=>'标签向导',
    'menu/make'=>'生成',
    'menu/make_opt'=>'生成选项',
    'menu/make_index'=>'生成首页',
    'menu/make_index_wap'=>'生成WAP首页',
    'menu/make_map'=>'生成地图',
    'menu/cjs'=>'采集',
    'menu/union'=>'推荐资源',
    'menu/collect_timming'=>'定时挂机',
    'menu/collect'=>'自定义接口',
    'menu/cj'=>'自定义规则',
    'menu/db'=>'数据库',
    'menu/database'=>'数据库管理',
    'menu/database_sql'=>'执行SQL语句',
    'menu/database_rep'=>'数据批量替换',
    'menu/database_inspect'=>'挂马检测',
    'menu/apps'=>'应用',
    'menu/addon'=>'应用市场',
    'menu/urlsend'=>'URL推送',
    'menu/safety_file'=>'文件安全检测',
    'menu/safety_data'=>'数据挂马检测',

    'model/admin/update_login_err'=>'更新登录信息失败',
    'model/admin/login_ok'=>'登录成功',
    'model/admin/logout_ok'=>'退出成功',
    'model/admin/not_login'=>'未登录',
    'model/admin/haved_login'=>'已登录',

    'model/card/not_found'=>'充值卡信息有误，请重试',
    'model/card/update_user_points_err'=>'更新用户点数失败，请重试',
    'model/card/update_card_status_err'=>'更新充值卡状态失败，请重试',
    'model/card/used_card_ok'=>'充值成功，增加积分【%s】',

    'model/cash/not_open'=>'提现功能未开启！',
    'model/cash/min_money_err'=>'最低提现金额！',
    'model/cash/mush_money_err'=>'提现太多了,没有这么多积分哦！',

    'model/collect/flag_err'=>'flag标识错误，请勿非法请求！',
    'model/collect/cjurl_err'=>'采集链接有误或不能为本地链接',
    'model/collect/get_html_err'=>'连接API接口失败，通常为服务器网络不稳定、IP被封、禁用相关函数！',
    'model/collect/json_err'=>'JSON格式不正确，不支持采集',
    'model/collect/xml_err'=>'XML格式不正确，不支持采集',
    'model/collect/data_tip1'=>'当前采集任务<strong class="green">%s</strong>/<span class="green">%s</span>页 采集地址&nbsp;%s',
    'model/collect/type_err'=>'分类未绑定，跳过err',
    'model/collect/name_in_filter_err'=>'数据在过滤单中，跳过err',
    'model/collect/name_err'=>'数据不完整，跳过err',
    'model/collect/not_check_add'=>'数据操作没有勾选新增，跳过。',
    'model/collect/not_check_update'=>'数据操作没有勾选更新，跳过。',
    'model/collect/add_ok'=>'新加入库，成功ok。',
    'model/collect/uprule_empty'=>'没有设置任何二次更新项目，跳过。',
    'model/collect/data_lock'=>'数据已经锁定，跳过。',
    'model/collect/not_need_update'=>'无需更新。',
    'model/collect/is_over'=>'数据采集完成。',
    'model/collect/not_found_rel_vod'=>'未找到相关视频无法关联，跳过。',
    'model/collect/not_found_rel_data'=>'未找到相关数据无法关联，跳过。',
    'model/collect/role_data_require'=>'数据不完整role_name,role_actor,vod_name必须，跳过err。',
    'model/collect/actor_data_require'=>'数据不完整actor_name,actor_sex必须，跳过err。',
    'model/collect/comment_data_require'=>'数据不完整comment_content,comment_name,rel_name必须，跳过err。',
    'model/collect/playurl_same'=>'播放地址相同，跳过。',
    'model/collect/playfrom_empty'=>'播放器类型为空，跳过。',
    'model/collect/downurl_same'=>'下载地址相同，跳过。',
    'model/collect/downfrom_empty'=>'下载器类型为空，跳过。',

    'model/collect/playgroup_add_ok'=>'播放组(%s)，新增ok。',
    'model/collect/playgroup_same'=>'播放组(%s)，无需更新。',
    'model/collect/playgroup_update_ok'=>'播放组(%s)，更新ok。',

    'model/collect/downgroup_add_ok'=>'下载组(%s)，新增ok。',
    'model/collect/downgroup_same'=>'下载组(%s)，无需更新。',
    'model/collect/downgroup_update_ok'=>'下载组(%s)，更新ok。',

    'model/group/have_user'=>'用户组下还有用户',
    'model/order/pay_over'=>'订单已支付完毕',
    'model/order/update_status_err'=>'更新订单状态失败',
    'model/order/update_user_points_err'=>'更新会员积分失败',
    'model/order/pay_ok'=>'充值完毕,回调函数执行成功',

    'model/type/to_info_err'=>'获取目标分类信息失败',
    'model/type/move_err'=>'转移失败',
    'model/type/move_ok'=>'转移失败',

    'model/user/not_open_reg'=>'未开放注册',
    'model/user/input_require'=>'请填写必填项',
    'model/user/pass_not_pass2'=>'密码与确认密码不一致',
    'model/user/haved_reg'=>'用户名已被注册，请更换',
    'model/user/name_contain'=>'用户名只能包含字母和数字，请更换',
    'model/user/name_filter'=>'用户名禁止包含：%s等字符，请重试',
    'model/user/ip_limit'=> '每IP每日限制注册%s次',
    'model/user/phone_haved'=> '手机号已被使用，请更换',
    'model/user/email_haved'=> '邮箱已被使用，请更换',
    'model/user/reg_err'=> '注册失败，请重试',
    'model/user/reg_ok'=>'注册成功,请登录去会员中心完善个人信息',
    'model/user/input_old_pass'=> '请输入原密码',
    'model/user/old_pass_err'=> '原密码错误',
    'model/user/pass_not_same_pass2'=> '两次输入的新密码不一致',
    'model/user/not_found'=>'获取用户信息失败',
    'model/user/update_login_err'=>'更新登录信息失败',
    'model/user/update_expire_err'=>'更新会员组过期信息失败',
    'model/user/update_expire_ok'=>'更新过期信息成功',
    'model/user/login_ok'=>'登录成功',
    'model/user/logout_ok'=>'退出成功',
    'model/user/not_login'=>'未登录',
    'model/user/haved_login'=>'已登录',
    'model/user/findpass_not_found'=>'获取用户失败，账号、问题、答案可能不正确',
    'model/user/findpass_ok'=>'密码找回成功成功',
    'model/user/select_diy_group_err'=>'请选择自定义收费会员组',
    'model/user/group_not_found'=>'获取会员组信息失败',
    'model/user/group_is_close'=>'会员组已经关闭，无法升级',
    'model/user/potins_not_enough'=>'积分不够，无法升级',
    'model/user/update_group_err'=>'升级会员组失败',
    'model/user/update_group_ok'=>'升级会员组成功',
    'model/user/msg_not_found'=>'验证信息错误，请重试',
    'model/user/do_not_send_frequently'=>'请不要频繁发送',
    'model/user/msg_send_ok'=>'验证码发送成功，请前往查看',
    'model/user/msg_send_err'=>'验证码发送失败',
    'model/user/update_bind_err'=>'更新用户绑定信息失败',
    'model/user/update_bind_ok'=>'绑定成功',
    'model/user/update_unbind_ok'=>'解除绑定成功',
    'model/user/pass_length_err'=>'密码最少6个字符',
    'model/user/email_format_err'=>'邮箱地址格式不正确',
    'model/user/email_err'=>'邮箱地址不正确',
    'model/user/email_host_not_allowed'=>'邮箱域名不允许',
    'model/user/phone_format_err'=>'手机号码格式不正确',
    'model/user/phone_err'=>'手机号码不正确',
    'model/user/pass_reset_err'=>'密码重置失败，请重试',
    'model/user/pass_reset_ok'=>'密码重置成功',
    'model/user/id_err'=>'用户编号错误',
    'model/user/visit_tip'=>'每日仅能获取%s次推广访问积分',
    'model/user/visit_err'=>'插入推广记录失败，请重试',
    'model/user/visit_ok'=>'推广成功',
    'model/user/reward_tip'=>'用户【%s、%s】消费%s积分，获得奖励%s积分',
    'model/user/reward_ok'=>'分销提成成功',

    'model/website/refer_max'=> '每日仅能%s次来路记录',
    'model/website/visit_err'=>'来路记录失败，请重试',
    'model/website/visit_ok'=>'来路记录成功',

    'controller/no_popedom'=>'您没有权限访问此数据，请升级会员',
    'controller/pay_play_points'=>'观看此数据，需要支付【%s】积分，确认支付吗？',
    'controller/pay_down_points'=>'下载此数据，需要支付【%s】积分，确认支付吗？',
    'controller/in_try_see'=>'进入试看模式',
    'controller/charge_data'=>'此页面为收费数据，请先登录后访问！',
    'controller/try_see_end'=>'试看结束,是否支付[%s]积分观看完整数据？您还剩下[%s]积分，请先充值！',
    'controller/not_enough_points'=>'对不起,观看此页面数据需要[%s]积分，您还剩下[%s]积分，请先充值！',
    'controller/popedom_ok'=>'权限验证通过',
    'controller/an_error_occurred'=>'发生错误',
    'controller/visitor'=>'游客',
    'controller/get_type_err'=>'获取分类失败，请选择其它分类！',

    'index/require_login'=>'登录后才可以发表留言',
    'index/require_content'=>'内容不能为空',
    'index/require_cn'=>'内容必须包含中文,请重新输入',
    'index/mid_err'=>'模型mid错误',
    'index/thanks_msg_audit'=>'谢谢，我们会尽快审核你的留言！',
    'index/thanks_msg'=>'感谢你的留言！',
    'index/blacklist_keyword'=>'您的评论有敏感词请修改后再提交！',
    'index/blacklist_ip'=>'禁止评论!',
    'index/blacklist_placeholder'=>'请输入黑名单关键字,每个关键字占一行',
    'index/blacklist_placeholder_ip'=>'请输入黑名单IP,每个IP占一行,非IP格式提交后将会被过滤',
    'index/payment_status'=>'该支付选项未开启！',
    'index/payment_not'=>'未找到支付选项！',
    'index/payment_ok'=>'支付完成！',
    'index/haved'=>'您已参与过了！',
    'index/ok'=>'操作成功！',
    'index/pwd_repeat'=>'请不要重复验证!',
    'index/pwd_frequently'=>'请不要频繁请求，请稍后重试!',
    'index/pwd_repeat'=>'请不要重复验证!',
    'index/no_login'=>'未登录',
    'index/ulog_fee'=>'收费收据需单独记录',
    'index/buy_popedom1'=>'您已经购买过此条数据，无需再次支付，请刷新页面重试！',
    'index/buy_popedom2'=>'对不起,更新用户积分信息失败，请刷新重试！',
    'index/buy_popedom3'=>'对不起,查看此页面数据需要[%s]积分，您还剩下[%s]积分，请先充值！',
    'index/bind_haved'=>'已经绑定该账号',
    'index/bind_ok'=>'绑定成功',
    'index/logincallback1'=>'同步信息注册失败，请联系管理员',
    'index/logincallback2'=>'获取第三方用户信息失败，请重试',
    'index/reg_ok'=> '注册成功',
    'index/portrait_tip1'=> '未开启自定义头像功能',
    'index/portrait_no_upload'=> '未找到上传的文件(原因：表单名可能错误，默认表单名“file”或“imgdata”)！',
    'index/portrait_ext'=> '非系统允许的上传格式！',
    'index/upload_err'=>'文件上传失败！',
    'index/portrait_err'=>'更新会员头像信息失败！',
    'index/portrait_thumb_err'=>'生成缩放头像图片文件失败！',
    'index/min_pay'=>'最小充值金额不能低于%s元',
    'index/order_not'=>'获取单据失败',
    'index/order_payed'=>'该单据已支付完成',
    'index/page_type'=>'列表页',
    'index/page_detail'=>'内容页',
    'index/page_play'=>'播放页',
    'index/page_down'=>'下载页',
    'index/try_see'=>'试看',

    'admin/public/head/title'=>'安全第一请勿泄露后台地址 - Copyright by 苹果CMS内容管理系统',
    'admin/public/jump/title'=>'跳转提示',

    'admin/index/login/title'=>'后台管理中心 - Copyright by 苹果CMS内容管理系统',
    'admin/index/login/tip_welcome'=>'欢迎使用',
    'admin/index/login/tip_sys'=>'系统管理',
    'admin/index/login/filed_no'=>'账号',
    'admin/index/login/filed_pass'=>'密码',
    'admin/index/login/filed_verify'=>'验证码',
    'admin/index/login/btn_submit'=>'立即登录',
    'admin/index/login/tip_declare'=>'免责声明',
    'admin/index/login/tip_declare_txt'=>'本程序开源且永久免费无任何内置数据，请在遵守当地法律的前提下使用，对用户在使用过程中的信息内容本程序不负任何责任！自由！平等！分享！开源！',
    'admin/index/login/verify_no'=>'请输入用户名',
    'admin/index/login/verify_pass'=>'请输入密码',
    'admin/index/login/verify_verify'=>'请输入验证码',

    'admin/index/index/name' =>'超级控制台',
    'admin/index/index/menu_switch' =>'打开/关闭左侧导航',
    'admin/index/index/menu_index' =>'网站首页',
    'admin/index/index/menu_lock' =>'锁屏操作',
    'admin/index/index/menu_logout' =>'退出登陆',
    'admin/index/index/menu_cache' =>'缓存',
    'admin/index/index/menu_cache_clear' =>'清理缓存',
    'admin/index/index/menu_welcome' =>'欢迎页面',
    'admin/index/index/menu_opt' =>'操作',
    'admin/index/index/menu_close_all' =>'关闭全部',
    'admin/index/index/menu_close_other' =>'关闭其他',
    'admin/index/index/menu_max' =>'最多可打开10个标签页',
    'admin/index/index/menu_close_empty' =>'没有可以关闭的窗口了@_@',

    'admin/index/quickmenu/name' =>'自定义快捷菜单',
    'admin/index/quickmenu/tip' =>'格式要求：1.菜单名称,菜单链接地址；2.每个快捷菜单各占一行；<br>
        1，支持远程地址，例如： 更新日志,//www.baidu.com/<br>
        2，支持插件文件，例如： 插件文件菜单,/application/xxxx.html<br>
        3，支持系统模块，例如： 文章管理,art/data<br>
        4，支持行分隔符，例如： 分隔符,###',


    'admin/index/welcome/filed_os' =>'运行环境',
    'admin/index/welcome/filed_host' =>'服务器IP/端口',
    'admin/index/welcome/filed_php_ver' =>'PHP版本',
    'admin/index/welcome/filed_thinkphp_ver' =>'ThinkPHP版本',
    'admin/index/welcome/filed_max_upload' =>'最大上传限制',
    'admin/index/welcome/filed_date' =>'服务器日期',
    'admin/index/welcome/filed_ver' =>'程序版本',
    'admin/index/welcome/filed_license' =>'授权类型',
    'admin/index/welcome/tip_update_db' =>'数据库更新提示',
    'admin/index/welcome/tip_update_db_txt' =>'提示，发现本地有数据库升级脚本？是否执行升级操作！执行完毕后将自动删除脚本！',
    'admin/index/welcome/tip_update_go'=>'【点击进升级数据库脚本】',
    'admin/index/welcome/filed_login_num' =>'登录次数',
    'admin/index/welcome/filed_last_login_ip' =>'上次登录IP',
    'admin/index/welcome/filed_last_login_time' =>'上次登录时间',
    'admin/index/welcome/tip_warn' =>'请不要修改系统文件，以免升级出现故障！本程序不内置任何数据，添加任何数据均是个人行为！请在遵守法律的前提下使用程序，否则后果自负！',

    'admin/index/quick_tit'=>'↓↓↓自定义菜单区域↓↓↓',
    'admin/index/title'=>'后台管理中心',
    'admin/index/welcome/title'=>'欢迎页面',
    'admin/index/quickmenu/title'=>'快捷菜单配置',
    'admin/index/cache_data'=>'发现配置缓存，请及时清理...',
    'admin/index/clear_ok'=>'缓存清理成功',
    'admin/index/clear_err'=>'缓存清理失败',
    'admin/index/iframe'=>'布局切换成功，跳转中',
    'admin/index/pass_err'=>'密码错误',
    'admin/index/unlock_ok'=>'解锁成功',
    'admin/index/title'=>'后台管理中心',



    'admin/system/config/title'=>'网站参数配置',
    'admin/system/config/base'=>'基本设置',
    'admin/system/config/performance'=>'性能优化',
    'admin/system/config/parameters'=>'预留参数',
    'admin/system/config/backstage'=>'后台设置',
    'admin/system/config/site_name'=>'网站名称',
    'admin/system/config/site_url'=>'网站域名',
    'admin/system/config/site_url_tip'=>'如：www.test.com,不要加http://',
    'admin/system/config/site_wapurl'=>'手机站域名',
    'admin/system/config/site_wapurl_tip'=>'如：wap.test.com,不要加http://',
    'admin/system/config/site_keywords'=>'关键字',
    'admin/system/config/site_description'=>'描述信息',
    'admin/system/config/site_icp'=>'备案号',
    'admin/system/config/site_qq'=>'客服QQ',
    'admin/system/config/site_email'=>'客服邮箱',
    'admin/system/config/install_dir'=>'安装目录',

    'admin/system/config/install_dir_tip'=>'根目录 ＂/＂，二级目录 ＂/maccms/＂以此类推',
    'admin/system/config/site_logo'=>'默认LOGO',
    'admin/system/config/site_waplogo'=>'手机站LOGO',
    'admin/system/config/template_dir'=>'网站模板',
    'admin/system/config/site_polyfill'=>'兼容旧版本',
    'admin/system/config/site_polyfill_tip'=>'开启后将引入polyfill兼容旧版本浏览器',
    'admin/system/config/site_logo_tip'=>'图片地址或路径',
    'admin/system/config/html_dir'=>'模板目录',
    'admin/system/config/mob_status'=>'自适应手机',
    'admin/system/config/mob_status_tip'=>'多域名：访问wap域名会自动使用手机模板；单域名：手机访问会自动使用手机模板；',
    'admin/system/config/mob_template_dir'=>'手机模板',
    'admin/system/config/mob_one'=>'单域',
    'admin/system/config/mob_multiple'=>'多域',
    'admin/system/config/site_tj'=>'统计代码',
    'admin/system/config/site_status'=>'站点状态',
    'admin/system/config/site_close_tip'=>'关闭提示',
    'admin/system/config/pathinfo_depr'=>'PATH分隔符',
    'admin/system/config/pathinfo_depr_tip'=>'PATHINFO分隔符 修改后将改变非静态模式下URL地址',
    'admin/system/config/xg'=>'斜杠/',
    'admin/system/config/zhx'=>'中横线-',
    'admin/system/config/xhx'=>'下横线_',
    'admin/system/config/suffix'=>'页面后缀名',

    'admin/system/config/wall_filter'=>'假墙防御',
    'admin/system/config/wall_unicode'=>'编码方式',
    'admin/system/config/wall_blank'=>'空白方式',
    'admin/system/config/wall_filter_tip'=>'开启后将部分页面传入参数在页面展示时编码或替换为空解决假墙威胁',
    'admin/system/config/popedom_filter'=>'数据权限过滤',
    'admin/system/config/popedom_filter_tip'=>'开启后将隐藏没有权限的分类和数据',
    'admin/system/config/cache_type'=>'缓存方式',
    'admin/system/config/cache_host'=>'服务器',
    'admin/system/config/cache_port'=>'端口',
    'admin/system/config/cache_username'=>'账号',
    'admin/system/config/cache_password'=>'密码',
    'admin/system/config/cache_host_tip'=>'缓存服务器IP',
    'admin/system/config/cache_port_tip'=>'缓存服务器端口',
    'admin/system/config/cache_username_tip'=>'缓存服务账号,没有请留空',
    'admin/system/config/cache_password_tip'=>'缓存服务密码,没有请留空',
    'admin/system/config/cache_test'=>'测试连接',

    'admin/system/config/cache_flag'=>'缓存标识',
    'admin/system/config/cache_flag_tip'=>'多站共用一个服务器上memcache、redis时需区分开',
    'admin/system/config/cache_flag_auto'=>'留空将自动生成',
    'admin/system/config/cache_core'=>'数据缓存',
    'admin/system/config/cache_time'=>'数据缓存时间',
    'admin/system/config/cache_time_tip'=>'单位秒建议设置为3600以上',
    'admin/system/config/cache_page'=>'页面缓存',
    'admin/system/config/cache_time_page'=>'页面缓存时间',
    'admin/system/config/compress'=>'压缩页面',
    'admin/system/config/search'=>'搜索开关',
    'admin/system/config/search_verify'=>'搜索验证码',
    'admin/system/config/search_timespan'=>'搜索间隔',
    'admin/system/config/search_timespan_tip'=>'单位秒，建议设置为3秒以上',
    'admin/system/config/search_len'=>'搜索参数长度',
    'admin/system/config/search_len_tip'=>'搜索页+筛选页单个参数长度限制,默认10个字符,超过自动截取。',
    'admin/system/config/404'=>'404页面',
    'admin/system/config/404_tip'=>'自定义404页面，页面放在模板的public目录下无需后缀名，默认为jump',
    'admin/system/config/show'=>'筛选页开关',
    'admin/system/config/show_verify'=>'筛选验证码',
    'admin/system/config/input_type'=>'参数获取方式',
    'admin/system/config/input_type_tip'=>'建议采用get方式，安全并且容易分析日志',
    'admin/system/config/ajax_page'=>'预留ajax开关',
    'admin/system/config/ajax_page_tip'=>'系统每个页面请求都预留对应的ajax方法，不需要建议关闭。例如：vod/search和vod/ajax_search',
    'admin/system/config/search_vod_rule'=>'视频搜索规则',
    'admin/system/config/search_rule_tip'=>'注意，仅影响wd参数，勾选过多影响性能，建议3个以内',
    'admin/system/config/search_art_rule'=>'文章搜索规则',
    'admin/system/config/vod_search_optimise'=>'视频搜索优化',
    'admin/system/config/vod_search_optimise/frontend'=>'前台',
    'admin/system/config/vod_search_optimise/collect'=>'采集',
    'admin/system/config/vod_search_optimise_tip'=>'缓存LIKE模糊查询的结果',
    'admin/system/config/vod_search_optimise_cache_minutes'=>'搜索缓存分钟',
    'admin/system/config/vod_search_optimise_cache_minutes_tip'=>'视频LIKE模糊查询缓存时间（分钟），最小1分钟，建议60分钟以上',
    'admin/system/config/copyright_status'=>'版权提示',
    'admin/system/config/copyright_msg'=>'提示信息',
    'admin/system/config/copyright_jump_detail'=>'内容页跳转',
    'admin/system/config/copyright_jump_play'=>'播放页跳转',
    'admin/system/config/copyright_jump_iframe'=>'iframe播放页跳转',
    'admin/system/config/copyright_notice'=>'版权提示信息',
    'admin/system/config/browser_junmp'=>'防红防封跳转',
    'admin/system/config/browser_junmp_tip'=>'使用微信、QQ访问将直接显示跳转提示页面',
    'admin/system/config/collect_timespan'=>'采集间隔',
    'admin/system/config/collect_timespan_tip'=>'单位秒，建议设置为3秒以上',
    'admin/system/config/pagesize'=>'后台每页数',
    'admin/system/config/pagesize_tip'=>'每页显示数据量、一般设置为20左右',
    'admin/system/config/lang'=>'后台语言包',

    'admin/system/config/makesize'=>'生成每页数',
    'admin/system/config/makesize_tip'=>'批量生成每次生成都少页、一般设置为20左右',
    'admin/system/config/admin_login_verify'=>'后台登录验证码',
    'admin/system/config/editor'=>'富文本编辑器',
    'admin/system/config/editor_tip'=>'系统默认带ueditor，使用其他请先到官网下载扩展包',
    'admin/system/config/player_sort'=>'播放器顺序',
    'admin/system/config/player_sort_tip'=>'前台展示显示的播放器排列顺序',
    'admin/system/config/global'=>'全局',
    'admin/system/config/encrypt'=>'加密地址',
    'admin/system/config/encrypt_not'=>'不加密',
    'admin/system/config/search_hot'=>'搜索热词',
    'admin/system/config/art_extend_class'=>'文章扩展分类',
    'admin/system/config/vod_extend_class'=>'视频扩展分类',
    'admin/system/config/vod_extend_state'=>'视频资源',
    'admin/system/config/vod_extend_version'=>'视频版本',
    'admin/system/config/vod_extend_weekday'=>'视频周期',
    'admin/system/config/vod_extend_area'=>'视频地区',
    'admin/system/config/vod_extend_lang'=>'视频语言',
    'admin/system/config/vod_extend_year'=>'视频年代',
    'admin/system/config/actor_extend_area'=>'演员地区',
    'admin/system/config/filter_words'=>'词语过滤',
    'admin/system/config/filter_words_tip'=>'应用于搜索参数、评论、留言的禁用词汇；多个用,号分隔',
    'admin/system/config/extra_var'=>'自定义参数',
    'admin/system/config/extra_var_tip'=>'每行一个变量,例如aa$$$我是老王；模板调用方法$GLOBALS[\'config\'][\'extra\'][\'aa\']',
    'admin/system/config/test_err'=>'发生错误，请检查是否开启扩展库和配置项!',

    'admin/system/configapi/title'=>'采集接口API配置',
    'admin/system/configapi/vod'=>'视频API设置',
    'admin/system/configapi/art'=>'文章API设置',
    'admin/system/configapi/actor'=>'演员API设置',
    'admin/system/configapi/role'=>'角色API设置',
    'admin/system/configapi/website'=>'网址API设置',
    'admin/system/configapi/vod_tip'=>'提示信息：<br>
                            1,视频列表地址/api.php/provide/vod/?ac=list<br>
                            2,视频详情地址/api.php/provide/vod/?ac=detail',
    'admin/system/configapi/status'=>'接口开关',
    'admin/system/configapi/charge'=>'是否收费',
    'admin/system/configapi/detail_inc_hits'=>'增加点击',
    'admin/system/configapi/detail_inc_hits_tip'=>'ac=detail且ids只有一个时，点击量+1',
    'admin/system/configapi/pagesize'=>'列表每页显示数量',
    'admin/system/configapi/pagesize_tip'=>'数据每页显示量，不建议超过50',
    'admin/system/configapi/imgurl'=>'图片域名',
    'admin/system/configapi/imgurl_tip'=>'显示图片的完整访问路径所需要，以http(s):开头,/结尾，不包含upload目录',
    'admin/system/configapi/typefilter'=>'分类过滤参数',
    'admin/system/configapi/typefilter_tip'=>'列出需要显示的分类ids例如 11,12,13',
    'admin/system/configapi/datafilter'=>'数据过滤参数',
    'admin/system/configapi/datafilter_tip'=>'SQL查询条件例如 vod_status=1',
    'admin/system/configapi/datafilter_tip_art'=>'SQL查询条件例如 art_status=1',
    'admin/system/configapi/datafilter_tip_actor'=>'SQL查询条件例如 actor_status=1',
    'admin/system/configapi/datafilter_tip_role'=>'SQL查询条件例如 role_status=1',
    'admin/system/configapi/datafilter_tip_website'=>'SQL查询条件例如 website_status=1',
    'admin/system/configapi/cachetime'=>'数据缓存时间',
    'admin/system/configapi/cachetime_tip'=>'数据缓存时间',
    'admin/system/configapi/from'=>'指定播放组',
    'admin/system/configapi/from_tip'=>'多个用,号分隔，如: youku,iqiyi,qvod',
    'admin/system/configapi/auth'=>'授权域名',
    'admin/system/configapi/art_tip'=>'提示信息：<br>
                            1,文章列表地址/api.php/provide/art/?ac=list<br>
                            2,文章详情地址/api.php/provide/art/?ac=detail',

    'admin/system/configapi/actor_tip'=>'提示信息：<br>
                            1,演员列表地址/api.php/provide/actor/?ac=list<br>
                            2,演员详情地址/api.php/provide/actor/?ac=detail',
    'admin/system/configapi/role_tip'=>'提示信息：<br>
                            1,角色列表地址/api.php/provide/role/?ac=list<br>
                            2,角色详情地址/api.php/provide/role/?ac=detail',
    'admin/system/configapi/website_tip'=>'提示信息：<br>
                            1,网址列表地址/api.php/provide/website/?ac=list<br>
                            2,网址详情地址/api.php/provide/website/?ac=detail',




    'admin/system/configcollect/title'=>'采集参数配置',
    'admin/system/configcollect/vod'=>'视频采集设置',
    'admin/system/configcollect/art'=>'文章采集设置',
    'admin/system/configcollect/actor'=>'演员采集设置',
    'admin/system/configcollect/role'=>'角色采集设置',
    'admin/system/configcollect/website'=>'网址采集设置',
    'admin/system/configcollect/comment'=>'评论采集设置',
    'admin/system/configcollect/status'=>'数据状态',
    'admin/system/configcollect/words'=>'采集词库设置',
    'admin/system/configcollect/hits_rnd'=>'随机点击量',
    'admin/system/configcollect/updown_rnd'=>'随机顶踩',
    'admin/system/configcollect/score_rnd'=>'随机评分数',
    'admin/system/configcollect/sync_pic'=>'自动同步图片',
    'admin/system/configcollect/auto_tag'=>'自动生成TAG',
    'admin/system/configcollect/class_filter'=>'扩展分类优化',
    'admin/system/configcollect/class_filter_tip'=>'将自动过滤扩分类名称里的[片,剧],例如动作片会变为动作;欧美剧会变成欧美;',
    'admin/system/configcollect/psename'=>'名称同义词替换',
    'admin/system/configcollect/psename_tip'=>'自动转换名称同义词，降低重复率。例如:第1季=第一季;',
    'admin/system/configcollect/psernd'=>'详情随机插入语句',
    'admin/system/configcollect/psesyn'=>'详情同义词替换',
    'admin/system/configcollect/pseplayer'=>'播放器同义词替换',
    'admin/system/configcollect/psearea'=>'地区同义词替换',
    'admin/system/configcollect/pselang'=>'语言同义词替换',
    'admin/system/configcollect/inrule'=>'入库重复规则',
    'admin/system/configcollect/inrule_tip_role'=>'豆瓣ID和视频名称传入按豆瓣ID优先',
    'admin/system/configcollect/inrule_tip_comment'=>'关联数据名称或豆瓣ID传入按豆瓣ID优先（豆瓣ID仅在视频模块起作用）',
    'admin/system/configcollect/uprule'=>'二次更新规则',
    'admin/system/configcollect/filter'=>'数据过滤',
    'admin/system/configcollect/urlrole'=>'地址二更规则',
    'admin/system/configcollect/urlrole/use_more'=>'集数多优先',
    // 'admin/system/configcollect/urlrole_tip'=>'二次更新地址遇到同类型播放器。替换：只保留新提交的地址。合并：整合原有地址和新地址去重。集数多优先：两次资源里连载集数多的优先替换使用。',
    'admin/system/configcollect/urlrole_tip'=>'二次更新地址遇到同类型播放器。替换：只保留新提交的地址。合并：整合原有地址和新地址去重。',
    'admin/system/configcollect/content'=>'详情',
    'admin/system/configcollect/playurl'=>'播放地址',
    'admin/system/configcollect/downurl'=>'下载地址',
    'admin/system/configcollect/words_tip'=>'同义词库：每行一个，不要有空行，格式：替换前=替换}后，不允许出现#号。<br>
                    随机词库：字符段一般20条左右即可，可以是笑话小故事，也可以加入超链接。<br>
                    此项功能影响采集性能，请不要一次加入太多词条 。适当的伪原创有助于搜索引擎收录。<br>
                    不使用词库功能请在【采集参数设置中禁用】。',
    'admin/system/configcollect/vod_namewords'=>'视频名称同义库',
    'admin/system/configcollect/vod_thesaurus'=>'视频详情同义词库',
    'admin/system/configcollect/vod_playerwords'=>'播放器同义词库',
    'admin/system/configcollect/vod_areawords'=>'地区同义词库',
    'admin/system/configcollect/vod_langwords'=>'语言同义词库',
    'admin/system/configcollect/vod_words'=>'视频详情随机词库',
    'admin/system/configcollect/art_thesaurus'=>'文章详情同义词库',
    'admin/system/configcollect/art_words'=>'文章详情随机词库',
    'admin/system/configcollect/actor_thesaurus'=>'演员详情同义词库',
    'admin/system/configcollect/actor_words'=>'演员详情随机词库',
    'admin/system/configcollect/role_thesaurus'=>'角色详情同义词库',
    'admin/system/configcollect/role_words'=>'角色详情随机词库',
    'admin/system/configcollect/website_thesaurus'=>'网址详情同义词库',
    'admin/system/configcollect/website_words'=>'网址详情随机词库',
    'admin/system/configcollect/comment_thesaurus'=>'评论详情同义词库',
    'admin/system/configcollect/comment_words'=>'评论详情随机词库',

    'admin/system/configcomment/title'=>'评论留言配置',
    'admin/system/configcomment/gbook'=>'留言本',
    'admin/system/configcomment/gbook_tip'=>'是否开启留言本',
    'admin/system/configcomment/audit'=>'是否审核',
    'admin/system/configcomment/login'=>'登录留言',
    'admin/system/configcomment/verify'=>'验证码',
    'admin/system/configcomment/pagesize'=>'每页个数',
    'admin/system/configcomment/pagesize_tip'=>'建议设置20以上',
    'admin/system/configcomment/timespan'=>'时间间隔',
    'admin/system/configcomment/timespan_tip'=>'单位秒、建议3秒以上',
    'admin/system/configcomment/comment'=>'评论状态',
    'admin/system/configcomment/comment_tip'=>'是否开启评论',




    'admin/system/configconnect/title'=>'整合登录配置',
    'admin/system/configconnect/tip'=>'提示信息：<br>
                        1,QQ登录地址/index.php/user/oauth/?type=qq<br>
                        2,微信登录地址/index.php/user/oauth/?type=weixin<br>
                        3,回调地址/index.php/user/logincallback/?type=qq 或  /index.php/user/logincallback/?type=weixin',
    'admin/system/configconnect/qq'=>'qq登录',
    'admin/system/configconnect/go_reg'=>'点击进入注册',
    'admin/system/configconnect/wx'=>'wx登录',



    'admin/system/configemail/title'=>'邮件发送配置',
    'admin/system/configemail/tip'=>'提示信息：<br>
                        修改后请先点击保存然后再测试发送。内容支持{$maccms.***}标签，  {$user.***}标签， {$code}验证码， {$time}有效时间。',
    'admin/system/configemail/type'=>'发送方式',
    'admin/system/configemail/time'=>'有效期限',
    'admin/system/configemail/time_tip'=>'邮件验证码多少分钟后失效',
    'admin/system/configemail/nick'=>'发件昵称',
    'admin/system/configemail/test'=>'测试地址',
    'admin/system/configemail/btn_test'=>'发送测试邮件',
    'admin/system/configemail/test_title'=>'测试标题',
    'admin/system/configemail/test_body'=>'测试正文',
    'admin/system/configemail/user_reg_title'=>'用户注册标题',
    'admin/system/configemail/user_reg_body'=>'用户注册正文',
    'admin/system/configemail/user_bind_title'=>'用户绑定标题',
    'admin/system/configemail/user_bind_body'=>'用户绑定正文',
    'admin/system/configemail/user_findpass_title'=>'用户找回标题',
    'admin/system/configemail/user_findpass_body'=>'用户找回正文',
    'admin/system/configemail/test_err'=>'发生错误，请检查是否开启相应扩展库',

    'admin/system/configinterface/pass_check'=>'保存失败，安全起见入库密码必须大于等于16位!',
    'admin/system/configinterface/title'=>'站外入库配置',
    'admin/system/configinterface/tip'=>'提示信息：<br>
                        1.转换分类每个各占一行;<br>
                        2.本地分类在前,采集分类在后(动作片=动作);<br>
                        3.不要有多余的空行;<br>
                        4.视频播放器、备注、地址、服务器组、文章分页等多页数据连接符都是$$$<br>
                        5.入库接口地址 视频/api.php/receive/vod；文章/api.php/receive/art； 演员/api.php/receive/actor；角色/api.php/receive/role；网址/api.php/receive/website；<br>',
    'admin/system/configinterface/status'=>'接口开关',
    'admin/system/configinterface/pass'=>'入库免登录密码',
    'admin/system/configinterface/pass_tip'=>'避免暴力破解密码建议设置位16位以上包含大小写字母数字及特殊符号或者修改api.php入口文件名',
    'admin/system/configinterface/vod_type'=>'视频分类转换',
    'admin/system/configinterface/art_type'=>'文章分类转换',
    'admin/system/configinterface/actor_type'=>'演员分类转换',
    'admin/system/configinterface/website_type'=>'网址分类转换',
    'admin/system/configinterface/title'=>'站外入库配置',
    'admin/system/configinterface/title'=>'站外入库配置',
    'admin/system/configinterface/title'=>'站外入库配置',



    'admin/system/configpay/title'=>'在线支付配置',
    'admin/system/configpay/card'=>'卡密',
    'admin/system/configpay/config'=>'支付配置',
    'admin/system/configpay/notify'=>'回调通知地址',
    'admin/system/configpay/notify_tip'=>'支付接口通知回调地址',
    'admin/system/configpay/min'=>'最小充值金额',
    'admin/system/configpay/min_tip'=>'单位RMB元，最低1元',
    'admin/system/configpay/scale'=>'兑换比例',
    'admin/system/configpay/scale_tip'=>'1元人民币等于多少积分',
    'admin/system/configpay/card_config'=>'卡密配置',
    'admin/system/configpay/card_url'=>'销售网址',
    'admin/system/configpay/card_url_tip'=>'第三方卡密平台',

    'admin/system/configplay/title'=>'播放器参数配置',
    'admin/system/configplay/tip'=>'提示信息：<br>
                        1，播放器大小支持px像素和%百分比两种单位。<br>
                        2，如果设置的值不包含任何单位，则按照100%处理。',

    'admin/system/configplay/width'=>'播放器宽度',
    'admin/system/configplay/width_tip'=>'例如: 540px 或 100%',
    'admin/system/configplay/height'=>'播放器高度',
    'admin/system/configplay/height_tip'=>'例如: 460px 或 100%',
    'admin/system/configplay/widthmob'=>'手机播放宽度',
    'admin/system/configplay/heightmob'=>'手机播放高度',
    'admin/system/configplay/widthpop'=>'弹窗口宽度',
    'admin/system/configplay/heightpop'=>'弹窗口高度',
    'admin/system/configplay/second'=>'预加载时间',
    'admin/system/configplay/second_tip'=>'例如: 5，单位是秒',
    'admin/system/configplay/prestrain'=>'预加载提示',
    'admin/system/configplay/prestrain_tip'=>'不要出现双引号、单引号等特殊字符号',
    'admin/system/configplay/buffer'=>'缓冲提示',
    'admin/system/configplay/buffer_tip'=>'不要出现双引号、单引号等特殊字符号',
    'admin/system/configplay/parse'=>'接口地址',
    'admin/system/configplay/parse_tip'=>'第三方处理接口',
    'admin/system/configplay/autofull'=>'自动全屏',
    'admin/system/configplay/showtop'=>'头部开关',
    'admin/system/configplay/showlist'=>'列表开关',
    'admin/system/configplay/flag'=>'播放器文件',
    'admin/system/configplay/flag_tip'=>'本地播放器',
    'admin/system/configplay/colors'=>'播放器颜色',
    'admin/system/configplay/select_colors'=>'选择颜色',
    'admin/system/configplay/select_colors_tip'=>'颜色使用16进制表示法，不带#号，以逗号分割，一共15个可配置颜色!
                        <br>依次是：背景色，文字颜色，链接颜色，分组标题背景色，分组标题颜色，当前分组标题颜色，当前集数颜色，集数列表滚动条凸出部分的颜色，滚动条上下按钮上三角箭头的颜色，滚动条的背景颜色，滚动条空白部分的颜色，滚动条立体滚动条阴影的颜色 ，滚动条亮边的颜色，滚动条强阴影的颜色，滚动条的基本颜色',


    'admin/system/configweixin/title'=>'微信对接配置',
    'admin/system/configweixin/tip'=>'公众号对接域名、公众号搜索域名、网站域名通常一致，因公众号可能产生的封域名问题，你可以单独设置域名进行对接。<br>
                    接口地址/api.php/wechat',
    'admin/system/configweixin/duijie'=>'对接域名',
    'admin/system/configweixin/duijie_tip'=>'请以http 或 https开头，公众号后台的对接地址为【http://wx.test.com/inc/weixin.php】',
    'admin/system/configweixin/sousuo'=>'搜索域名',
    'admin/system/configweixin/sousuo_tip'=>'用来在公共号内显示的域名，一般红名更换此域名即可，请以http 或 https开头',
    'admin/system/configweixin/token'=>'对接TOKEN',
    'admin/system/configweixin/token_tip'=>'公众号后台对接的token密钥',
    'admin/system/configweixin/guanzhu'=>'关注回复',
    'admin/system/configweixin/guanzhu_tip'=>'用户关注你的公众号时自动回复的一句话',
    'admin/system/configweixin/wuziyuan'=>'无资源回复',
    'admin/system/configweixin/wuziyuan_tip'=>'用户搜索不到资源时默认返回的信息',
    'admin/system/configweixin/wuziyuanlink'=>'无资源回复链接',
    'admin/system/configweixin/wuziyuanlink_tip'=>'无资源回复链接或内容',
    'admin/system/configweixin/pagelink'=>'返回页面地址',
    'admin/system/configweixin/pagelink_detail'=>'内容页面',
    'admin/system/configweixin/pagelink_play'=>'播放页面',
    'admin/system/configweixin/pagelink_search'=>'搜索页面',
    'admin/system/configweixin/msgtype'=>'返回内容类型',
    'admin/system/configweixin/msgtype_pic'=>'图文',
    'admin/system/configweixin/msgtype_font'=>'文字',
    'admin/system/configweixin/msgtype_tip'=>'微信新规定图文只能返回1条',
    'admin/system/configweixin/gjc'=>'自定义关键词',
    'admin/system/configweixin/keyword'=>'关键词',
    'admin/system/configweixin/return_text'=>'返回文字',
    'admin/system/configweixin/return_pic'=>'返回图片',
    'admin/system/configweixin/return_link'=>'返回链接',

    'admin/system/configuser/title'=>'会员参数配置',
    'admin/system/configuser/tip'=>'提示信息：<br>
                        1.开启试看功能的话,播放窗口将采用iframe动态页面方式载入，可能影响性能哦; <br>',
    'admin/system/configuser/model'=>'会员模块',
    'admin/system/configuser/reg_open'=>'注册开关',
    'admin/system/configuser/reg_status'=>'注册默认状态',
    'admin/system/configuser/phone_reg_verify'=>'手机注册验证',
    'admin/system/configuser/email_reg_verify'=>'邮箱注册验证',
    'admin/system/configuser/email_white_hosts'=>'邮箱白名单',
    'admin/system/configuser/email_white_hosts_tip'=>"填写后，只有在白名单内的邮箱主机名才允许注册。多个用,或换行分隔。如: qq.com,360.com\n注：如果黑白名单都填写，策略将同时生效",
    'admin/system/configuser/email_black_hosts'=>'邮箱黑名单',
    'admin/system/configuser/email_black_hosts_tip'=>"填写后，在黑名单内的邮箱主机名不允许注册。多个用,或换行分隔。如: protonmail.com,gmail.com\n注：如果黑白名单都填写，策略将同时生效",
    'admin/system/configuser/reg_verify'=>'注册验证码',
    'admin/system/configuser/login_verify'=>'登录验证码',
    'admin/system/configuser/reg_points'=>'注册赠分',
    'admin/system/configuser/reg_points_tip'=>'用户注册成功后默认赠送积分',
    'admin/system/configuser/reg_num'=>'每IP限制',
    'admin/system/configuser/reg_num_tip'=>'每个IP每日限制注册次数',
    'admin/system/configuser/invite_reg_points'=>'邀请注册积分',
    'admin/system/configuser/invite_reg_points_tip'=>'成功邀请用户赚取奖励积分',
    'admin/system/configuser/invite_visit_points'=>'推广访问积分',
    'admin/system/configuser/invite_visit_points_tip'=>'成功邀请访问赚取奖励积分',
    'admin/system/configuser/invite_visit_num_tip'=>'每个IP每日限制可以获取几次推广访问积分',
    'admin/system/configuser/reward_status'=>'三级分销状态',
    'admin/system/configuser/reward_ratio'=>'一级提成比例',
    'admin/system/configuser/reward_ratio2'=>'二级提成比例',
    'admin/system/configuser/reward_ratio3'=>'三级提成比例',
    'admin/system/configuser/reward_unit'=>'单位百分比',
    'admin/system/configuser/reward_tip'=>'用户支付成功分销推广者都可获得一定比例的积分,提成不足1积分将忽略。',
    'admin/system/configuser/cash_status'=>'提现状态',
    'admin/system/configuser/cash_ratio'=>'兑换比例',
    'admin/system/configuser/cash_ratio_tip'=>'兑换比例1元=多少积分',
    'admin/system/configuser/cash_min'=>'最低提现金额',
    'admin/system/configuser/cash_min_tip'=>'最低提现多少金额',
    'admin/system/configuser/trysee'=>'试看时长',
    'admin/system/configuser/trysee_tip'=>'全局设置无权限需要积分点播的试看时长，单位分钟；0表示关闭全局试看；',
    'admin/system/configuser/vod_points_type'=>'视频收费方式',
    'admin/system/configuser/vod_points_0'=>'每集',
    'admin/system/configuser/vod_points_1'=>'每数据',
    'admin/system/configuser/art_points_type'=>'文章收费方式',
    'admin/system/configuser/art_points_0'=>'每页',
    'admin/system/configuser/art_points_1'=>'每数据',
    'admin/system/configuser/portrait_status'=>'头像上传',
    'admin/system/configuser/portrait_size'=>'头像尺寸',
    'admin/system/configuser/portrait_size_tip'=>'尺寸建议100x100',
    'admin/system/configuser/filter_words'=>'用户名过滤',
    'admin/system/configuser/filter_words_tip'=>'多个用,号分隔',

    'admin/system/configurl/title'=>'url参数配置',
    'admin/system/configurl/view'=>'浏览模式设置',
    'admin/system/configurl/html'=>'静态路径设置',
    'admin/system/configurl/route'=>'路由伪静态设置',
    'admin/system/configurl/dynamic'=>'动态模式',
    'admin/system/configurl/static'=>'静态模式',
    'admin/system/configurl/static_one'=>'静态每集一页',
    'admin/system/configurl/index'=>'首页',
    'admin/system/configurl/map'=>'地图',
    'admin/system/configurl/search'=>'搜索',
    'admin/system/configurl/label'=>'自定义页面',
    'admin/system/configurl/vod_type'=>'视频分类',
    'admin/system/configurl/vod_show'=>'视频分类筛选',
    'admin/system/configurl/art_type'=>'文章分类',
    'admin/system/configurl/art_show'=>'文章分类筛选',
    'admin/system/configurl/topic_index'=>'专题首页',
    'admin/system/configurl/topic_detail'=>'专题详情',
    'admin/system/configurl/vod_detail'=>'视频详情',
    'admin/system/configurl/vod_play'=>'视频播放',
    'admin/system/configurl/vod_down'=>'视频下载',
    'admin/system/configurl/art_detail'=>'文章详情',
    'admin/system/configurl/variable'=>'变量',
    'admin/system/configurl/structure'=>'常用结构',
    'admin/system/configurl/multipage_connector'=>'多页连接符',
    'admin/system/configurl/multipage_connector_tip'=>'例如：分隔符为-,第二页是type/index-2.html。',
    'admin/system/configurl/common_connector'=>'多页连接符',
    'admin/system/configurl/file_ext'=>'文件后缀',
    'admin/system/configurl/file_ext_tip'=>'例如：建议为html',
    'admin/system/configurl/common_ext'=>'文件后缀',
    'admin/system/configurl/route_tip'=>'提示信息：<br>
                        1.动态下,开启路由状态将自动重写URL; <br>
                        2.路由规则每行一条,中间用=>隔开,左边是路由表达式,右边是路由地址; <br>
                        3.路由地址是系统提供的，原则上不会变，只需要调整路由表达式。<br>
                        4.不要有多余的空行;<br>
                        5.ID类型根据需要自行选择，选择拼音时，数据中不能有重复的拼音，否则获取数据会出现问题。<br>
                        6.URL分隔符支持/ 和 - 。不建议用其他符号。',

    'admin/system/configurl/suffix_hide'=>'隐藏后缀',
    'admin/system/configurl/route_status'=>'路由状态',
    'admin/system/configurl/rewrite_status'=>'伪静态状态',
    'admin/system/configurl/route_rule'=>'路由规则',
    'admin/system/configurl/encode_key'=>'加密KEY',
    'admin/system/configurl/encode_len'=>'加密长度',
    'admin/system/configurl/encode_tip'=>'KEY改变URL也会改变。长度表示加密后的长度不会少于原有数字长度。',

    'admin/system/configupload/title'=>'附件参数配置',
    'admin/system/configupload/tip'=>'提示：不管是本地上传还是第三方存储，都需要先上传到本地，再转存到第三方。<br>
                        所以本地操作系统的临时文件目录必须要有写入权限，否则会上传文件失败。<br>
                        PHP临时文件目录修改方法在PHP配置文件里搜索sys_temp_dir。<br>
                        当前操作系统临时文件目录：',
    'admin/system/configupload/write_ok'=>'测试写入临时文件成功，上传状态正常',
    'admin/system/configupload/write_err'=>'测试写入临时文件失败，请检查临时文件目录权限',
    'admin/system/configupload/thumb_tip'=>'上传图片时是否自动生成缩略图',
    'admin/system/configupload/thumb_size'=>'尺寸大小',
    'admin/system/configupload/thumb_size_tip'=>'缩略图尺寸例如长x宽,例300x300',
    'admin/system/configupload/thumb_type'=>'裁剪方式',
    'admin/system/configupload/thumb_type1'=>'等比例缩放',
    'admin/system/configupload/thumb_type2'=>'缩放后填充',
    'admin/system/configupload/thumb_type3'=>'居中裁剪',
    'admin/system/configupload/thumb_type4'=>'左上角裁剪',
    'admin/system/configupload/thumb_type5'=>'右下角裁剪',
    'admin/system/configupload/thumb_type6'=>'固定尺寸缩放',
    'admin/system/configupload/watermark'=>'文字水印',
    'admin/system/configupload/watermark_location'=>'水印位置',
    'admin/system/configupload/watermark_location1'=>'左上角',
    'admin/system/configupload/watermark_location2'=>'上居中',
    'admin/system/configupload/watermark_location3'=>'右上角',
    'admin/system/configupload/watermark_location4'=>'左居中',
    'admin/system/configupload/watermark_location5'=>'居中',
    'admin/system/configupload/watermark_location6'=>'右居中',
    'admin/system/configupload/watermark_location7'=>'左下角',
    'admin/system/configupload/watermark_location8'=>'下居中',
    'admin/system/configupload/watermark_location9'=>'右下角',
    'admin/system/configupload/watermark_content'=>'水印内容',
    'admin/system/configupload/watermark_size'=>'字体大小',
    'admin/system/configupload/watermark_size_tip'=>'单位：px(像素)',
    'admin/system/configupload/watermark_color'=>'水印颜色',
    'admin/system/configupload/watermark_color_tip'=>'格式:#000000',
    'admin/system/configupload/protocol'=>'三方访问协议',
    'admin/system/configupload/protocol_tip'=>'使用第三方存储会转换为mac://开头，这表示模板里展示图片链接中把mac替换为http或https',
    'admin/system/configupload/mode'=>'保存方式',
    'admin/system/configupload/mode_local'=>'本地保存',
    'admin/system/configupload/mode_remote'=>'远程访问',
    'admin/system/configupload/remoteurl'=>'图片远程URL',
    'admin/system/configupload/remoteurl_tip'=>'本地图片如存在远程，可使用此功',
    'admin/system/configupload/img_key'=>'反盗链标识',
    'admin/system/configupload/img_key_tip'=>'需要处理防盗链的域名或关键字多个请用|连接',
    'admin/system/configupload/img_api'=>'反盗链接口',
    'admin/system/configupload/img_api_tip'=>'处理防盗链图片的接口地址',
    'admin/system/configupload/keep_local'=>'保留本地',
    'admin/system/configupload/keep_local_tip'=>'如果选择上传到远程，上传完成后，本地也将保留一份',

    'admin/system/configsms/title'=>'短信发送配置',
    'admin/system/configsms/tip'=>'提示信息：<br>
                        请务必按照短信接口服务商的要求做好短信签名和短信内容的设置。<br>',
    'admin/system/configsms/type'=>'服务商',
    'admin/system/configsms/sign'=>'短信签名',
    'admin/system/configsms/tpl_code_reg'=>'注册模板编号',
    'admin/system/configsms/tpl_code_tip'=>'模板编号需要在服务商短信控制台中申请',
    'admin/system/configsms/tpl_code_bind'=>'绑定模板编号',
    'admin/system/configsms/tpl_code_findpass'=>'找回密码模板编号',
    'admin/system/configsms/test_err'=>'发生错误，请检查是否开启相应扩展库!',



    'admin/system/configseo/vod_index'=>'视频首页',
    'admin/system/configseo/art_index'=>'文章首页',
    'admin/system/configseo/actor_index'=>'演员首页',
    'admin/system/configseo/role_index'=>'角色首页',
    'admin/system/configseo/plot_index'=>'剧情首页',
    'admin/system/configseo/website_index'=>'网址首页',
    'admin/system/configseo/tit'=>'标题',
    'admin/system/configseo/key'=>'关键字',
    'admin/system/configseo/des'=>'描述',
    'admin/system/configseo/tip_des'=>'提示信息',










    'admin/actor/title'=>'演员管理',
    'admin/addon/title'=>'插件管理',
    'admin/addon/get_dir_err'=>'获取插件目录失败',
    'admin/addon/get_addon_info_err'=>'获取插件信息失败',
    'admin/addon/lack_config_err'=>'缺少插件配置文件info.ini',
    'admin/addon/name_empty_err'=>'插件名称不能为空',
    'admin/addon/haved_err'=>'已经存在插件',
    'admin/addon/path_err'=>'非法目录请求',
    'admin/addon/add_tip'=>'提示：<br>
                1.请确保第三方插件符合程序开发规范。
                2.--使用前请做好安全检测避免出现安全问题。',


    'admin/admin/title'=>'管理员管理',
    'admin/admin/del_cur_err'=>'禁止删除当前登录账号',
    'admin/admin/popedom_tip'=>'提示：<br>
                    1.权限控制精准到每个操作，创始人ID为1的管理员拥有所有权限。
                    2.--开头的是页面内按钮操作选项。',


    'admin/art/title'=>'文章管理',
    'admin/card/title'=>'充值卡管理',
    'admin/card/make_num'=>'生成数量',
    'admin/card/please_input_make_num'=>'请输入生成数量',
    'admin/card/please_input_money'=>'请输入充值卡面值',
    'admin/card/please_input_points'=>'请输入充值卡点数',


    'admin/card/import_tip'=>'卡号,密码,创建时间',
    'admin/cash/title'=>'提现管理',
    'admin/vodserver/title'=>'服务器组管理',
    'admin/vodserver/url'=>'服务器组地址',
    'admin/vodplayer/title'=>'播放器管理',
    'admin/voddowner/title'=>'下载器管理',
    'admin/vodplayer/alone_api_url'=>'独立接口地址',
    'admin/vodplayer/alone_api_url'=>'独立接口地址',
    'admin/vodplayer/code_tip'=>'唯一标识英文、纯数字会自动加_，禁止出现./\\等符号',
    'admin/vodplayer/name_tip'=>'中文名称',
    'admin/vodplayer/api_url'=>'接口地址',
    'admin/vodplayer/api_url_tip'=>'独立接口地址，权重高于全局播放器设置的接口',
    'admin/vodplayer/sort_tip'=>'数值越大排列越靠前',
    'admin/vodplayer/code_empty'=>'请输入编码',
    'admin/vodplayer/import_tip'=>'提示：<br>
                       1.请确保导入文件格式正确。',


    'admin/timming/title'=>'定时任务管理',
    'admin/timming/unique_id'=>'唯一标识英文',
    'admin/timming/call_method'=>'调用方法',
    'admin/timming/exec_file'=>'执行文件',
    'admin/timming/collect'=>'自定义采集collect',
    'admin/timming/make'=>'静态生成make',
    'admin/timming/cj'=>'自定义采集规则cj',
    'admin/timming/cache'=>'清理缓存cache',
    'admin/timming/urlsend'=>'网址推送urlsend',
    'admin/timming/attach_param'=>'附加参数',
    'admin/timming/attach_param_tip'=>'可以留空，例如:ac=timming&id=1',
    'admin/timming/exec_cycle'=>'执行周期',
    'admin/timming/exec_time'=>'执行时间',

    'monday'=>'周一',
    'tuesday'=>'周二',
    'wednesday'=>'周三',
    'thursday'=>'周四',
    'friday'=>'周五',
    'saturday'=>'周六',
    'sunday'=>'周日',


    'admin/domain/title'=>'站群管理',
    'admin/domain/help_tip'=>'提示信息：<br>
                        1，此功能支持非静态模式下同1个数据库不同域名显示不同的模板和网站配置信息<br>
                        2，不限制域名网站数量<br>
                        3，导入文本格式是：域名$网站名称$关键字$描述$模板$模板目录$广告目录。每行一个网站。清空原有数据。<br>',

    'admin/domain/title'=>'站群管理',


    'admin/website/title'=>'网址管理',
    'admin/website/referer'=>'总来路',
    'admin/website/referer_month'=>'月来路',
    'admin/website/referer_week'=>'周来路',
    'admin/website/referer_day'=>'日来路',




    'admin/vod/title'=>'视频管理',
    'admin/vod/no'=>'没有',
    'admin/vod/have'=>'有',
    'admin/vod/plot/title'=>'分集剧情管理',
    'admin/vod/del_play_must_select_play'=>'删除播放组时，必须选择播放器参数',
    'admin/vod/del_down_must_select_down'=>'删除下载组时，必须选择下载器参数',
    'admin/vod/select_weekday'=>'选择周期',
    'admin/vod/select_area'=>'选择地区',
    'admin/vod/select_lang'=>'选择语言',
    'admin/vod/select_player'=>'选择播放器',
    'admin/vod/select_server'=>'选择服务器',
    'admin/vod/player_empty'=>'空播放器',
    'admin/vod/select_downer'=>'选择下载器',
    'admin/vod/downer_empty'=>'空下载器',
    'admin/vod/select_isend'=>'选择完结',
    'admin/vod/select_copyright'=>'选择版权',
    'admin/vod/is_end'=>'已完结',
    'admin/vod/no_end'=>'未完结',
    'admin/vod/del_player'=>'删播放组',
    'admin/vod/del_downer'=>'删下载组',
    'admin/vod/episode_plot'=>'分集剧情',
    'admin/vod/plot'=>'分集剧情',
    'admin/vod/plot_name'=>'剧情标题',
    'admin/vod/move_up'=>'上移',
    'admin/vod/move_down'=>'下移',
    'admin/vod/copyright_open'=>'开启版权处理',
    'admin/vod/copyright_close'=>'关闭版权处理',
    'admin/vod/move_behind'=>'幕后',
    'admin/vod/total'=>'总集数',
    'admin/vod/serial'=>'连载数',
    'admin/vod/pubdate'=>'上映日期',
    'admin/vod/director'=>'导演',
    'admin/vod/writer'=>'编剧',
    'admin/vod/tv'=>'电视频道',
    'admin/vod/weekday'=>'节目周期',
    'admin/vod/duration'=>'视频时长',
    'admin/vod/douban_score'=>'豆瓣评分',
    'admin/vod/douban_id'=>'豆瓣ID',
    'admin/vod/douban_id_empty'=>'请填写豆瓣ID',
    'admin/vod/rel_vod_tip'=>'如“变形金刚”1、2、3部ID分别为11,12,13或将每部都填“变形金刚”',
    'admin/vod/rel_art_tip'=>'如“变形金刚资讯”1、2、3部ID分别为11,12,13或将每部都填“变形金刚资讯”',
    'admin/vod/version'=>'资源版本',
    'admin/vod/state'=>'资源类别',
    'admin/vod/isend'=>'完结',
    'admin/vod/tpl'=>'内容页模板',
    'admin/vod/tpl_play'=>'播放页模板',
    'admin/vod/tpl_down'=>'下载页模板',
    'admin/vod/correct'=>'校正',
    'admin/vod/reverse_order'=>'倒序',
    'admin/vod/del_prefix'=>'去前缀',
    'admin/vod/complete_works'=>'全集',
    'admin/vod/stint_play'=>'点播每集所需积分',
    'admin/vod/stint_down'=>'下载每集所需积分',
    'admin/vod/select_plot'=>'选择分集剧情',
    'admin/vod/copyright'=>'版权',
    'admin/vod/serialize'=>'连载',
    'admin/vod/add_group_play'=>'添加一组播放',
    'admin/vod/add_group_down'=>'添加一组下载',
    'admin/batch_tip'=>'共%s条数据需要处理，每页%s条，共%s页，正在处理第%s页',


    'admin/template/title'=>'模板管理',
    'admin/template/ads/title'=>'广告位管理',
    'admin/template/wizard/title'=>'标签向导管理',
    'admin/template/ext_safe_tip'=>'安全提示，后缀名只允许htm,html,js,xml',
    'admin/template/php_safe_tip'=>'安全提示，模板中包含php代码禁止在后台编辑',
    'admin/template/call_code'=>'调用代码',
    'admin/template/current_dir'=>'当前路径',
    'admin/template/name_tip'=>'后缀名仅允许html、htm、js、xml；自定义页面以label_开头。',
    'admin/template/reverse_order'=>'倒序',
    'admin/template/positive_order'=>'正序',
    'admin/template/filter_search'=>'筛选查询链接',
    'admin/template/reply_content'=>'回复内容',


    'admin/cj/title'=>'自定义采集管理',
    'admin/cj/url/title'=>'采集URL地址',
    'admin/cj/publish/title'=>'内容发布管理',
    'admin/cj/url_list_err'=>'获取网址信息失败',
    'admin/cj/url_cj_complete'=>'URL采集完成',
    'admin/cj/content/tip'=>'正在采集内容，共【%s】条，分%s页，每页采集%s条，当前%s页',
    'admin/cj/content_cj_complete'=>'内容采集完成',
    'admin/cj/cj_complete'=>'采集完成',
    'admin/cj/content_into/tip'=>'正在导入内容，共【%s】条，分%s页，每页采集%s条，当前%s页',
    'admin/cj/content_into/complete'=>'内容入库完成',
    'admin/cj/cj_url'=>'采集网址',
    'admin/cj/cj_content'=>'采集内容',
    'admin/cj/content_publish'=>'内容发布',
    'admin/cj/publish_plan'=>'发布方案',
    'admin/cj/collected'=>'已采集',
    'admin/cj/collected_not'=>'未采集',
    'admin/cj/published'=>'已发布',
    'admin/cj/trim_space'=>'去空格',
    'admin/cj/label_data_rel'=>'标签与数据库对应关系',
    'admin/cj/data_column'=>'数据库字段',
    'admin/cj/label_column'=>'标签字段',
    'admin/cj/processing_function'=>'处理函数',
    'admin/cj/rule_url'=>'网址规则',
    'admin/cj/rule_content'=>'内容规则',
    'admin/cj/rule_diy'=>'自定义规则',
    'admin/cj/adv_config'=>'高级配置',
    'admin/cj/rule_name'=>'规则名称',
    'admin/cj/rule_name_en'=>'规则英文名',
    'admin/cj/page_charset'=>'网页编码',
    'admin/cj/cj_model'=>'采集模块',
    'admin/cj/url_collect'=>'网址采集',
    'admin/cj/url_type'=>'网址类型',
    'admin/cj/sequence_url'=>'序列网址',
    'admin/cj/multi_url'=>'多个网址',
    'admin/cj/one_url'=>'单一网址',
    'admin/cj/wildcard_tip'=>'作为通配符',
    'admin/cj/page_num_config'=>'页码配置',
    'admin/cj/page_num_increment'=>'页码递增',
    'admin/cj/one_per_line'=>'每行一条',
    'admin/cj/url_config'=>'网址配置',
    'admin/cj/url_must_contain'=>'网址中必须包含',
    'admin/cj/url_not_contain'=>'网址中不得包含',
    'admin/cj/collect_interval'=>'采集区间',
    'admin/cj/wildcard_prompt'=>'<p>1、匹配规则请设置开始和结束符，具体内容使用“[内容]”做为通配符 。</p>
                                    <p>2、匹配规则也可以是固定内容，只要不出现“[内容]”通配符就视为固定内容。</p>
                                    <p>3、过滤选项格式为“要过滤的内容[|]替换值”，要过滤的内容支持正则表达式，每行一条。</p>',



    'admin/cj/title_rule'=>'标题规则',
    'admin/cj/match_rule'=>'匹配规则',
    'admin/cj/filter_rule'=>'过滤规则',
    'admin/cj/type_rule'=>'分类规则',
    'admin/cj/content_rule'=>'内容规则',
    'admin/cj/page_mode'=>'分页模式',
    'admin/cj/list_all_mode'=>'全部列出模式',
    'admin/cj/next_page_mode'=>'上下页模式',
    'admin/cj/next_page_rule'=>'下一页规则',
    'admin/cj/next_page_tip'=>'请填写下一页超链接中间的代码。如：<a href="http://www.xxx.com/page_1.html">下一页</a>，他的“下一页规则”为“下一页”。',
    'admin/cj/add_group'=>'添加一组',

    'admin/cj/content_page'=>'内容分页',
    'admin/cj/no_page'=>'不分页',
    'admin/cj/original_page'=>'按原文分页',
    'admin/cj/import_sort'=>'导入顺序',
    'admin/cj/same_to_site'=>'与目标站相同',
    'admin/cj/opposite_to_site'=>'与目标站相反',


    'admin/collect/title'=>'采集资源管理',
    'admin/collect/load_break'=>'正在载入断点位置，请稍后。。。',
    'admin/collect/view_all_resource'=>'查看全部资源',
    'admin/collect/cj_select'=>'采选中',
    'admin/collect/cj_today'=>'采当天',
    'admin/collect/cj_all'=>'采全部',
    'admin/collect/clear_bind'=>'清空绑定',
    'admin/collect/name'=>'资源名称',
    'admin/collect/api_url'=>'接口地址',
    'admin/collect/attach_param'=>'附加参数',
    'admin/collect/attach_param_tip'=>'提示信息：一般&开头，例如老版xml格式采集下载地址需加入&ct=1',
    'admin/collect/api_type'=>'接口类型',
    'admin/collect/data_type'=>'资源类型',
    'admin/collect/data_opt'=>'数据操作',
    'admin/collect/data_opt_tip'=>'提示信息：如果某个资源作为副资源不想新增数据，可以只勾选更新。',
    'admin/collect/add_update'=>'新增+更新',
    'admin/collect/add'=>'新增',
    'admin/collect/update'=>'更新',
    'admin/collect/url_filter'=>'地址过滤',
    'admin/collect/no_filter'=>'不过滤',
    'admin/collect/filter_code'=>'过滤代码',
    'admin/collect/filter_code_tip'=>'多组地址的资源开启白名单后只会入库指定代码的地址。比如 youku,iqiyi',
    'admin/collect/filter_year'=>'过滤年份',
    'admin/collect/filter_year_tip'=>'填写后仅入库指定年份的视频。多个年份用英文半角逗号分隔，如 2022,2023',
    'admin/collect/test_ok'=>'测试类型成功，接口类型',

    'admin/comment/title'=>'评论管理',

    'admin/gbook/title'=>'留言本管理',


    'admin/group/title'=>'会员组管理',
    'admin/group/reg_group_del_err'=>'注册默认会员组无法删除!',
    'admin/group/help_tip'=>'提示信息：<br>
        1.游客、普通会员属于系统内置会员组,无法删除和禁用; <br>2.请单独设置每个会员组的权限,不会向下继承权限;',
    'admin/group/pack_day'=>'包天',
    'admin/group/pack_week'=>'包周',
    'admin/group/pack_month'=>'包月',
    'admin/group/pack_year'=>'包年',
    'admin/group/popedom'=>'相关权限',
    'admin/group/popedom_tip'=>'提示：<br>
                    1.列表页、内容页、播放页、下载页4个权限，控制是否可以进入页面，没权限会直接返回提示信息。<br>
                    2.试看权限：如果没有访问播放页的权限、或者有权限但是需要积分购买的数据，开启了试看权限也是可以进入页面的。',
    'admin/group/popedom_list'=>'列表页',
    'admin/group/popedom_detail'=>'内容页',
    'admin/group/popedom_play'=>'播放页',
    'admin/group/popedom_down'=>'下载页',
    'admin/group/popedom_trysee'=>'试看',

    'admin/annex/title'=>'附件管理',
    'admin/annex/check'=>'检测无效文件',
    'admin/annex/check_complete'=>'无效文件清理完毕',
    'admin/annex/info_tip'=>'共%s数据，分%s次检测，每次%s条，当前第%s次',

    'admin/annex/init_tip'=>'<strong>附件数据初始化1.0版本</strong><br>
                            1，将对分类表、视频、文章、网址、演员、角色、会员等表进行检索。<br>
                            2，将包含本地图片地址内容插入到附件表中。<br>
                            3，建议升级的版本执行一次。',
    'admin/annex/init_data'=>'数据初始化',
    'admin/annex/dir_model'=>'文件夹模式',
    'admin/annex/check_ok'=>'附件数据初始化结束',
    'admin/annex/check_tip1'=>'正在检测%s表...共%s条，分%s次检测，每次%s条，当前第%s次',
    'admin/annex/check_jump'=>'表%s检测完毕，稍后继续...',


    'admin/images/title'=>'图片管理',
    'admin/images/sync_complete'=>'同步操作完毕!',
    'admin/images/sync_tip'=>'共%s条数据需要处理，每页%s条，共%s页，正在处理第%s页数据',
    'admin/images/sync_range'=>'同步范围',
    'admin/images/sync_option'=>'同步选项',
    'admin/images/date'=>'数据日期',
    'admin/images/opt/tip1'=>'同步字段-同步详情图片时，同步选项参数不起作用!',
    'admin/images/opt/tip2'=>'每页同步条数，不建议设置过大',
    'admin/images/opt/pic'=>'主图',
    'admin/images/pic_content'=>'详情图片',


    'admin/database/title'=>'数据库管理',
    'admin/database/select_export_table'=>'请选择您要备份的数据表！',
    'admin/database/lock_check'=>'检测到有一个备份任务正在执行，请稍后再试！！',
    'admin/database/backup_err'=>'备份出错！',
    'admin/database/backup_ok'=>'备份完成！',
    'admin/database/select_file'=>'请选择您要恢复的备份文件！',
    'admin/database/import_ok'=>'数据恢复完成！',
    'admin/database/import_err'=>'数据恢复出错！',
    'admin/database/file_damage'=>'备份文件可能已经损坏，请检查！',
    'admin/database/select_optimize_table'=>'请选择您要优化的数据表！',
    'admin/database/optimize_ok'=>'数据表优化完成！',
    'admin/database/optimize_err'=>'数据表优化失败！',
    'admin/database/select_repair_table'=>'请选择您要修复的数据表！',
    'admin/database/repair_ok'=>'数据表修复完成！',
    'admin/database/repair_err'=>'数据表修复失败！',
    'admin/database/select_del_file'=>'请选择您要删除的备份文件！',
    'admin/database/backup_db'=>'备份数据库',
    'admin/database/import_db'=>'还原数据库',
    'admin/database/optimize_db'=>'优化数据库',
    'admin/database/repair_db'=>'修复数据库',
    'admin/database/table'=>'表名',
    'admin/database/count'=>'数据量',
    'admin/database/size'=>'大小',
    'admin/database/redundancy'=>'冗余',
    'admin/database/optimize'=>'优化',
    'admin/database/repair'=>'修复',
    'admin/database/backup_name'=>'备份名称',
    'admin/database/backup_num'=>'备份卷数',
    'admin/database/backup_zip'=>'备份压缩',
    'admin/database/backup_size'=>'备份大小',
    'admin/database/backup_time'=>'备份时间',
    'admin/database/import'=>'还原',
    'admin/database/import_confirm'=>'确认还原此备份吗？此操作不可恢复',
    'admin/database/batch_replace'=>'批量替换',
    'admin/database/select_table'=>'选择数据表',
    'admin/database/select_col'=>'选择字段',
    'admin/database/field'=>'要替换的字段',
    'admin/database/findstr'=>'被替换的内容',
    'admin/database/tostr'=>'替换为内容',
    'admin/database/where'=>'替换条件',

    'admin/database/sql'=>'执行sql语句',
    'admin/database/sql_tip'=>'常用语句对照：<br>
                        1.查询数据
                        SELECT * FROM {pre}vod   查询所有数据<br>
                        SELECT * FROM {pre}vod WHERE vod_id=1000   查询指定ID数据
                        <br>
                        2.删除数据
                        DELETE FROM {pre}vod   删除所有数据<br>
                        DELETE FROM {pre}vod WHERE vod_id=1000   删除指定的第几条数据<br>
                        DELETE FROM {pre}vod WHERE vod_actor LIKE \'%刘德华%\'   vod_actor"刘德华"的数据
                        <br>
                        3.修改数据
                        UPDATE {pre}vod SET vod_hits=1   将所有vod_hits字段里的值修改成"1"<br>
                        UPDATE {pre}vod SET vod_hits=1 WHERE vod_id=1000  指定的第几条数据把vod_hits字段里的值修改成"1"
                        <br>
                        4.替换图片地址
                        UPDATE {pre}vod SET vod_pic=REPLACE(vod_pic, \'原始字符串\', \'替换成其他字符串\')
                        <br>
                        5.清空数据ID重新从1开始
                        TRUNCATE {pre}vod',

    'admin/safety/data_inspect'=>'挂马检测',
    'admin/safety/data_inspect_tip'=>'<strong>挂马检测3.0版本</strong><br>
                            1，将对分类表，视频表，文章表，会员表等表结构进行检查。<br>
                            2，检测包含script,iframe等特殊字符串。<br>
                            3，将自动清除挂马代码。<br>
                            4，不能保证100%清除，如还有问题请自行进入phpmyadmin或其他数据库管理工具里清除。<br>
                            5，建议清理多次，直到没有出现问题数据。',
    'admin/safety/data_clear_ok'=>'清理结束,请再次执行,以免有漏掉的数据',
    'admin/safety/data_check_tip1'=>'开始检测%s表...',
    'admin/safety/data_check_tip2'=>'共检测到%s条危险数据...',

    'admin/safety/exec'=>'确认执行',
    'admin/safety/file_inspect'=>'文件安全检测',
    'admin/safety/file_inspect_tip'=>'<strong>安全检测3.0版本</strong><br>
                            1，将对网站内所有文件进行比对筛选进行检查。<br>
                            2，将原版程序包内自带文件将比对md5罗列出。<br>
                            3，将原版程序包内没有的新增文件罗列出。<br>
                            4，不能保证100%正确，如还有问题请到官网github提报。<br>
                            5，建议多次检测，详细检查每个罗列出的文件。',
    'admin/safety/file_msg1'=>'获取官方文件数据失败，请重试',
    'admin/safety/file_msg2'=>'获取本地文件列表失败，请重试',
    'admin/safety/file_msg3'=>'新增文件',
    'admin/safety/file_msg4'=>'差异文件',

    'admin/link/title'=>'友情链接管理',
    'admin/link/text_link'=>'文字链接',
    'admin/link/pic_link'=>'图片链接',


    'admin/make/title'=>'生成静态管理',
    'admin/make/view_model_static_err'=>'浏览模式非静态，无法生成',
    'admin/make/typepage_make_complete'=>'分类页生成完毕',
    'admin/make/typepage_make_complete_later_make_index'=>'分类页生成完毕，稍后继续生成首页',
    'admin/make/list_make_complate_later'=>'列表页生成完毕，稍后继续',
    'admin/make/type_tip'=>'正在生成【%s】列表页，共%s页，分%s次生成，当前%s次',
    'admin/make/type_timming_tip'=>'定时任务完毕，本次每个分类生成%s个列表页面，避免网站卡死！',
    'admin/make/topicpage_make_complete'=>'专题列表页生成完毕',
    'admin/make/topic_index_tip'=>'正在生成专题列表页，共%s页，分%s次生成，当前%s次',
    'admin/make/topic_tip'=>'正在生成专题内容页，共%s条',
    'admin/make/topic_make_complete'=>'专题内容页生成完毕',
    'admin/make/info_make_complete'=>'内容页生成完毕',
    'admin/make/info_make_complete_later_make_type'=>'内容页生成完毕，稍后继续生成分类页',
    'admin/make/info_make_complete_later'=>'内容页生成完毕，稍后继续',
    'admin/make/info_tip'=>'正在生成【%s】内容页，共%s条，分%s次生成，每次%s条，当前第%s次',
    'admin/make/label_tip'=>'正在生成自定义页，共%s个页',
    'admin/make/label_complete'=>'自定义页生成完毕',
    'admin/make/select_type'=>'选择分类',
    'admin/make/all_type'=>'全部分类',
    'admin/make/today_type'=>'当天分类',
    'admin/make/select_info'=>'选择内容',
    'admin/make/all_info'=>'全部内容',
    'admin/make/today_info'=>'当天内容',
    'admin/make/no_make_info'=>'未生成的',
    'admin/make/one_today'=>'一键当天',
    'admin/make/topic_list'=>'专题列表',
    'admin/make/select_topic'=>'选择专题',
    'admin/make/all_topic'=>'全部专题',
    'admin/make/topic_index'=>'专题首页',
    'admin/make/label_page'=>'自定义页面',
    'admin/make/rss'=>'RSS订阅文件',
    'admin/make/google'=>'谷歌SiteMap',
    'admin/make/baidu'=>'百度SiteMap',
    'admin/make/so'=>'SO-SiteMap',
    'admin/make/sogou'=>'搜狗SiteMap',
    'admin/make/bing'=>'Bing-SiteMap',
    'admin/make/sm'=>'神马SiteMap',
    'admin/make/make_page_num'=>'生成页数',


    'admin/order/title'=>'订单管理',
    'admin/order/order_no'=>'单号',
    'admin/order/order_money'=>'订单金额',
    'admin/order/order_status'=>'订单状态',
    'admin/order/order_time'=>'下单时间',
    'admin/order/pay_type'=>'支付类型',
    'admin/order/pay_time'=>'支付时间',




    'admin/plog/title'=>'积分日志管理',
    'admin/plog/log_time'=>'日志时间',
    'admin/plog/points_recharge'=>'积分充值',
    'admin/plog/reg_promote'=>'注册推广',
    'admin/plog/visit_promote'=>'访问推广',
    'admin/plog/three_distribution'=>'三级分销',
    'admin/plog/points_upgrade'=>'积分升级',
    'admin/plog/points_buy'=>'积分消费',
    'admin/plog/points_withdrawal'=>'积分提现',


    'admin/role/title'=>'角色管理',
    'admin/topic/title'=>'专题管理',
    'admin/topic/vod_include'=>'视频收录',
    'admin/topic/art_include'=>'文章收录',
    'admin/topic/tpl_empty'=>'请输入专题模板',
    'admin/topic/count'=>'包含数据量',


    'admin/type/title'=>'分类管理',
    'admin/type/type_tpl'=>'分类页模版',
    'admin/type/show_tpl'=>'筛选页模版',
    'admin/type/detail_tpl'=>'内容页模版',
    'admin/type/play_tpl'=>'播放页模板',
    'admin/type/down_tpl'=>'下载页模板',
    'admin/type/tip'=>'提示信息：<br>
            1,新增加分类后，请到用户-会员组分别对每个组设置权限，否则会提示无权限访问',
    'admin/type/parent_type'=>'父级分类',
    'admin/type/top_type'=>'顶级分类',
    'admin/type/logo'=>'分类图标',
    'admin/type/pic'=>'分类封面',
    'admin/type/tpl_empty'=>'请输入分类页模板',
    'admin/type/extend_version'=>'扩展版本',
    'admin/type/extend_state'=>'扩展资源',
    'admin/type/extend_director'=>'扩展导演',
    'admin/type/extend_star'=>'扩展演员',


    'admin/ulog/title'=>'日志管理',

    'admin/update/step1_a'=>'在线升级进行中第一步【文件升级】,请稍后......',
    'admin/update/step1_b'=>'正在下载升级文件包...',
    'admin/update/download_err'=>'下载升级包失败，请重试...',
    'admin/update/download_ok'=>'下载升级包完毕...',
    'admin/update/upgrade_package_processed'=>'正在处理升级包的文件...',
    'admin/update/upgrade_err'=>'升级失败，请检查系统目录及文件权限！...',
    'admin/update/step2_a'=>'在线升级进行中第二步【数据升级】,请稍后......',
    'admin/update/upgrade_sql'=>'发现数据库升级脚本文件，正在处理...',
    'admin/update/no_sql'=>'未发现数据库升级脚本，稍后进入更新数据缓存部分...',
    'admin/update/step3_a'=>'在线升级进行中第三步【更新缓存】,请稍后......',
    'admin/update/update_cache'=>'更新数据缓存文件...',
    'admin/update/upgrade_complete'=>'恭喜您，系统升级完毕...',

    'admin/upload/test_write_ok'=>'测试写入成功',
    'admin/upload/test_write_ok'=>'写入失败，请检查临时文件目录权限',
    'admin/upload/not_find_extend'=>'未找到第三方扩展上传类库',
    'admin/upload/no_input_file'=>'未找到上传的文件(原因：表单名可能错误，默认表单名“file”或“imgdata”)！',
    'admin/upload/forbidden_ext'=>'非系统允许的上传格式！',
    'admin/upload/upload_success'=>'文件上传成功！',
    'admin/upload/upload_faild'=>'文件上传失败！',
    'admin/upload/make_thumb_faild'=>'文件上传失败！',
    'admin/upload/upload_safe'=>'文件包含危险内容！',

    'admin/urlsend/title'=>'URL推送管理',
    'admin/urlsend/no_data'=>'没有获取到数据',
    'admin/urlsend/tip'=>'共%s条数据等待推送，分%s页推送，当前第%s页',
    'admin/urlsend/complete'=>'数据推送完毕',
    'admin/urlsend/tip2'=>'断点会记录在缓存中，更新缓存后断点将消失。<br>
            开始推送之前请先填写好上面的所需配置项。<br>
            当前站点配置域名：',
    'admin/urlsend/send_genre'=>'推送类型',
    'admin/urlsend/page_send_num'=>'每页推送数',
    'admin/urlsend/start_page'=>'起始页码',
    'admin/urlsend/in_break_point_exec'=>'进入断点继续执行',
    'admin/urlsend/send_range'=>'推送范围',
    'admin/urlsend/add_update'=>'新增+更新',
    'admin/urlsend/add'=>'新增',

    'admin/user/title'=>'用户管理',
    'admin/user/comment_record'=>'评论记录',
    'admin/user/order_record'=>'订单记录',
    'admin/user/visit_record'=>'访问记录',
    'admin/user/point_record'=>'积分记录',
    'admin/user/withdrawals_record'=>'提现记录',
    'admin/user/three_distribution'=>'三级分销',
    'admin/user/time_end'=>'包时截止',
    'admin/user/find_question'=>'找回问题',
    'admin/user/find_answer'=>'找回答案',
    'admin/user/access_empty'=>'请输入账号',
    'admin/user/pass_empty'=>'请输入密码',

    'admin/user/reward/select_level'=>'选择级别',
    'admin/user/reward/one_distribution'=>'一级分销',
    'admin/user/reward/two_distribution'=>'二级分销',
    'admin/user/reward/three_distribution'=>'三级分销',
    'admin/user/reward/distribution_level'=>'三级分销',
    'admin/user/reward/one_people_num'=>'一级分销总人数',
    'admin/user/reward/two_people_num'=>'二级分销总人数',
    'admin/user/reward/three_people_num'=>'三级分销总人数',
    'admin/user/reward/total_commission_points'=>'总提成积分',

    'admin/visit/title'=>'访问记录管理',


    'api/auth_err'=>'域名未授权',
    'api/close_err'=>'接口关闭err',
    'api/pass_err'=>'非法使用err',
    'api/pass_safe_err'=>'安全起见入库密码必须大于等于16位',
    'api/require_name'=>'名称必须err',
    'api/require_type'=>'分类名称和分类ID至少填写一项err',
    'api/require_sex'=>'性别必须err',
    'api/require_actor_name'=>'演员名必须err',
    'api/require_role_name'=>'角色名必须err',
    'api/require_rel_vod'=>'关联数据名vod_name或豆瓣编号douban_id至少填写一项err',
    'api/require_rel_name'=>'关联数据名rel_name或豆瓣编号douban_id至少填写一项err',
    'api/require_mid'=>'模块ID必须err',
    'api/require_comment_name'=>'评论昵称须err',
    'api/require_comment_name'=>'评论内容必须err',
    'api/never'=>'从未',
    'api/task_tip_exec'=>'任务：%s，状态：%s，上次执行时间：%s---执行',
    'api/task_tip_jump'=>'任务：%s，状态：%s，上次执行时间：%s---跳过',


    'install/title'=>'苹果CMS-V10系统安装',
    'install/header'=>'感谢您选择苹果CMS-V10系搭建网站',
    'install/lang'=>'语言包[langs]',
    'install/select_lang'=>'请选择语言包[select lang]',
    'install/lang_tip'=>'请根据自己身需要选择后台语言包',

    'install/user_agreement_title'=>'苹果CMS用户协议 适用于所有用户',
    'install/user_agreement'=>' 请您在使用(苹果CMS)前仔细阅读如下条款。包括免除或者限制作者责任的免责条款及对用户的权利限制。您的安装使用行为将视为对本《用户许可协议》的接受，并同意接受本《用户许可协议》各项条款的约束。 <br /><br />
                一、安装和使用： <br />
                (苹果CMS)是免费和开源提供给您使用的，您可安装无限制数量副本。 您必须保证在不进行非法活动，不违反所在国家相关政策法规的前提下使用本软件。 <br /><br />
                二、免责声明：  <br />
                本软件并无附带任何形式的明示的或暗示的保证，包括任何关于本软件的适用性, 无侵犯知识产权或适合作某一特定用途的保证。  <br />
                在任何情况下，对于因使用本软件或无法使用本软件而导致的任何损害赔偿，作者均无须承担法律责任。作者不保证本软件所包含的资料,文字、图形、链接或其它事项的准确性或完整性。作者可随时更改本软件，无须另作通知。  <br />
                所有由用户自己制作、下载、使用的第三方信息数据和插件所引起的一切版权问题或纠纷，本软件概不承担任何责任。<br /><br />
                三、协议规定的约束和限制：  <br />
                禁止去除(苹果CMS)源码里的版权信息，商业授权版本可去除后台界面及前台界面的相关版权信息。</br>
                禁止在(苹果CMS)整体或任何部分基础上发展任何派生版本、修改版本或第三方版本用于重新分发。</br></br>
                <strong>版权所有 (c) 2020，苹果CMS,保留所有权利</strong>。',

    'install/user_agreement_agree'=>'同意协议并安装系统',
    'install/environment_title'=>'运行环境检测',
    'install/environment_name'=>'环境名称',
    'install/current_config'=>'当前配置',
    'install/required_config'=>'所需配置',

    'install/dir_file'=>'目录/文件',
    'install/required_popedom'=>'所需权限',
    'install/current_popedom'=>'当前权限',

    'install/func_ext'=>'函数/扩展',
    'install/type'=>'类型',
    'install/result'=>'结果',
    'install/back_step'=>'返回上一步',
    'install/next_step'=>'进行下一步',
    'install/question'=>'常见问题解决办法',
    'install/database_config'=>'数据库配置',

    'install/server_address'=>'服务器地址',
    'install/server_address_tip'=>'数据库服务器地址，一般为127.0.0.1',
    'install/database_port'=>'数据库端口',
    'install/database_port_tip'=>'系统数据库端口，一般为3306',
    'install/database_name'=>'数据库名称',
    'install/database_name_tip'=>'系统数据库名,必须包含字母',
    'install/database_username'=>'数据库账号',
    'install/database_username_tip'=>'连接数据库的用户名',
    'install/database_pass'=>'数据库密码',
    'install/database_pass_tip'=>'连接数据库的密码',
    'install/database_pre'=>'数据库前缀',
    'install/database_pre_tip'=>'建议使用默认,数据库前缀必须带_',
    'install/overwrite_database'=>'覆盖数据库',
    'install/overwrite'=>'覆盖',
    'install/not_overwrite'=>'不覆盖',
    'install/overwrite_tip'=>'如需保留原有数据，请选择不覆盖',
    'install/test_connect'=>'测试数据库连接',
    'install/test_connect_tip'=>'请先点击 【测试数据连接】 再安装',
    'install/other_config'=>'其他设置',
    'install/admin_name'=>'管理员账号',
    'install/admin_name_tip'=>'管理员账号最少4位',
    'install/admin_pass'=>'管理员密码',
    'install/admin_pass_tip'=>'保证密码最少6位',
    'install/init_data'=>'初始化数据',
    'install/create'=>'创建',
    'install/not_create'=>'不创建',
    'install/create_tip'=>'是否创建基础分类数据',
    'install/exec'=>'立即执行安装',
    'install/submit_tip'=>'请先点击并通过测试数据连接!',

    'install/environment_failed'=>'环境检测未通过，不能进行下一步操作！',
    'install/init_err'=>'初始失败！',
    'install/write_read_err'=>'无读写权限！',
    'install/not_found'=>'不存在',
    'install/database_connect_err'=>'数据库连接失败，请检查数据库配置！',
    'install/database_name_haved'=>'该数据库已存在，可直接安装。如需覆盖，请选择覆盖数据库！',
    'install/database_connect_ok'=>'数据库连接成功',
    'install/access_denied'=>'非法访问',
    'install/please_test_connect'=>'请先点击测试数据库连接！',
    'install/please_input_admin_name_pass'=>'请填写管理账号和密码！',
    'install/sql_err'=>'导入表结构SQL失败，请检查install.sql的语句是否正确。',
    'install/init_data_err'=>'导入初始化数据SQL失败，请检查initdata.sql的语句是否正确。',
    'install/admin_err'=>'管理员账号创建失败',
    'install/is_ok'=>'系统安装成功，欢迎您使用苹果CMS建站',
    'install/os'=>'操作系统',
    'install/php'=>'php版本',
    'install/gd'=>'GD库',

    'install/not_limited'=>'不限制',
    'install/not_installed'=>'未安装',
    'install/read_and_write'=>'读写',
    'install/not_writable'=>'不可写',
    'install/support'=>'支持',
    'install/not_support'=>'支持',
    'install/class'=>'类',
    'install/model'=>'模块',
    'install/function'=>'函数',
    'install/config'=>'配置',


    'validate/require_name'=>'名称必须',
    'validate/require_type'=>'分类必须',
    'validate/require_content'=>'内容必须',
    'validate/require_nick'=>'昵称必须',
    'validate/require_mid'=>'模型id必须',
    'validate/require_rid'=>'关联id必须',
    'validate/require_pass'=>'密码必须',
    'validate/require_url'=>'网址必须',
    'validate/require_actor'=>'演员必须',
    'validate/require_user'=>'用户必须',
    'validate/require_no'=>'卡号必须',
    'validate/require_name_min'=>'名称最少不能低于6个字符',
    'validate/require_money'=>'金额必须',
    'validate/require_points'=>'积分必须',
    'validate/require_bank_name'=>'银行名称必须',
    'validate/require_payee_name'=>'收款人姓名必须',
    'validate/require_bank_no'=>'银行账号必须',
    'validate/require_sourcecharset'=>'目标网址编码类型必须',
    'validate/require_sourcetype'=>'目前网址类型必须',
    'validate/require_urlpage'=>'目前网址必须',
    'validate/require_order_code'=>'单号必须',
    'validate/require_order_price'=>'价格必须',
    'validate/require_order_points'=>'点数必须',
    'validate/require_msg_to'=>'接收地址必须',
    'validate/require_verify'=>'验证码必须',
    'validate/require_path'=>'路径必须',
    'validate/require_tpl'=>'模板必须',
    'validate/require_ip'=>'ip必须',
    'validate/require_time'=>'时间必须',
];